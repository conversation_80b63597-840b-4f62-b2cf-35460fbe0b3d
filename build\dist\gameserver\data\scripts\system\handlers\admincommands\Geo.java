/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.configs.network.NetworkConfig;
import gameserver.geoEngine2.GeoEngine2;
import gameserver.geoEngine2.collision.CollisionResult;
import gameserver.geoEngine2.collision.CollisionResults;
import gameserver.geoEngine2.math.Vector3f;
import gameserver.geoEngine2.scene.Geometry;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;

/**
 * 
 * <AUTHOR>
 */
public class Geo extends AdminCommand {
    public Geo() {
        super("geo");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.GM_LEVEL && NetworkConfig.GAMESERVER_ID != 100) {
            PacketSendUtility.sendMessage(admin,
                "You don't have enough rights to execute this command.");
            return;
        }

        if (params.length < 1) {
            PacketSendUtility.sendMessage(admin,
                "Syntax: //geo <canSee | getZ | getBaseZ | stressTest | collision | surface>");
            return;
        }

        String option = params[0].toLowerCase();
        if ("cansee".startsWith(option)) {
            if (admin.getTarget() == null) {
                PacketSendUtility.sendMessage(admin, "Error! Please select a target.");
                return;
            }

            VisibleObject target = admin.getTarget();
            if (GeoEngine2.getInstance().canSee(admin, target)) {
                PacketSendUtility.sendMessage(admin, "Success! You can see your target.");
            }
            else {
                PacketSendUtility.sendMessage(admin, "Failure! You cannot see your target.");
            }
        }
        else if ("getz".startsWith(option)) {
            float fixZ = GeoEngine2.getInstance().getZ(admin);
            float bicubicZ = GeoEngine2.getInstance().getBicubicZ(admin.getWorldId(), admin.getX(),
                admin.getY());
            float bilinearZ = GeoEngine2.getInstance().getBilinearZ(admin.getWorldId(),
                admin.getX(), admin.getY());

            PacketSendUtility.sendMessage(admin, "Fixed Z: " + fixZ + " ("
                + admin.getMoveController().getOriginZ() + ") [bicubic: " + bicubicZ
                + "] [bilinear: " + bilinearZ + "]");
        }
        else if ("getbasez".startsWith(option)) {
            float baseZ = GeoEngine2.getInstance().getLowestZ(admin.getWorldId(), admin.getX(),
                admin.getY());
            PacketSendUtility.sendMessage(admin, "Base Z: " + baseZ + " ("
                + admin.getMoveController().getOriginZ() + ")");
        }
        else if ("collision".startsWith(option)) {
            if (admin.getTarget() == null) {
                PacketSendUtility.sendMessage(admin, "Error! Please select a target.");
                return;
            }

            VisibleObject target = admin.getTarget();

            // Vector3f coll = GeoEngine2.getInstance().getClosestCollision(admin, target.getX(),
            // target.getY(), target.getZ());
            CollisionResults results = GeoEngine2.getInstance().getCollisions(admin, target.getX(),
                target.getY(), target.getZ());
            PacketSendUtility.sendMessage(admin, "CollisionResults: size " + results.size());

            for (CollisionResult collision : results) {
                Vector3f coll = collision.getContactPoint();
                Geometry geom = collision.getGeometry();
                PacketSendUtility.sendMessage(admin,
                    "Collision (" + (geom != null ? geom.getName() : "null") + "): " + coll.getX()
                        + ", " + coll.getY() + ", " + coll.getZ());
            }
        }
        else if ("surface".startsWith(option)) {
            CollisionResult result = GeoEngine2.getInstance().getClosestCollisionResult(admin,
                admin.getX(), admin.getY(), 0);

            Vector3f normal = result.getContactNormal();
            Geometry geom = result.getGeometry();

            int terrainInfo = GeoEngine2.getInstance().getTerrainInfo(admin.getWorldId(),
                admin.getX(), admin.getY());

            if (normal == null) {
                PacketSendUtility.sendMessage(admin, "Error! TerrainInfo: " + terrainInfo);
                return;
            }

            PacketSendUtility.sendMessage(
                admin,
                "CollisionResult (" + (geom != null ? geom.getName() : "null") + "): ["
                    + normal.getX() + ", " + normal.getY() + ", " + normal.getZ()
                    + "] - terrainInfo: " + terrainInfo);
        }
        else if ("stresstest".startsWith(option)) {
            if (admin.getTarget() == null) {
                PacketSendUtility.sendMessage(admin, "Error! Please select a target.");
                return;
            }

            VisibleObject target = admin.getTarget();

            PacketSendUtility.sendMessage(admin, "Commencing stress test...");

            long testTotal = System.currentTimeMillis();

            long testGetZ = System.currentTimeMillis();
            for (int i = 0; i < 10000; i++) {
                GeoEngine2.getInstance().getZ(admin);
            }
            testGetZ = System.currentTimeMillis() - testGetZ;

            long testCanSee = System.currentTimeMillis();
            for (int i = 0; i < 10000; i++) {
                GeoEngine2.getInstance().canSee(admin, target);
            }
            testCanSee = System.currentTimeMillis() - testCanSee;

            long testClosest = System.currentTimeMillis();
            for (int i = 0; i < 10000; i++) {
                GeoEngine2.getInstance().getClosestCollision(admin, target.getX(), target.getY(),
                    target.getZ());
            }
            testClosest = System.currentTimeMillis() - testClosest;

            testTotal = System.currentTimeMillis() - testTotal;

            PacketSendUtility.sendMessage(admin, "Stress test finished! Results:\n"
                + "10000 * getZ: " + testGetZ + " ms\n" + "10000 * canSee: " + testCanSee + " ms\n"
                + "10000 * getClosestCollision: " + testClosest + " ms\n" + "Total time elapsed: "
                + testTotal + " ms");
        }
    }
}