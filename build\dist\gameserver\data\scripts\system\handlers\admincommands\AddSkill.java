/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.dataholders.DataManager;
import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.templates.npcskill.NpcSkillList;
import gameserver.model.templates.npcskill.NpcSkillTemplate;
import gameserver.services.ItemService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.utils.i18n.CustomMessageId;
import gameserver.utils.i18n.LanguageHandler;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AddSkill extends AdminCommand {

    public AddSkill() {
        super("addskill");
    }

    /*
     * (non-Javadoc)
     * @see
     * com.aionemu.gameserver.utils.chathandlers.AdminCommand#executeCommand(com.aionemu.gameserver.model.gameobjects
     * .player.Player, java.lang.String[])
     */

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_ADDSKILL) {
            PacketSendUtility.sendMessage(admin,
                LanguageHandler.translate(CustomMessageId.COMMAND_NOT_ENOUGH_RIGHTS));
            return;
        }

        VisibleObject target = admin.getTarget();

        if (params.length > 0 && "general".equalsIgnoreCase(params[0])) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //addskill general <ely | asmo>");
                return;
            }
            else if (!(target instanceof Player)) {
                PacketSendUtility.sendMessage(admin, "Your target must be a player!");
                return;
            }
            
            Player player = (Player) target;

            if ("ely".equalsIgnoreCase(params[1])) {
                Item item = ItemService.newItem(*********, 100);

                //item.setTemporary(true);

                player.getInventory().putToBag(item, true);
                ItemService.updateItem(player, item, true);
                
                int[] skillIds = new int[] { 11889, 11898, 11902, 11903, 11904, 11905, 11906 };
                
                for (int skillId : skillIds) {
                    player.getSkillList().addSkill(player, skillId, 1, true);
                }
                
                PacketSendUtility.sendMessage(admin, "Added Transformation skills/seeds to " + player.getName());
            }
            else if ("asmo".equalsIgnoreCase(params[1])) {
                Item item = ItemService.newItem(*********, 100);

                //item.setTemporary(true);

                player.getInventory().putToBag(item, true);
                ItemService.updateItem(player, item, true);
                
                int[] skillIds = new int[] { 11894, 11898, 11902, 11903, 11904, 11905, 11906 };
                
                for (int skillId : skillIds) {
                    player.getSkillList().addSkill(player, skillId, 1, true);
                }
                
                PacketSendUtility.sendMessage(admin, "Added Transformation skills/seeds to " + player.getName());
            }
            else {
                PacketSendUtility.sendMessage(admin, "Invalid race specified!");
            }

            return;
        }

        if (params.length != 2) {
            PacketSendUtility.sendMessage(admin,
                LanguageHandler.translate(CustomMessageId.COMMAND_ADDSKILL_SYNTAX));
            return;
        }

        int skillId = 0;
        int skillLevel = 0;

        try {
            skillId = Integer.parseInt(params[0]);
            skillLevel = Integer.parseInt(params[1]);
        }
        catch (NumberFormatException e) {
            PacketSendUtility.sendMessage(admin,
                LanguageHandler.translate(CustomMessageId.INTEGER_PARAMETER_REQUIRED));
            return;
        }

        if (DataManager.SKILL_DATA.getSkillTemplate(skillId) == null) {
            PacketSendUtility.sendMessage(admin, "The specified skill id " + skillId
                + " does not exist.");
            return;
        }

        if (target instanceof Player) {
            Player player = (Player) target;
            player.getSkillList().addSkill(player, skillId, skillLevel, true);
            PacketSendUtility.sendMessage(admin, LanguageHandler.translate(
                CustomMessageId.COMMAND_ADDSKILL_ADMIN_SUCCESS, skillId, player.getName()));
            PacketSendUtility.sendMessage(
                player,
                LanguageHandler.translate(CustomMessageId.COMMAND_ADDSKILL_PLAYER_SUCCESS,
                    admin.getName()));
        }
        else if (target instanceof Npc) {
            Npc npc = (Npc) target;

            NpcSkillTemplate template = new NpcSkillTemplate(0, skillId, skillLevel, 10, false);

            if (npc.getNpcSkillList() == null) {
                List<NpcSkillTemplate> skillList = new ArrayList<NpcSkillTemplate>();

                skillList.add(template);

                npc.setNpcSkillList(new NpcSkillList(npc.getNpcId(), 0, skillList));
            }
            else {
                npc.getNpcSkillList().getNpcSkills().add(template);
            }

            PacketSendUtility.sendMessage(admin,
                "Added skill " + skillId + " to NPC " + npc.getName());
        }
    }
}
