/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.ChatType;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_MESSAGE;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.Executor;
import gameserver.world.World;

/**
 * <AUTHOR> Ritsu
 *         <p/>
 *         Smart Matching Enabled //announce a message The above example will work for "anonymous" flag. //announce name
 *         message The above example will show "[GM] Name: message".
 */
public class AnnounceNew extends AdminCommand {
    public AnnounceNew() {
        super("a2");
    }

    @Override
    public int getSplitSize() {
        return 2;
    }

    @Override
    public void executeCommand(Player admin, String[] params) {

        if (admin.getAccessLevel() < AdminConfig.COMMAND_ANNOUNCE) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command.");
            return;
        }

        if (params == null || params.length < 1) {
            PacketSendUtility.sendMessage(admin, "Syntax: //a <message>");
            return;
        }

        String name = "";
        String message = "";

        name += admin.getTagName(true);

        for (int i = 0; i < params.length; i++) {
            if (params[i].startsWith("http"))
                message += Util.generateWebLink(params[i]) + " ";
            else
                message += params[i] + " ";
        }

        final String _name = name;
        final String _message = message;

        World.getInstance().doOnAllPlayers(new Executor<Player>() {
            @Override
            public boolean run(Player player) {
                PacketSendUtility.sendPacket(player, new SM_MESSAGE(0, _name, _message,
                    ChatType.GROUP_LEADER));
                return true;
            }
        });
    }
}
