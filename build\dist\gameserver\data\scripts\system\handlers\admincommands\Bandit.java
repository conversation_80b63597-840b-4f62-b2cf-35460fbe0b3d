/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_MOTION;
import gameserver.network.aion.serverpackets.SM_PLAYER_INFO;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.Executor;

/**
 * <AUTHOR>
 */
public class Bandit extends AdminCommand {
    public Bandit() {
        super("bandit");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_KILL) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command");
            return;
        }

        if (params.length == 1 && params[0].equalsIgnoreCase("map")) {
            admin.getWorldMapInstance().doOnAllPlayers(new Executor<Player>() {
                @Override
                public boolean run(Player pl) {
                    makeBandit(pl);

                    return true;
                }
            }, true);
        }
        else {
            VisibleObject target = admin.getTarget();

            if (target == null || !(target instanceof Player)) {
                PacketSendUtility.sendMessage(admin, "Please select a player!");
                return;
            }

            Player player = (Player) target;
            makeBandit(player);

            if (player.isBandit())
                PacketSendUtility.sendMessage(admin, player.getName() + " is now a Bandit!");
            else
                PacketSendUtility.sendMessage(admin, player.getName() + " is no longer Bandit!");
        }
    }

    private void makeBandit(Player player) {
        player.setBandit(!player.isBandit());

        player.clearKnownlist();
        PacketSendUtility.sendPacket(player, new SM_PLAYER_INFO(player, false));
        PacketSendUtility.sendPacket(player, new SM_MOTION(player));
        player.updateKnownlist();
        player.getEffectController().updatePlayerEffectIcons();
    }
}
