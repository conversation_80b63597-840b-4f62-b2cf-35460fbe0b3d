/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_TRANSFORM;
import gameserver.skillengine.effect.EffectTemplate;
import gameserver.skillengine.effect.TransformEffect;
import gameserver.skillengine.model.Effect;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

import java.util.Map;

import javolution.util.FastMap;

/**
 * 
 * <AUTHOR>
 */
public class MorphCommand extends UserCommand {
    private static Map<Integer, Long> lastExecute = new FastMap<Integer, Long>();

    public MorphCommand() {
        super("morph");
    }

    public void executeCommand(Player player, String param) {
        String[] params = param.split(" ");

        if (player.isInCombatLong()) {
            PacketSendUtility.sendMessage(player, "You cannot use this command while in combat!");
            return;
        }

        if (lastExecute.containsKey(player.getObjectId())) {
            if ((System.currentTimeMillis() - lastExecute.get(player.getObjectId())) < 20000) {
                PacketSendUtility.sendMessage(player,
                    "You cannot use this command more than every 20 seconds!");
                return;
            }
        }

        player.setInMorphCancel(!player.isInMorphCancel());

        player.setTransformedModelId(0);
        PacketSendUtility.broadcastPacketAndReceive(player, new SM_TRANSFORM(player));

        if (player.isInMorphCancel()) {
            PacketSendUtility
                .sendMessage(player,
                    "You have enabled morph-cancelling. Type .morph again for transforms to affect you.");
        }
        else {
            int newModel = 0;

            for (Effect tmp : player.getEffectController().getAbnormalEffects()) {
                for (EffectTemplate template : tmp.getEffectTemplates()) {
                    if (template instanceof TransformEffect) {
                        newModel = ((TransformEffect) template).getTransformId();
                        break;
                    }
                }
            }

            player.setTransformedModelId(newModel);

            PacketSendUtility.broadcastPacketAndReceive(player, new SM_TRANSFORM(player));

            PacketSendUtility.sendMessage(player, "You have disabled morph-cancelling.");
        }

        lastExecute.put(player.getObjectId(), System.currentTimeMillis());
    }
}