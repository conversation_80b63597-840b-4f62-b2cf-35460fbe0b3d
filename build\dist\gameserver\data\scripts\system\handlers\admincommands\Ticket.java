/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.TicketService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;

/**
 * <AUTHOR>
 * 
 */
public class Ticket extends AdminCommand {
    public Ticket() {
        super("ticket");
    }

    /*
     * (non-Javadoc)
     * @see gameserver.utils.chathandlers.AdminCommand#executeCommand(gameserver.model.gameobjects.player.Player,
     * java.lang.String[])
     */
    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_TICKET) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command!");
            return;
        }

        if (params.length < 1) {
            PacketSendUtility.sendMessage(admin, "Syntax: //ticket <accept | peek>"
                + "\nCurrent status: " + TicketService.getInstance().getTickets() + " tickets");
            return;
        }
        if (params[0].equals("accept")) {
            accept(admin);
        }
        else if (params[0].equals("peek")) {
            peek(admin);
        }
        else {
            PacketSendUtility.sendMessage(admin, "Syntax: //ticket <accept | peek>");
            return;
        }
    }

    public void accept(Player admin) {
        StringBuilder builder = new StringBuilder();
        gameserver.model.Ticket ticket = TicketService.getInstance().getTicket();

        if (ticket == null) {
            PacketSendUtility.sendMessage(admin, "There are no tickets available at the moment");
            return;
        }

        builder.append("===============\n");
        builder.append("From: " + ticket.getOwner().getName() + "\n");
        builder.append(ticket.getSummary() + "\n");

        int tickets = TicketService.getInstance().getTickets();
        if (tickets > 0)
            builder.append("(" + tickets + " more tickets)\n");

        builder.append("===============");

        PacketSendUtility.sendMessage(admin, builder.toString());
    }

    public void peek(Player admin) {
        StringBuilder builder = new StringBuilder();
        gameserver.model.Ticket ticket = TicketService.getInstance().peek();

        if (ticket == null) {
            PacketSendUtility.sendMessage(admin, "There are no tickets available at the moment");
            return;
        }

        builder.append("===============\n");
        builder.append("From: " + ticket.getOwner().getName() + "\n");
        builder.append(ticket.getSummary() + "\n");

        int tickets = TicketService.getInstance().getTickets();
        if (tickets > 1)
            builder.append("(" + (tickets - 1) + " more tickets)\n");

        builder.append("===============");

        PacketSendUtility.sendMessage(admin, builder.toString());
    }

}
