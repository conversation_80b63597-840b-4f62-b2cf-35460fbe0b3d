/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.model.gameobjects.player.Player;
import gameserver.services.LegionHallService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

/**
 * 
 * <AUTHOR>
 */
public class HallCommand extends UserCommand {
    public HallCommand() {
        super("hall");
    }

    public void executeCommand(Player player, String param) {
        String[] params = param.split(" ");

        if (!player.isSpawned())
            return;

        if (LegionHallService.getInstance().isLegionHall(player.getWorldMapInstance())) {
            LegionHallService.getInstance().leaveHall(player);
        }
        else {
            if (player.getLegion() != null) {
                if (player.getLegion().getLegionLevel() < LegionHallService.LEGION_HALL_LEVEL)
                    message(player, "Your legion needs to be level "
                        + LegionHallService.LEGION_HALL_LEVEL + " to use the Legion Hall!");
                else
                    LegionHallService.getInstance().enterHall(player, player.getLegion());
            }
            else {
                message(player, "You do not belong to any legion!");
            }
        }
    }

    private void message(Player player, String msg) {
        PacketSendUtility.sendMessage(player, msg);
    }
}