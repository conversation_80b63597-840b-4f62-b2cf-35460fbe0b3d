/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package usercommands;

import gameserver.model.gameobjects.player.Player;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.ChatHandler;
import gameserver.utils.chathandlers.ChatHandlers;
import gameserver.utils.chathandlers.CommandChatHandler;
import gameserver.utils.chathandlers.UserCommand;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * 
 */
public class Help extends UserCommand {
    private static Set<String> excempt = new HashSet<String>() {
        {
            add("s");
            add("help");
            //add("manor");
        }
    };

    public Help() {
        super("help");
    }

    /*
     * (non-Javadoc)
     * @see gameserver.utils.chathandlers.UserCommand#executeCommand(gameserver.model.gameobjects.player.Player,
     * java.lang.String)
     */
    @Override
    public void executeCommand(Player player, String params) {
        StringBuilder builder = new StringBuilder();

        builder.append("Available commands:\n");

        for (ChatHandler handler : ChatHandlers.getInstance().getHandlers()) {
            if (handler instanceof CommandChatHandler) {
                CommandChatHandler cHandler = (CommandChatHandler) handler;

                for (UserCommand cmd : cHandler.getUserCommands().values()) {
                    if (excempt.contains(cmd.getCommandName()))
                        continue;

                    builder.append("." + cmd.getCommandName());
                    builder.append(", ");
                }
            }
        }

        PacketSendUtility.sendMessage(player, builder.toString());
    }
}
