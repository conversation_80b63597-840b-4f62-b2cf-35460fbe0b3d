/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.model.gameobjects.player.Player;
import gameserver.services.LadderService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

/**
 * 
 * <AUTHOR>
 */
public class EventCommand extends UserCommand {
    public EventCommand() {
        super("event");
    }

    public void executeCommand(Player player, String param) {
        @SuppressWarnings("unused")
        String[] params = param.split(" ");

        if (player.isTemporary())
            return;

        if (!LadderService.getInstance().isEventReady()) {
            PacketSendUtility
                .sendMessage(player, "There is no currently no event to register for.");
            return;
        }

        if (LadderService.getInstance().isInQueue(player)) {
            LadderService.getInstance().unregisterFromQueue(player);
            PacketSendUtility
                .sendSys2Message(player, "BG", "You have unregistered from the queue.");
            return;
        }
        else {
            if (LadderService.getInstance().registerForEvent(player)) {
                PacketSendUtility.sendSys2Message(player, "BG",
                    "You have registered for the event! Please wait until it starts.");
                return;
            }
            else {
                PacketSendUtility.sendSys2Message(player, "BG",
                    "Failed to register you for the event!");
                return;
            }
        }
    }
}