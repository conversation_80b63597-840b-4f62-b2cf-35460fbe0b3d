<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:include schemaLocation="../stats/stats.xsd"/>
	<xs:include schemaLocation="../items/item_templates.xsd"/>
	<xs:include schemaLocation="../import.xsd"/>
	<xs:element name="npc_templates" type="npcTemplates"/>

	<xs:complexType name="npcTemplates">
		<xs:sequence>
			<xs:element ref="import" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="npc_template" type="npcTemplate" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="npcTemplate">
		<xs:sequence>
			<xs:element name="stats" type="npcStatsTemplate"/>
			<xs:element name="equipment" type="npcEquipmentList" minOccurs="0"/>
			<xs:element name="kisk_stats" type="KiskStats" minOccurs="0"/>
			<xs:element name="bound_radius" type="BoundRadius" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
		<xs:attribute name="npc_id" type="npcId" use="required"/>
		<xs:attribute name="level" type="xs:byte" use="required"/>
		<xs:attribute name="name_id" type="xs:int" use="required"/>
		<xs:attribute name="title_id" type="xs:int" default="0"/>
		<xs:attribute name="name" type="xs:string" default=""/>
		<xs:attribute name="height" type="xs:float" default="0"/>
		<xs:attribute name="talking_distance" type="xs:int" default="0"/>
		<xs:attribute name="ammo_speed" type="xs:int"/>
		<xs:attribute name="npc_type" type="npcType" use="required"/>
		<xs:attribute name="rank" type="NpcRank"/>
		<xs:attribute name="srange" type="xs:int" default="0"/>
		<xs:attribute name="tribe" type="xs:string"/>
		<xs:attribute name="ai" type="xs:string" default="dummy"/>
		<xs:attribute name="race" type="Race"/>
		<xs:attribute name="hpgauge" type="xs:int"/>
		<xs:attribute name="arange" type="xs:int"/>
		<xs:attribute name="arate" type="xs:int"/>
		<xs:attribute name="adelay" type="xs:int" default="2000"/>
		<xs:attribute name="state" type="xs:int"/>
		<xs:attribute name="stare_range" type="xs:int" default="10"/>
		<xs:attribute name="talk_delay" type="xs:int"/>
	</xs:complexType>

	<xs:complexType name="npcStatsTemplate">
		<xs:complexContent>
			<xs:extension base="statsTemplate">
				<xs:sequence/>
				<xs:attribute name="run_speed_fight" type="xs:float" use="required"/>
				<xs:attribute name="crit" type="xs:int" use="required"/>
				<xs:attribute name="pdef" type="xs:int" use="required"/>
				<xs:attribute name="mdef" type="xs:int" use="required"/>
				<xs:attribute name="maxXp" type="xs:int" use="required"/>
				<xs:attribute name="accuracy" type="xs:int" use="required"/>
				<xs:attribute name="power" type="xs:int" use="required"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>

	<xs:complexType name="npcEquipmentList">
		<xs:sequence>
			<xs:element name="item" type="itemId" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="KiskStats">
		<xs:sequence/>
		<xs:attribute name="usemask" type="xs:int" use="required"/>
		<xs:attribute name="members" type="xs:int" use="required"/>
		<xs:attribute name="resurrects" type="xs:int" use="required"/>
	</xs:complexType>

	<xs:simpleType name="npcType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="GUARD_INSTANCE"/>
			<xs:enumeration value="ATTACKABLE"/>
			<xs:enumeration value="AGGRESSIVE"/>
			<xs:enumeration value="POSTBOX"/>
			<xs:enumeration value="RESURRECT"/>
			<xs:enumeration value="USEITEM"/>
			<xs:enumeration value="PORTAL"/>
			<xs:enumeration value="CHEST"/>
			<xs:enumeration value="NON_ATTACKABLE"/>
			<xs:enumeration value="ARTIFACT"/>
			<xs:enumeration value="ARTIFACT_PROTECTOR"/>
			<xs:enumeration value="ASSEMBLED_NPC"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="NpcRank">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NORMAL"/>
			<xs:enumeration value="ELITE"/>
			<xs:enumeration value="JUNK"/>
			<xs:enumeration value="HERO"/>
			<xs:enumeration value="LEGENDARY"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="npcId">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="200000"/>
			<xs:maxInclusive value="1000000"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
