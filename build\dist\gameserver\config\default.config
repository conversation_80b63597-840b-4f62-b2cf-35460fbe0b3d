# Aion X Emu Configuration File
#
# To load server on this config file, please use command :
#
# java -Xms128m -Xmx1536m -ea -Xbootclasspath/p:./libs/jsr166.jar -cp ./libs/*:ax-game-1.0.1.jar gameserver.GameServer /path/to/configuration/file.config > log/console.log &


# ===================================
#             DATABASE
# ===================================

# JDBC driver to use
database.driver = com.mysql.jdbc.Driver

# JDBC database URL
database.url = ***********************************************************************************************

# Database user
database.user = root

# Database password
database.password = Azer$tyuio^p789

# JDBC Connection Pool minimum size (concurrent connections)
database.connections.min = 5

# JDBC Connection Pool maximum size
database.connections.max = 10

# DAO Scripts context file
database.scriptcontext.descriptor = ./data/scripts/system/database.xml

# ===================================
#              NETWORK
# ===================================

# GameServer ID
gameserver.network.login.gsid = 1

# GameServer bind IP address (* for all available IP)
gameserver.network.client.host = *

# GameServer bind TCP port
gameserver.network.client.port = 7777

# RDC (Remote Data Call) bind IP address
gameserver.network.rdc.client.host = *

# RDC bind TCP port
gameserver.network.rdc.client.port = 732

# Online players limit
gameserver.network.client.maxplayers = 1000

# Required access level to enter gameserver
# If set to bigger than 0 only GM's are able to connect
# and normal players can't see server on serverlist
# Default = 0
gameserver.network.client.requiredlevel = 0

# LoginServer location
gameserver.network.login.address = localhost:9014

# LoginServer password for this GameServer
gameserver.network.login.password = Azer$tyuio^p789

# ChatServer location
gameserver.network.chat.address = localhost:9021

# ChatServer password for this ChatServer
gameserver.network.chat.password = Azer$tyuio^p789

# Enable debug information from network layer
gameserver.network.nio.debug = false

# Additional threads that are used to read network data
gameserver.network.nio.threads.read = 1

# Number of retries to do when reading a packet
gameserver.network.nio.threads.read.retries = 8

# Additional threads that are used to write network data
gameserver.network.nio.threads.write = 1

# Number of retries to do when writing a packet
gameserver.network.nio.threads.write.retries = 8

# Enable additional threads (workers) to handle data processing
gameserver.network.nio.threads.workers.enable = false

# Number of workers
gameserver.network.nio.threads.workers = 1

# Number of buffers by worker
gameserver.network.nio.threads.workers.buffers = 16

# Display/hide unknown packets received by server
gameserver.network.display.unknownpackets = true

# ===================================
#            GAMESERVER
# ===================================

# GameServer Name
gameserver.name = Harboes Aion Server

# Pattern for player names validation
# To enable UTF support for names - set this parameter to [a-zA-Z\u0410-\u042f\u0430-\u044f]{2,16}
gameserver.character.name.pattern = [a-zA-Z\u0410-\u042f\u0430-\u044f]{2,20}

# Server Country Code (cc)
gameserver.country.code = 2

# Server Version
gameserver.version = 4.10

# Server Mode
# 1  = user can create characters from both factions
# 34 = user is bound to a faction, can't create more than 2 characters and can't enter the game world (character reservation mode)
# 128  = user is bound to a faction (can't create characters from both factions at the same time)
gameserver.mode = 128

# Disable chat server connection
gameserver.disable.chatserver = false

# Disable RDC server
gameserver.disable.rdcserver = true

# Display the server revision in player MOTD
# SHALL BE DISABLED ON UNIX SERVERS
gameserver.motd.revision.display = false

# Enable ratios limitation
gameserver.factions.ratio.limited = false

# When a faction ratio reach this value, it's not possible to create new characters of this faction
gameserver.factions.ratio.value = 50

# Minimum character level to take into account in ratio calculation
gameserver.factions.ratio.level = 10

# Minimum characters count in each faction needed before limiting ratios
gameserver.factions.ratio.minimum = 50

# Enable server freefly
gameserver.enable.freefly = false

# Log chat?
gameserver.log.chat = false

# Log item?
gameserver.log.item = false

# Language
gameserver.lang = "en"

# ===================================
#              RATES
# ===================================

# Display server rates when player enter in world
gameserver.rate.display.rates = true

# Regular experience rate for group members
gameserver.rate.regular.group.xp = 20
gameserver.rate.premium.group.xp = 30
gameserver.rate.vip.group.xp = 40

# Experience rate for single players
gameserver.rate.regular.xp = 20
gameserver.rate.premium.xp = 30
gameserver.rate.vip.xp = 40

# Item drop rate
gameserver.rate.regular.drop = 20
gameserver.rate.premium.drop = 30
gameserver.rate.vip.drop = 40

# Gathering XP rates
gameserver.rate.regular.gathering.xp = 20
gameserver.rate.premium.gathering.xp = 30
gameserver.rate.vip.gathering.xp = 40

# Crafting XP rates
gameserver.rate.regular.crafting.xp = 20
gameserver.rate.premium.crafting.xp = 30
gameserver.rate.vip.crafting.xp = 40

# Quest XP rates
gameserver.rate.regular.quest.xp = 20
gameserver.rate.premium.quest.xp = 30
gameserver.rate.vip.quest.xp = 40

# Quest Kinah rate
gameserver.rate.regular.quest.kinah = 20
gameserver.rate.premium.quest.kinah = 30
gameserver.rate.vip.quest.kinah = 40

# Abyss points rate (can be 0.5 etc.)
# For PvE
gameserver.rate.regular.ap.npc = 1
gameserver.rate.premium.ap.npc = 1
gameserver.rate.vip.ap.npc = 1

# Abyss points rate (can be 0.5 etc.)
# For PvP
gameserver.rate.regular.ap.player = 1
gameserver.rate.regular.ap.lost_player = 1
gameserver.rate.premium.ap.player = 1
gameserver.rate.premium.ap.lost_player = 1
gameserver.rate.vip.ap.player = 1
gameserver.rate.vip.ap.lost_player = 1

# Kinah rate
gameserver.rate.regular.kinah = 20
gameserver.rate.premium.kinah = 30
gameserver.rate.vip.kinah = 40

# DP rate for members
gameserver.rate.regular.dp = 1
gameserver.rate.premium.dp = 2
gameserver.rate.vip.dp = 3

# DP rate for PvP members
gameserver.rate.regular.pvp.dp = 1
gameserver.rate.premium.pvp.dp = 2
gameserver.rate.vip.pvp.dp = 3

# DP rate for group members
gameserver.rate.regular.group.dp = 1
gameserver.rate.premium.group.dp = 2
gameserver.rate.vip.group.dp = 3

# ===================================
#              CACHE
# ===================================

# This parameter says what kind of cache should be used
# in CacheMap that is used for caching many things.
#
# Setting this value to true means that cache will be using
# soft references - what means, that objects will be in memory as long as possible
# and would be removed when there is little of memory.
#
# Setting this value to false mans that cache will be using
# weak references - what means, that objects will be in memory as long as they are
# strong achievable to
# Default: false
gameserver.cache.softcache = false

# If true then whole Player objects (with inventory etc) are cached as long
# as there is memory for them
# Default: false
gameserver.cache.players = false

# If true then whole PlayerCommonData are cached as long
# as there is memory for them
# Default: false
gameserver.cache.pcd = false

# If true then Account objects are cached as long as there is memory for them
# Default: false
gameserver.cache.accounts = false

# ===================================
#             CUSTOMS
# ===================================

# Speaking mode between factions
# 0  = default, factions can't speak together
# 1  = all players can speak to all others
# Default: 0
gameserver.factions.speaking.mode = 0

# Searching players of opposite faction
# false : factions can't search each other
# true: factions can search each other
# Default: false
gameserver.factions.search.mode = false

# Skill auto learn mode
# true = no skill books are required
# false = need skill books to learn lvl1 skill
# Default: false
gameserver.skill.autolearn = true

# Stigma skills auto learn mode
# true = no stigma stones are required
# false = need stigma stones to learn this skills
# Default: false
gameserver.stigma.autolearn = false

# Stigma slots Antihack checking.
# quest = player quests are properly checked.
# level = player levels are properly checked.
# none = nothing checked maximum slots used.
gameserver.stigma.antihack = level

# Advanced Stigma slots Antihack checking.
# quest = player quests are properly checked.
# level = player levels are properly checked.
# none = nothing checked maximum slots used.
gameserver.advstigma.antihack = level

# Retail like character deletion times
# true = it takes 7 days to delete a character that is level 20+
# false = characters get deleted in 5 minutes
# Default: true
gameserver.character.delete.retail = false

# Disable aggressive behavior from monsters
# Default: false
gameserver.disable.mob.aggro = false

# Enable 2nd class change simple mode
# Default: false
gameserver.enable.simple.2ndclass = true

# Unstuck delay in seconds
# Default: 3600 (1 hour)
gameserver.unstuck.delay = 3600

# Enable instances (temporary config till instances will be mature)
# Default: true
gameserver.instances.enable = true

# Default Fly Time
# Default: 60 (60 seconds)
gameserver.base.flytime = 60

# Cross-Faction Binding
# Allows opposite factions to bind in enemy territories
# Default: false
gameserver.cross.faction.binding = false

# Disable drop rate reduction based on level diference between players and mobs
# Default: false
gameserver.disable.drop.reduction = true

# Maximum number of pvp kills on one player before receiving 1AP per kill in a time period.
# Default: 5
gameserver.pvp.maxkills = 2

# Time period in hours
# Default: 24
gameserver.pvp.period = 3

# Enable .ely, .asmo, .both chat channels
# Default: false
gameserver.channels.all.enabled = false

# Enable .world chat channel
# Default: false
gameserver.channels.world.enabled = false

# Show all connected players from both factions in research window
gameserver.search.listall = false

# Enable/Disable instance cooldown
gameserver.instance.cooldown = true

# Announce for rare drops
# Shown message only for players that are situated on the same faction and location.
# Default: false
gameserver.announce.raredrops = true

# Kick players using speed hack
# Default: true
gameserver.kick.speedhack.enable = true

# Ping minimun Interval to consider speed hack
# Default: 100000
gameserver.kick.speedhack.pinginterval = 100000

# Chain Trigger Rate
# true = Chain Trigger Rate is enabled
# false = Chain Trigger Rate is disabled(all chain skill 100% success)
# Default: true
gameserver.skill.chain.triggerrate = true

# Criticall Crafting Rates
# default: 25
gameserver.custum.craft.crit = 25

# Surveys
# true = Surveys is enabled and you can integrate with Web Shops(Players receive items on logon).
# false = Surveys is disabled.
# Default: true
gameserver.enable.surveys = true

# Enable the rewards for pvp kills
# Default: false
# (Reset all_kill from abyss_rank table before activate it)
gameserver.pvpreward.enable = false

# Set the kills needed to get a reward (do NOT set config to 0)
# Default: 5
gameserver.pvpreward.kills.needed1 = 5

# Default: 10
gameserver.pvpreward.kills.needed2 = 10

# Default: 15
gameserver.pvpreward.kills.needed3 = 15

# Select the item reward for pvp kills
# Default: 186000031 (Silver Medal)
gameserver.pvpreward.item.reward1 = 186000031

# Default: 186000030 (Golden Medal)
gameserver.pvpreward.item.reward2 = 186000030

# Default: 186000096 (Platinum Medal)
gameserver.pvpreward.item.reward3 = 186000096

# Minimum level to use search.
# Default: 10
search.level.restriction = 10

# Minimum level to use whisper.
# Default: 10
whisper.level.restriction = 10

# Allow players to control their experience gain with xpon / xpoff commands
# Default: false
gameserver.player.experience.control = false

# Time in seconds which character stays online
# after closing client window
# Default: 10
gameserver.disconnect.time = 10

# ----------------------------
# Extra GameServer Configurations
# ----------------------------
# Minimul level to remodel an item
# Default: 20
gameserver.itemremodel.minlevel = 20

# Multiplier for teleportation and transport price
# Set 0 for free teleport (can be 0.5)
# Default: 1
gameserver.transport.price.multiplier = 1

# Multiplier for soul healing price (can be 0.5)
# Default: 1
gameserver.soulhealing.price.multiplier = 1

# Minimum level for using rift.
# Default: 25
gameserver.rift.minimum.level = 25

# Remove the requirement of title for instances and portals
# Default: true
gameserver.portal.requirement.title = true

# Remove the requirement of race for instances and portals
# Default: true
gameserver.portal.requirement.race = true

# Remove the requirement of level for instances and portals
# Default: true
gameserver.portal.requirement.level = true

# ----------------------------
# Dark Poeta Settings
# ----------------------------

# Point reward rate multiplier (can be 0.5 or 1.5)
# Set 4 for rate x4
# Default: 1
gameserver.darkpoeta.reward.point.rate = 1

# Grades settings
# Time in seconds

# Grade S
# Default time: 7200
# Default points: 20000
gameserver.darkpoeta.grade.S.time = 7200
gameserver.darkpoeta.grade.S.points = 20000

# Grade A
# Default time: 5400
# Default points: 17100
gameserver.darkpoeta.grade.A.time = 5400
gameserver.darkpoeta.grade.A.points = 17100

# Grade B
# Default time: 3600
# Default points: 13100
gameserver.darkpoeta.grade.B.time = 3600
gameserver.darkpoeta.grade.B.points = 13100

# Grade C
# Default time: 1800
# Default points: 11000
gameserver.darkpoeta.grade.C.time = 1800
gameserver.darkpoeta.grade.C.points = 11000

# ===================================
#             DREDGION
# ===================================

# Dredgion Time
# Default: 120 (2 hrs)
gameserver.dredgion.timer = 120

# Dredgion Min. Level
gameserver.dredgion.levellimit = 45

# Dredgion Player
# Default = 12
gameserver.dredgion.player = 12

# Dredgion Group Limit
# Default  player = 12 , grouplimit = 6 ::: 12/2
gameserver.dredgion.grouplimit = 6

# Dredgion AP Winner
# Default = 3000
gameserver.dredgion.winreward = 3000

# Dredgion AP Lost
# Default = 1500
gameserver.dredgion.lostreward = 1500

# ----------------------------
# Character Passkey
# ----------------------------
# Enable or Disable Character Passkey
# Default: true
passkey.enable = false

# Enter the maximum number of incorrect password set
# Default: 5 (retail server value)
passkey.wrong.maxcount = 5

# ----------------------------
# GeoData config
# ----------------------------
# Enable Geodata (true/false)�
gameserver.geodata.enable=true

# ----------------------------
# HTML Welcome Message
# ----------------------------

# Enable or Disable HTML Welcome Message
# To Edit this file, go to /data/static_data/HTML
# and open welcome.html
# remember to edit the messages just after <![CDATA[ tag
enable.html.welcome = false

# ----------------------------
# HTML Config
# ----------------------------

# Default: ./data/static_data/HTML/
html.root = ./data/static_data/HTML/

# Default: ./cache/html.cache
html.cache.file = ./cache/html.cache

# Default: UTF-8
html.encoding = UTF-8

# ----------------------------
# Top Ranking
# ----------------------------
# Time at what top ranking is updated.
# Default(�� ���������): 0:00:00
gameserver.topranking.time = 0:00:00

# Delay between two updates in hours
# Default(�� ���������): 24
gameserver.topranking.delay = 2

# ----------------------------
# Enable send to the prison (artmoney hack)
# ----------------------------
# Set timer from prison
# default: true
gameserver.artmoney.hack = true

# ----------------------------
# Send to the prison (artmoney hack)
# ----------------------------
# Set timer from prison
# default: 120 minutes
gameserver.artmoney.hackbuy.time = 120

# ===================================
#           ENCHANTMENTS
# ===================================

# Default: 97
gameserver.manastone.percent = 97

# Default: 83
gameserver.manastone.percent1 = 83

# Default: 73
gameserver.manastone.percent2 = 73

# Default: 65
gameserver.manastone.percent3 = 65

# Default: 59
gameserver.manastone.percent4 = 59

# Default: 42
gameserver.manastone.percent5 = 42

# Supplement Additional Success Rates
# Default: 10
gameserver.supplement.lesser = 10

# Default: 15
gameserver.supplement.regular = 15

# Default: 20
gameserver.supplement.greater = 20

# ===================================
#           FALL_DAMAGE
# ===================================

# Enable fall damage
gameserver.fall.damage.active = true

# The damage percentage per meter.
gameserver.fall.damage.percentage = 1.0

# Minimum fall height to receive damage.
gameserver.fall.damage.distance.minimum = 10

# Maximum fall height on which you will die when you hit the ground.
gameserver.fall.damage.distance.maximum = 50

# Maximum fall height after which you will die in mid air.
gameserver.fall.damage.distance.midair = 200

# ===================================
#             GROUPS
# ===================================

# Time in seconds after a group member is removed after he/she disconnected
gameserver.playergroup.removetime = 600

# Maximum distance in meters between killed monster and party member to receive XP
gameserver.playergroup.maxdistance = 100

# Players 10 levels below highest member should get 0 exp
# Set a higher number to allow power leveling
# Default: 10
gameserver.playergroup.maxlevel.difference = 10

# Allow other race invitation in group
# Default: false
gameserver.playergroup.invite.other.race = false

# Time in seconds after a alliance member is removed after he/she disconnected
gameserver.playeralliance.removetime = 600

# Allow other race invitation in alliance
# Default: false
gameserver.playeralliance.invite.other.race = false


# ===================================
#             LEGIONS
# ===================================

# Character patterns
gameserver.legion.pattern = [a-zA-Z\u0410-\u042f\u0430-\u044f 123456789]{2,16}
gameserver.legion.selfintropattern = [a-zA-Z\u0410-\u042f\u0430-\u044f 123456789]{2,25}
gameserver.legion.nicknamepattern = [a-zA-Z\u0410-\u042f\u0430-\u044f 123456789]{2,10}
gameserver.legion.announcementpattern = [A-Za-z0-9 \-\\[\\]\<\>\(\)\!\?\?\|\@\#\$\%\^\&\*\+\=\/\,\.\'\"\;\:\_\{\}\~\`\u0410-\u042f\u0430-\u044f]{2,120}

# Disband settings
gameserver.legion.disbandtime = 86400
gameserver.legion.disbanddifference = 604800

# Required Kinah to create a legion
gameserver.legion.creationrequiredkinah = 10000

# Legion Emblem settings
gameserver.legion.emblemrequiredkinah = 10000

# Legion level up settings
gameserver.legion.level2requiredkinah = 100000
gameserver.legion.level3requiredkinah = 1000000
gameserver.legion.level4requiredkinah = 2000000
gameserver.legion.level5requiredkinah = 6000000
gameserver.legion.level2requiredmembers = 10
gameserver.legion.level3requiredmembers = 20
gameserver.legion.level4requiredmembers = 30
gameserver.legion.level5requiredmembers = 40
gameserver.legion.level2requiredcontribution = 0
gameserver.legion.level3requiredcontribution = 20000
gameserver.legion.level4requiredcontribution = 100000
gameserver.legion.level5requiredcontribution = 500000

# Legion member settings
gameserver.legion.level1maxmembers = 30
gameserver.legion.level2maxmembers = 60
gameserver.legion.level3maxmembers = 90
gameserver.legion.level4maxmembers = 120
gameserver.legion.level5maxmembers = 150

# Legion functions
gameserver.legion.warehouse = true
gameserver.legion.inviteotherfaction = false

# Time in seconds for updating legion ranking
gameserver.legion.ranking.periodicupdate = 1200

# ===================================
#           NPC_MOVEMENTS
# ===================================

# Enable NPC movement
gameserver.npc.movement.active = false

# The minimum time in seconds that the NPC waits before moving again.
gameserver.npc.movement.delay.minimum = 3

# The maximum time in seconds that the NPC waits before moving again.
gameserver.npc.movement.delay.maximum = 15

# ===================================
#           PERIODIC_SAVE
# ===================================

# WARNING:
# With 500 player online saving can be up to 10 seconds
# (it depends on hardware, changes in item locations, new acquisitions etc)
# Generally accepted interval is 15-20 minutes

# Time in seconds for saving player data (player, abyss rank, quests, skills)
gameserver.periodicsave.player.general = 900

# Time in seconds for saving player items and item stones
gameserver.periodicsave.player.items = 900

# Time in seconds for saving legion wh items and item stones
gameserver.periodicsave.legion.items = 1200

# Time in seconds for saving broker
gameserver.periodicsave.broker = 1500

# ===================================
#              PRICES
# ===================================

# Controls the "Prices:" value in influence tab.
gameserver.prices.default.prices = 100

# Hidden modifier for all prices.
gameserver.prices.default.modifier = 100

# Taxes: value = 100 + tax %
gameserver.prices.default.taxes = 100

gameserver.prices.vendor.buymod = 100

gameserver.prices.vendor.sellmod = 20

# ===================================
#             SHUTDOWN
# ===================================

# Shutdown Settings.
# NOTE: This settings works only if shutdown is used from console.
# Shutdown Hook Mode. 1 = Shutdown, 2 = Restart.
gameserver.shutdown.mode = 1

# Shutdown Hook delay.
gameserver.shutdown.delay = 20

# Shutdown announce interval.
gameserver.shutdown.interval = 1

# This is a safe reboot mode.
# All action is disabled in shutdown progress.
gameserver.shutdown.safereboot = true

# ===================================
#              SIEGE
# ===================================

# Enable/Disable Siege Engine
gameserver.siege.enabled = true

# Siege Timer Interval in seconds
gameserver.siege.interval = 600

# Chance for Invulnerable fortress to switch to Vulnerable
gameserver.siege.vulnerable.chance = 75

# Points Per Fortress
gameserver.siege.influence.fortress = 10

# Points Per Artifact
gameserver.siege.influence.artifact = 1

# Custom Abyss point reward when general is killed for default forteress
# Default: 0
# Advised: 9000
gameserver.siege.apreward.default = 0

# Custom Abyss point reward when general is killed for divine forteress
# Default: 0
# Advised: 40000
gameserver.siege.apreward.divine = 0

# ===================================
#           TASK_MANAGER
# ===================================

# Enable/disable deadlock detector (true/false)
gameserver.deadlock.enable = false

# Interval for deadlock detector run schedule (seconds)
gameserver.deadlock.interval = 300

# ===================================
#             THREADS
# ===================================

thread.basepoolsize = 2
thread.threadpercore = 4

# Default: 5000
thread.runtime = 5000

# ===================================
#             ADMINS
# ===================================

# GM Level.
gameserver.administration.gmlevel = 2

# Display or not gm tags for different levels of admins/gms
gameserver.gmtag.display = true

# Different tags for gms according to AccessLevel
# Tag for Access Level 1
# Default: <HELPER>
gameserver.gmtag.level1 = <HELPER>

# Tag for Access Level 2
# Default: <GM>
gameserver.gmtag.level2 = <GM>

# Tag for Access Level 3
# Default: <HEADGM>
gameserver.gmtag.level3 = <HEADGM>

# Tag for Access Level 4
# Default: <ADMIN>
gameserver.gmtag.level4 = <ADMIN>

# Tag for Access Level 5
# Default: <HEADADMIN>
gameserver.gmtag.level5 = <HEADADMIN>

# Announce on GM connection
# GM name is announced to players on login
gameserver.announce.gm.connection = false

# Invisible on GM connection
# GM login invisible
gameserver.invis.gm.connection = false

# Invulnerable on GM connection
# GM login invulnerable
gameserver.invul.gm.connection = true

# Silent on GM connection
# GM login in whisper refusal mode
gameserver.silence.gm.connection = false

# Speed Up on GM connection
# Gm logins with setted speed stats value
gameserver.speed.gm.connection = 0

# Unlimited Flight Time for GMs
gameserver.administration.flight.unlimited = 3

# Free Flight Everywhere for GMs
gameserver.administration.flight.freefly = 3

# Shields hurt players with this access level and below
gameserver.administration.shield.vulnerable = 0

# ===================================
#          ADMIN_COMMANDS
# ===================================

# Character Passkey Reset
administration.command.passkeyreset = 3

# Various AI-related tasks
gameserver.administration.command.ai = 3

# Adds an item to your inventory
gameserver.administration.command.add = 3

# Adds an item set to your inventory
gameserver.administration.command.addset = 3

# Adds drop entry
gameserver.administration.command.adddrop = 3

# Add target player skill
gameserver.administration.command.addskill = 3

# Add title to player
gameserver.administration.command.addtitle = 3

# This command send fakeserverpackets to the server for test porpouses, Used by Developers
gameserver.administration.command.advsendfakeserverpacket = 3

# The message is sent to all players chatbox
gameserver.administration.command.announce = 3

# Announce for only one faction (asmo/ely)
gameserver.administration.command.announce_faction = 3

# Announcements
gameserver.administration.command.announcements = 3

# All ban and unban commands (//ban, //unban, //banip, //unbanip)
gameserver.administration.command.ban = 3

# Bookmark command
gameserver.administration.command.bk = 3

# Configures properties on runtime
gameserver.administration.command.configure = 3

# Self explanatory
gameserver.administration.command.deletespawn = 3

# Command to remove all buff effect of the player
gameserver.administration.command.dispel = 3

# Command to manage dredgion instance
gameserver.administration.command.dredgion = 3

# Dye a player
gameserver.administration.command.dye = 3

# Gag and ungag
gameserver.administration.command.gag = 3

# Teleport youself to specified place name
# Example: //goto poeta
gameserver.administration.command.goto = 3

# Give missin skills to self
gameserver.administration.command.givemissingskills = 3

# Restores target hp/mp/dp
gameserver.administration.command.heal = 3

# Print target info
gameserver.administration.command.info = 3

# Make you invis/vis
gameserver.administration.command.invis = 3

# Switch to Invulnerable / Vulnerable
gameserver.administration.command.invul = 3

# Disconnect the player
gameserver.administration.command.kick = 3

# Kill target
gameserver.administration.command.kill = 3

# Add Kinah to self or target/named player
gameserver.administration.command.kinah = 3

# Legion command allows you to disband/setlevel/setpoints of a legion
gameserver.administration.command.legion = 3

# Show all connected players from both factions when opening research window
gameserver.administration.search.listall = 3

# Morph command
gameserver.administration.command.morph = 3

# Move player1 to player2 location
gameserver.administration.command.moveplayertoplayer = 3

# Teleport to specified XYZ coordinates
gameserver.administration.command.moveto = 3

# Teleport to specified NPC
gameserver.administration.command.movetonpc = 3

# Move player to my position
gameserver.administration.command.movetome = 3

# Teleport you to player position
gameserver.administration.command.movetoplayer = 3

# Command to watch cutscenes. Used by quest writers
gameserver.administration.command.movie = 3

# Make yourself appear neutral to both factions
gameserver.administration.command.neutral = 3

# The message is sent to all players chatbox and a message appear in center screen
gameserver.administration.command.notice = 3

# Who is allowed to handle petitions
gameserver.administration.command.petition = 3

# Playerinfo command
gameserver.administration.command.playerinfo = 3

# Prison command
gameserver.administration.command.prison = 3

# Give admin rights to player
gameserver.administration.command.promote = 3

# Quest steps control (admin/GM)
gameserver.administration.command.questcommand = 3

# Restart quest (player) default = 3 (turned off)
gameserver.administration.command.questcommandplayers = 3

# Command to remove all buff effect of the player
gameserver.administration.command.recall = 3

# Reload all command
gameserver.administration.command.reload = 3

# Reload all spawn data from files
gameserver.administration.command.reloadspawns = 3

# Remove command allow remove an item from player
gameserver.administration.command.remove = 3

# Resurrect a target player
gameserver.administration.command.resurrect = 3

# Revoke admin rights to player
gameserver.administration.command.revoke = 3

# Command to add fly rings
gameserver.administration.command.ring = 3

# Save all spawn data to files (Directory to save GameServer/data/static_data/spawns/new)
gameserver.administration.command.savespawndata = 3

# Speak as player or a NPC
gameserver.administration.command.say = 3

# See the droplsit of target mob
gameserver.administration.command.seedroplist = 3

# This commands sends packets to the server for test porpouses, Used by Developers
gameserver.administration.command.sendfakeserverpacket = 3
gameserver.administration.command.sendrawpacket = 3

# Set target player ap
gameserver.administration.command.setap = 3

# Set target player class
gameserver.administration.command.setclass = 3

# Set target player experience amount
gameserver.administration.command.setexp = 3

# Sets target player level
gameserver.administration.command.setlevel = 3

# Set title target player title
gameserver.administration.command.settitle = 3

# Modify current siege values
gameserver.administration.command.siege = 3

# Switch to not whisperable / whisperable
gameserver.administration.command.silence = 3

# Self explanatory
gameserver.administration.command.spawn = 3

# Temporary increases speed of character
gameserver.administration.command.speed = 3
gameserver.administration.command.speed.maxvalue = 1000

# Show gamestats about selected target
gameserver.administration.command.stat = 3

# System commands
gameserver.administration.command.system = 3

# Self explanatory
gameserver.administration.command.unloadspawn = 3

# Unstuck player
gameserver.administration.command.unstuck = 3

# Change weather of a region a reset weather in the world
gameserver.administration.command.weather = 3

# Ban / Unban players from .world / .ely / .asmo channels
gameserver.administration.command.worldban = 3

# Prints zone info (//zone) or refreshes currect zone (//zone refresh)
gameserver.administration.command.zone = 3

# Returns how many players are online
gameserver.administration.command.online = 3

# Command to fix Z coordinate of mob
gameserver.administration.command.fixz = 3

# Command to fix heading of mob
gameserver.administration.command.fixh = 3

# Rename named player to new specified and valid name
gameserver.administration.command.rename = 3

# HTML commands
gameserver.administration.command.html = 3

# Returns how many players are online and nicks
gameserver.administration.command.who = 3

# Make yourself appear as enemy to both factions and/or npcs
gameserver.administration.command.enemy = 3

#Enchant command
gameserver.administration.command.enchant = 3

#Socket command
gameserver.administration.command.socket = 3

#PowerUp command
gameserver.administration.command.powerup = 3

#Godstone command
gameserver.administration.command.godstone = 3

#Gmlist command
gameserver.administration.command.gmlist = 0

#Appearance command
gameserver.administration.command.appearance = 3

#See command
gameserver.administration.command.see = 3

#Lounge command
gameserver.administration.command.lounge = 1

#Enter group instance without group
gameserver.administration.instancenogroup = 3

# see all commands GM ingame
gameserver.administration.command.admin = 1

gameserver.player.maxlevel = 65

gameserver.player.startinglevel = 65