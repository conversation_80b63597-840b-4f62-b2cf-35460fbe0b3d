<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" version="1.0">
	<!-- Base types -->
	<xs:simpleType name="playerClass">
		<xs:restriction base="xs:string">
			<xs:enumeration value="WARRIOR"/>
			<xs:enumeration value="GLADIATOR"/>
			<xs:enumeration value="TEMPLAR"/>
			<xs:enumeration value="SCOUT"/>
			<xs:enumeration value="ASSASSIN"/>
			<xs:enumeration value="RANGER"/>
			<xs:enumeration value="MAGE"/>
			<xs:enumeration value="SORCERER"/>
			<xs:enumeration value="SPIRIT_MASTER"/>
			<xs:enumeration value="PRIEST"/>
			<xs:enumeration value="CLERIC"/>
			<xs:enumeration value="CHANTER"/>
			<xs:enumeration value="ENGINEER"/>
			<xs:enumeration value="RIDER"/>
			<xs:enumeration value="GUNNER"/>
			<xs:enumeration value="ARTIST"/>
			<xs:enumeration value="BARD"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="startingPlayerClass">
		<xs:restriction base="xs:string">
			<xs:enumeration value="WARRIOR"/>
			<xs:enumeration value="SCOUT"/>
			<xs:enumeration value="MAGE"/>
			<xs:enumeration value="PRIEST"/>
			<xs:enumeration value="ENGINEER"/>
			<xs:enumeration value="ARTIST"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Race">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ELYOS"/>
			<xs:enumeration value="ASMODIANS"/>
			<xs:enumeration value="LYCAN"/>
			<xs:enumeration value="CONSTRUCT"/>
			<xs:enumeration value="CARRIER"/>
			<xs:enumeration value="DRAKAN"/>
			<xs:enumeration value="LIZARDMAN"/>
			<xs:enumeration value="TELEPORTER"/>
			<xs:enumeration value="NAGA"/>
			<xs:enumeration value="BROWNIE"/>
			<xs:enumeration value="KRALL"/>
			<xs:enumeration value="SHULACK"/>
			<xs:enumeration value="BARRIER"/>
			<xs:enumeration value="PC_LIGHT_CASTLE_DOOR"/>
			<xs:enumeration value="PC_DARK_CASTLE_DOOR"/>
			<xs:enumeration value="DRAGON_CASTLE_DOOR"/>
			<xs:enumeration value="GCHIEF_LIGHT"/>
			<xs:enumeration value="GCHIEF_DARK"/>
			<xs:enumeration value="DRAGON"/>
			<xs:enumeration value="OUTSIDER"/>
			<xs:enumeration value="RATMAN"/>
			<xs:enumeration value="DEMIHUMANOID"/>
			<xs:enumeration value="UNDEAD"/>
			<xs:enumeration value="BEAST"/>
			<xs:enumeration value="MAGICALMONSTER"/>
			<xs:enumeration value="ELEMENTAL"/>
			<xs:enumeration value="NONE"/>
			<xs:enumeration value="PC_ALL"/>
			<xs:enumeration value="GOBLIN"/>
			<xs:enumeration value="GENERAL"/>
			<xs:enumeration value="NPC"/>
			<xs:enumeration value="LIVINGWATER"/>
			<xs:enumeration value="TRICODARK"/>
			<xs:enumeration value="NEUT"/>
			<xs:enumeration value="GHENCHMAN_LIGHT"/>
			<xs:enumeration value="GHENCHMAN_DARK"/>
			<xs:enumeration value="SIEGEDRAKAN"/>
			<xs:enumeration value="ORC"/>
			<xs:enumeration value="DRAGONET"/>
			<xs:enumeration value="GCHIEF_DRAGON"/>
			<xs:enumeration value="EVENT_TOWER_LIGHT"/>
			<xs:enumeration value="EVENT_TOWER_DARK"/>
			<xs:enumeration value="WORLD_EVENT_DEFTOWER"/>
			<xs:enumeration value="WORLD_EVENT_BONFIRE"/>
			<xs:enumeration value="ALL"/>
			<xs:enumeration value="DOORKILLER"/>
			<xs:enumeration value="LF5_Q_ITEM"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:complexType name="BoundRadius">
		<xs:attribute name="front" type="xs:float"/>
		<xs:attribute name="side" type="xs:float"/>
		<xs:attribute name="upper" type="xs:float"/>
	</xs:complexType>
</xs:schema>
