<?xml version="1.0" encoding="UTF-8"?>
<cube_expander xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="cube_expander.xsd">
    <!-- Instructions
         min_level and max_level reflects current cube expansion level. By default it is 0 (not yet expanded)
         price01 - is the price to expand to level 1 from level 0
         ..
         price09 - is the price to expand to level 9 from level 8

         The difference (max_level - min_level + 1) should be equal to number of prices specified
     -->
    <!-- Poeta -->
    <cube_npc id="798008" name="Baevrunerk">
        <expand level="1" price="1000"/>
    </cube_npc>
    <!-- Ishalgen -->
    <cube_npc id="798037" name="Bacorerk">
        <expand level="1" price="1000"/>
    </cube_npc>
    <!-- Pandaemonium -->
    <cube_npc id="798058" name="Ondarinerk">
        <expand level="1" price="1000"/>
        <expand level="2" price="12000"/>
        <expand level="3" price="80000"/>
        <expand level="4" price="180000"/>
    </cube_npc>
    <cube_npc id="798059" name="Nekorunerk">
        <expand level="1" price="1000"/>
        <expand level="2" price="12000"/>
        <expand level="3" price="80000"/>
        <expand level="4" price="180000"/>
    </cube_npc>
    <!-- Sanctum -->
    <cube_npc id="798011" name="Heerunerk">
        <expand level="1" price="1000"/>
        <expand level="2" price="12000"/>
        <expand level="3" price="80000"/>
        <expand level="4" price="180000"/>
    </cube_npc>
    <cube_npc id="798012" name="Yiehmonerk">
        <expand level="1" price="1000"/>
        <expand level="2" price="12000"/>
        <expand level="3" price="80000"/>
        <expand level="4" price="180000"/>
    </cube_npc>
    <!-- Abyss, Tigraki Island -->
    <cube_npc id="279022" name="Jarumonerk">
        <expand level="1" price="1000"/>
        <expand level="2" price="12000"/>
        <expand level="3" price="80000"/>
        <expand level="4" price="180000"/>
        <expand level="5" price="350000"/>
    </cube_npc>
</cube_expander>