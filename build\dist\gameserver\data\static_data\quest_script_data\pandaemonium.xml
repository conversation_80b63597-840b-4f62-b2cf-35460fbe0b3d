<?xml version="1.0" encoding="utf-8"?>
<quest_scripts>
<!--
  This file is part of Aion X Emu <aionxemu.com>.

	This is free software: you can redistribute it and/or modify
	it under the terms of the GNU Lesser Public License as published by
	the Free Software Foundation, either version 3 of the License, or
	(at your option) any later version.

	This software is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY; without even the implied warranty of
	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
	GNU Lesser Public License for more details.

	You should have received a copy of the GNU Lesser Public License
	along with this software.  If not, see <http://www.gnu.org/licenses/>.
-->

	<!-- 2900: No Escaping Destiny handled by script -->
	<!-- [<PERSON>] <PERSON>'s Mistake -->
	<item_collecting id="2905" start_npc_id="204104" />
	<!-- [Craft] Ingots for Armor -->
	<item_collecting id="2906" start_npc_id="204106" />
	<!-- [Craft] Cooking Commitment -->
	<item_collecting id="2907" start_npc_id="204100" />
	<!-- [Craft] The Governor's Chair -->
	<item_collecting id="2908" start_npc_id="204108" />
	<!-- [Craft] The Basics of Alchemy -->
	<item_collecting id="2909" start_npc_id="204102" />
	<!-- [Craft] Wasted Materials -->
	<item_collecting id="2910" start_npc_id="204110" />
	<!-- 2911: Song of Blessing handled by script -->
	<!-- 2912: Follow the Ribbon handled by script -->
	<!-- 2913: A Chain of Debt handled by script -->
	<!-- 2914: A Token of Lost Love handled by script -->
	<!-- 2915: TODO: Helping Apellbine -->
	<!-- 2916: TODO: Man in the Long Black Robe -->
	<!-- 2917: Arekedil's Heritage handled by script -->
	<!-- 2918: Deep Maternal Love handled by script -->
	<!-- 2919: TODO: Book of Oblivion -->
	<!-- 2920: TODO: Elementary, My Dear Daeva -->
	<!-- 2921: TODO: Love at First Sight -->
	<!-- 2922: TODO: Fascinating Gift -->
	<!-- 2923: A Rose Will Bloom -->
	<item_collecting id="2923" start_npc_id="798058" />
	<!-- 2924: Siel's Tears -->
	<item_collecting id="2924" start_npc_id="204108" />
	<!-- 2925: TODO: A Heartfelt Confession -->
	<!-- Heart in Love -->
	<item_collecting id="2926" start_npc_id="204261" />
	<!-- [Spy/Group] A Many-Splendored Ring -->
	<item_collecting id="2927" start_npc_id="204261" />
	<!-- 2928: Power of Love handled by script -->
	<!-- 2931: [Expert] Weaponsmithing Expert -->
	<report_to start_npc_id="204104" end_npc_id="204052" id="2931" item_id="182207020" />
	<!-- 2932: [Expert] Armorsmithing Expert -->
      <report_to start_npc_id="204106" end_npc_id="204052" id="2932" item_id="182207021" />
	<!-- 2933: [Expert] Handicrafting Expert -->
      <report_to start_npc_id="204108" end_npc_id="204052" id="2933" item_id="182207022" />	
	<!-- 2934: [Expert] Cooking Expert -->
	<report_to start_npc_id="204100" end_npc_id="204052" id="2934" item_id="182207023" />
	<!-- 2935: [Expert] Alchemy Expert -->
	<report_to start_npc_id="204102" end_npc_id="204052" id="2935" item_id="182207024" />
	<!-- 2936: [Expert] Tailoring Expert -->
	<report_to start_npc_id="204110" end_npc_id="204052" id="2936" item_id="182207025" />
	<!-- Unexpected Reward -->
	<report_to id="2937" start_npc_id="204092" end_npc_id="798059" />
	<!-- 2938: TODO: Secret Library Access -->
	<!-- 2939: Weapon for Fraiban -->
	<report_to id="2939" start_npc_id="204104" end_npc_id="278010" item_id="182207027" />
	<!-- 2940: Kinterun's Package -->
	<report_to id="2940" start_npc_id="204106" end_npc_id="278011" item_id="182207028" />
	<!-- Salix Staff Handle -->
	<report_to id="2941" start_npc_id="204108" end_npc_id="278010" item_id="182207029" />
	<!-- Fried Chikra -->
	<report_to id="2942" start_npc_id="204100" end_npc_id="278007" item_id="182207030" />
	<!-- Homebody Honir -->
	<report_to id="2943" start_npc_id="204102" end_npc_id="278010" item_id="182207032" />
	<!-- Cloth for Bacht -->
	<report_to id="2944" start_npc_id="204110" end_npc_id="278011" item_id="182207032" />
	<!-- 2948: TODO: Huron's Letter -->
	<!-- Just Dye, Already -->
	<item_collecting id="2949" start_npc_id="204121" />
	<!-- Materials for 100-slot Cube -->
	<item_collecting id="2950" start_npc_id="798059" />
	<!-- Looking for Garkbinerk -->
	<report_to id="2951" start_npc_id="798059" end_npc_id="279006" />
	<!-- Winning Vindachinerk's Favor -->
	<item_collecting id="2952" start_npc_id="279006" end_npc_id="279016" />
	<!-- 2953: Delivering Supply Request handled by script -->
	<!-- 2954: Delivering Odella Juice handled by script -->
	<!-- A Feast for a Son -->
	<report_to id="2955" start_npc_id="204242" end_npc_id="204127" />
	<!-- Preparing the Banquet -->
	<item_collecting id="2956" start_npc_id="204127" />
	<!-- 2957: TODO: Flowers for the Banquet -->
	<!-- 2958: TODO: Last Minute Worries -->
	<!-- You Never Call, You Never Write -->
	<report_to id="2959" start_npc_id="204211" end_npc_id="204164" />
	<!-- Peace Offering -->
	<item_collecting id="2960" start_npc_id="204164" />
	<!-- A Secret Request -->
	<report_to id="2961" start_npc_id="204055" end_npc_id="204253" />
	<!-- 2962: TODO: Jafnhar's Whereabouts -->
	<!-- 2963: TODO: On Behalf of a Friend -->
	<!-- 2964: TODO: Striking at Shadows -->
	<!-- 2965: Ancient Weapons handled by script -->
	<!-- 2966: A Sword Gone Astray handled by script -->
	<!-- 2967: A Helmet Gone Astray handled by script -->
	<!-- 2968: A Staff Gone Astray handled by script -->
	<!-- 2969: Combat Rations handled by script -->
	<!-- 2970: Combat Potions handled by script -->
	<!-- 2971: A Robe Gone Astray handled by script -->
	<!-- [Expert] Weaponsmith's Test -->
	<item_collecting id="2972" start_npc_id="204104" />
	<!-- [Expert] Weaponsmith's Final Exam -->
	<item_collecting id="2973" start_npc_id="204104" />
	<!-- [Expert] Armorsmith's Test -->
	<item_collecting id="2974" start_npc_id="204106" />
	<!-- [Expert] Armorsmith's Final Exam -->
	<item_collecting id="2975" start_npc_id="204106" />
	<!-- [Expert] Handicrafter's Test -->
	<item_collecting id="2976" start_npc_id="204108" />
	<!-- [Expert] Handicrafter's Final Exam -->
	<item_collecting id="2977" start_npc_id="204108" />
	<!-- [Expert] Cook's Test -->
	<item_collecting id="2978" start_npc_id="204100" />
	<!-- [Expert] Cook's Final Exam -->
	<item_collecting id="2979" start_npc_id="204100" />
	<!-- [Expert] Alchemist's Test -->
	<item_collecting id="2980" start_npc_id="204102" />
	<!-- [Expert] Alchemist's Final Exam -->
	<item_collecting id="2981" start_npc_id="204102" />
	<!-- [Expert] Tailor's Test -->
	<item_collecting id="2982" start_npc_id="204110" />
	<!-- [Expert] Tailor's Final Exam -->
	<item_collecting id="2983" start_npc_id="204110" />
	<!-- Ingredients for Dye -->
	<report_to id="2984" start_npc_id="204138" end_npc_id="204121" item_id="182207064" />
	<!-- 2985: An Expert's Reward -->
	<report_to id="2985" start_npc_id="204052" end_npc_id="204072" />
	<!-- 4201: TODO: [Group] Sandblossom Wine -->
	<!-- [Group] Request of a Wine Lover -->
	<item_collecting id="4202" start_npc_id="204173" action_item_id="700523" />
	<!-- [Group] Anikiki's Taming Manual -->
	<item_collecting id="4203" start_npc_id="204283" />
	<!-- 4204: Deliver the Taming Manual -->
	<report_to id="4204" start_npc_id="204283" end_npc_id="279008" />
	<!-- [Group] Hat of the Steel Beard Pirates -->
	<item_collecting id="4206" start_npc_id="204285" />
	<!-- [Group] Log of the Steel Rake -->
	<item_collecting id="4207" start_npc_id="204284" />
	<!-- 4209: TODO: [Group] Rendezvous with the Spy -->
	<!-- 4210: TODO: [Group] Missing Haorunerk -->
	<!-- 4212: TODO: [Group] Missing Sidrunerk -->
	<!-- [Group] Unlucky Nekai -->
	<monster_hunt id="4213" start_npc_id="204202">
		<monster_infos var_id="0" max_kill="4" npc_id="215032" />
		<monster_infos var_id="1" max_kill="4" npc_id="215038" />
	</monster_hunt>
	<!-- [Group] Black Cloud Ledgers -->
	<item_collecting id="4214" start_npc_id="204284" />
	<!-- [Group] The Medicine Thief -->
	<item_collecting id="4215" start_npc_id="204072" />
	<!-- [Group] A Present for Father -->
	<item_collecting id="4216" start_npc_id="204282" />
	<!-- 4218: TODO: [Group] Mysterious Amabille -->
	<!-- 4901: Kalsten's Recommendation -->
	<report_to id="4901" start_npc_id="204080" end_npc_id="204056" item_id="182207067" />
	<!-- Thialfi's Recommendation -->
	<report_to id="4902" start_npc_id="204081" end_npc_id="204057" item_id="182207068" />
	<!-- Ve's Recommendation -->
	<report_to id="4903" start_npc_id="204082" end_npc_id="204058" item_id="182207069" />
	<!-- 4904: Lyfjaberga's Recommendation -->
	<report_to id="4904" start_npc_id="204083" end_npc_id="204059" item_id="182207070" />
	<!-- 4905: TODO: Interviewing the Veterans -->
	<!-- 4906: TODO: Tales of Heroes -->
	<!-- [Spy/Group] Lepharists in Elysea -->
	<item_collecting id="4907" start_npc_id="204208" action_item_id="700511" />
	<!-- 4908: TODO: Shugo Express Mail -->
	<!-- [Group] 3 Fragments of Agrif's Rage -->
	<item_collecting id="4909" start_npc_id="798317" />
	<!-- [Group] 2 Fragments of Agrif's Rage -->
	<item_collecting id="4910" start_npc_id="798317" />
	<!-- [Group] Hilt of Agrif's Rage -->
	<item_collecting id="4911" start_npc_id="798317" />
	<!-- 4912: TODO: [Group] The Curse of Agrif's Rage -->
	<!-- A Future Threat -->
	<report_to id="4913" start_npc_id="204182" end_npc_id="279027" />
	<!-- 4914: TODO: Lifeform Remodeling Report -->
	<!-- [Group] Obtaining the Dragel -->
	<item_collecting id="4915" start_npc_id="203385" />
	<!-- [Group] Acquiring the Drana -->
	<item_collecting id="4916" start_npc_id="203385" />
	<!-- 4917: Information Collector Chopirunerk -->
	<report_to id="4917" start_npc_id="203385" end_npc_id="798358" />
	<!-- [Group] Gathering the Surkana -->
	<item_collecting id="4918" start_npc_id="798358" />
	<!-- [Group] Acquiring the Drazma -->
	<item_collecting id="4919" start_npc_id="798358" />
	<!-- 4920: TODO: [Group] Making the Activated Surkana -->
	<!-- 4921: Chopirunerk's Reward -->
	<report_to start_npc_id="798358" end_npc_id="279022" item_id="182207128" id="4921" />
	<!-- 4922: The Gladiator Preceptor's Test handled by script -->
	<!-- 4923: The Templar Preceptor's Test handled by script -->
	<!-- 4924: The Ranger Preceptor's Test handled by script -->
	<!-- 4925: The Assassin Preceptor's Test handled by script -->
	<!-- 4926: The Sorcerer Preceptor's Test handled by script -->
	<!-- 4927: The Spiritmaster Preceptor's Test handled by script -->
	<!-- 4928: The Cleric Preceptor's Test handled by script -->
	<!-- 4929: The Chanter Preceptor's Test handled by script -->
	<!-- 4930: TODO: Advice of Destiny -->
	<!-- 4931: TODO: Enlightenment of Destiny -->
	<!-- 4932: TODO: Teachings of Destiny -->
	<!-- 4933: TODO: Intention of Lord Marchutan -->
	<!-- [Top Expert] Weaponsmithing Zeal -->
	<item_collecting id="4945" start_npc_id="204104" />
	<!-- [Top Expert] Weaponsmith's Skill -->
	<item_collecting id="4946" start_npc_id="204104" />
	<!-- 4947: [Top Expert] Weaponsmithing Expert -->
	<report_to id="4947" start_npc_id="204104" end_npc_id="204052" item_id="182207129" />
	<!-- [Top Expert] Armorsmithing Zeal -->
	<item_collecting id="4948" start_npc_id="204106" />
	<!-- [Top Expert] Armorsmith's Skill -->
	<item_collecting id="4949" start_npc_id="204106" />
	<!-- 4950: [Top Expert] Armorsmithing Expert -->
	<report_to id="4950" start_npc_id="204106" end_npc_id="204052" item_id="182207130" />
	<!-- [Top Expert] Handicrafting Zeal -->
	<item_collecting id="4951" start_npc_id="204108" />
	<!-- [Top Expert] Handicrafter's Skill -->
	<item_collecting id="4952" start_npc_id="204108" />
	<!-- 4953: [Top Expert] Handicrafting Expert -->
	<report_to id="4953" start_npc_id="204108" end_npc_id="204052" item_id="182207131" />
	<!-- [Top Expert] Cooking Zeal -->
	<item_collecting id="4954" start_npc_id="204100" />
	<!-- [Top Expert] Cook's Skill -->
	<item_collecting id="4955" start_npc_id="204100" />
	<!-- 4956: [Top Expert] Cooking Expert -->
	<report_to id="4956" start_npc_id="204100" end_npc_id="204052" item_id="182207132" />
	<!-- [Top Expert] Alchemy Zeal -->
	<item_collecting id="4957" start_npc_id="204102" />
	<!-- [Top Expert] Alchemist's Skill -->
	<item_collecting id="4958" start_npc_id="204102" />
	<!-- [Top Expert] Alchemy Expert -->
	<report_to id="4959" start_npc_id="204102" end_npc_id="204052" item_id="182207133" />
	<!-- [Top Expert] Tailoring Zeal -->
	<item_collecting id="4960" start_npc_id="204110" />
	<!-- [Top Expert] Tailor's Skill -->
	<item_collecting id="4961" start_npc_id="204110" />
	<!-- [Top Expert] Tailoring Expert -->
	<report_to id="4962" start_npc_id="204110" end_npc_id="204052" item_id="182207134" />
	<!-- 4963: TODO: Epilogue -->
	<!-- Payrinrinerk's Information -->
	<report_to id="4964" start_npc_id="204286" end_npc_id="204051" />
	<!-- 4965: TODO: Secretive Communication -->
	<report_to id="4965" start_npc_id="204182" end_npc_id="204207" item_id="182207135" />
	<!-- 4966: TODO: [Growth] Ninis's First Charm -->
	<!-- 4967: TODO: [Growth] Ninis's Second Charm -->
	<!-- 4968: TODO: [Growth] Ninis's Third Charm -->
	<!-- 4969: TODO: [Growth] Ninis's Fourth Charm -->
	<!-- 4970: TODO: The Fashionistas -->
	<!-- 4971: TODO: Project Runway -->
	<!-- 4972: TODO: Judge Not -->
	<!-- 4973: TODO: Marra's Worry -->
	<!-- 4974: TODO: The Secret of His Success -->
	<!-- 4975: Moorinerk's Offer -->
	<item_collecting id="4975" start_npc_id="204283" />
	<!-- 4976: TODO: A Settler's Ambition -->
	<!-- 4977: [Coin/Group] Drana Gluttony -->
	<monster_hunt start_npc_id="203385" id="4977">
		<monster_infos var_id="0" npc_id="214880" max_kill="8"/>
		<monster_infos var_id="0" npc_id="215388" max_kill="8"/>
		<monster_infos var_id="0" npc_id="215389" max_kill="8"/>
	</monster_hunt>
	<!-- 4978: [Coin/Group] Safecracking -->
	<monster_hunt start_npc_id="204286" id="4978">
		<monster_infos var_id="0" npc_id="215079" max_kill="1"/>
	</monster_hunt>
	<!-- 4979: [Coin/Group] In Shugo's Clothing -->
	<monster_hunt start_npc_id="204283" id="4979">
		<monster_infos var_id="0" npc_id="215081" max_kill="1"/>
	</monster_hunt>
	<!-- [Event] The Lost Contract -->
	<item_collecting id="9551" start_npc_id="204209" />
	<!-- [Event] Runaway Invitations -->
	<item_collecting id="9552" start_npc_id="204209" />
	<!-- 29000: TODO: [Expert] Essencetapper's Test -->
	<!-- 29001: TODO: [Expert] Essencetapping Expert -->
	<!-- 29002: TODO: [Expert] Aethertapper's Test -->
	<!-- 29003: TODO: [Expert] Aethertapping Expert -->
	<!-- 29004: Veldina's Call handled by script -->
	<!-- 29008: TODO: [Master] Weaponsmith's Potential -->
	<!-- 29009: TODO: [Master] Weaponsmithing Master -->
	<!-- 29014: TODO: [Master] Armorsmith's Potential -->
	<!-- 29015: TODO: [Master] Armorsmithing Master -->
	<!-- 29020: TODO: [Master] Tailor's Potential -->
	<!-- 29021: TODO: [Master] Tailoring Master -->
	<!-- 29026: TODO: [Master] Handicrafter's Potential -->
	<!-- 29027: TODO: [Master] Handicrafting Master -->
	<!-- 29032: TODO: [Master] Alchemist's Potential -->
	<!-- 29033: TODO: [Master] Alchemy Master -->
	<!-- 29038: TODO: [Master] Cook's Potential -->
	<!-- 29039: TODO: [Master] Cooking Master -->
	<!-- 29040: TODO: Mane's Best Friend -->
	<!-- 29043: [Daily] Flower Power -->
	<item_collecting id="29043" start_npc_id="798445" />
</quest_scripts>