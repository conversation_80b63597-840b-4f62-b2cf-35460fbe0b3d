<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:include schemaLocation="../import.xsd"/>
    <xs:element name="mount_data">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="mount" type="MountTemplate" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="MountTemplate">
		<xs:sequence>
			<xs:element name="mount_stats" type="MountStats" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="bound_radius" type="BoundRadius" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="id" type="xs:int" use="required"/>
		<xs:attribute name="name" type="xs:string" use="required"/>
		<xs:attribute name="type" type="xs:int" use="required"/>
		<xs:attribute name="itemId" type="xs:string" use="optional"/>
	</xs:complexType>
	<xs:complexType name="MountStats">
		<xs:attribute name="run_speed" type="xs:float" use="required"/>
		<xs:attribute name="fly_speed" type="xs:float" use="required"/>
		<xs:attribute name="fp_start" type="xs:int" use="required"/>
		<xs:attribute name="fp_cost" type="xs:int" use="required"/>
		<xs:attribute name="can_spring" type="xs:int" use="required"/>
		<xs:attribute name="spring_speed" type="xs:float" use="required"/>
	</xs:complexType>
</xs:schema>