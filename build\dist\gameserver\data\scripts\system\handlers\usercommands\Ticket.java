/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package usercommands;

import gameserver.model.gameobjects.player.Player;
import gameserver.services.TicketService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

/**
 * <AUTHOR>
 * 
 */
public class Ticket extends UserCommand {
    public Ticket() {
        super("ticket");
    }

    /*
     * (non-Javadoc)
     * @see gameserver.utils.chathandlers.UserCommand#executeCommand(gameserver.model.gameobjects.player.Player,
     * java.lang.String)
     */
    @Override
    public void executeCommand(Player player, String params) {
        if (params.length() == 0) {
            PacketSendUtility.sendMessage(player,
                "Syntax: .ticket \"Short description here\" -- will notify GM's of your issue");
            return;
        }
        if (TicketService.getInstance().hasTicket(player)) {
            PacketSendUtility.sendMessage(player, "You already have an open ticket!");
            return;
        }

        gameserver.model.Ticket ticket = new gameserver.model.Ticket(player, params, "");
        TicketService.getInstance().addTicket(ticket);
        PacketSendUtility.sendMessage(player, "Your ticket was created successfully.");
    }

}
