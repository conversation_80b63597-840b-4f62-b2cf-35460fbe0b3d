/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.alliance.PlayerAllianceMember;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.pvpevents.Battleground;
import gameserver.services.LadderService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.Executor;
import gameserver.world.World;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * 
 */
public class Tournament extends AdminCommand {
    private boolean starting = false;
    private Battleground bg;
    private List<List<Player>> teams;

    public Tournament() {
        super("tournament");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.GM_LEVEL) {
            message(admin, "You dont have enough rights to execute this command.");
            return;
        }

        if (params.length < 1) {
            message(admin, "Syntax: //tournament <create | add | remove | start | cancel | status>");
            return;
        }

        if ("create".startsWith(params[0].toLowerCase())) {
            if (starting) {
                message(admin, "A tournament has already been created.");
                return;
            }

            if (params.length < 2) {
                StringBuilder sb = new StringBuilder();
                sb.append("Aliases:");
                for (String a : Battleground.getAliases().keySet())
                    sb.append(" " + a + ",");

                message(admin,
                    "Syntax: //tournament create <battleground name> -- class name or alias");
                message(admin, sb.toString());

                return;
            }

            if (createBattleground(admin, params[1])) {
                teams = new ArrayList<List<Player>>();
                starting = true;
                bg.setIsTournament(true);
                announce("The " + bg.getName()
                    + " battleground will be the home of a Tournament - prepare yourselves!");
            }
            else {
                message(admin, "Please specify a valid Battleground name.");
                return;
            }
        }
        else if ("add".startsWith(params[0].toLowerCase())) {
            if (!starting) {
                message(admin, "No tournament has been created.");
                return;
            }

            if (params.length < 2) {
                message(admin,
                    "Syntax: //tournament add <name> - all members of the player's group will be added");
                return;
            }

            Player player = World.getInstance().findPlayer(Util.convertName(params[1]));
            if (player == null) {
                message(admin, "Please enter the name of an online player.");
                return;
            }

            boolean alreadyIn = false;
            for (Iterator<List<Player>> it = teams.iterator(); it.hasNext();) {
                for (Player pl : it.next()) {
                    if (pl.getName().equals(Util.convertName(params[1]))) {
                        alreadyIn = true;
                        break;
                    }
                }
            }

            if (alreadyIn) {
                message(admin, "The group of " + params[0] + " is already added.");
                return;
            }

            List<Player> players;
            if (player.isInGroup()) {
                players = new ArrayList<Player>(player.getPlayerGroup().getMembers());
            }
            else if (player.isInAlliance()) {
                players = new ArrayList<Player>();
                for (PlayerAllianceMember pla : player.getPlayerAlliance().getMembers())
                    if (pla.isOnline())
                        players.add(pla.getPlayer());
            }
            else {
                players = new ArrayList<Player>();
                players.add(player);
            }

            teams.add(players);

            if (players.size() > 1)
                message(admin, "All members of " + player.getName() + "'s group have been added.");
            else
                message(admin, player.getName()
                    + " does not belong to any group or alliance and has been added solo!");
        }
        else if ("remove".startsWith(params[0].toLowerCase())) {
            if (!starting) {
                message(admin, "No tournament has been created.");
                return;
            }

            if (params.length < 2) {
                message(admin,
                    "Syntax: //tournament remove <name> -- removes the group belonging to the player");
            }

            boolean success = false;
            for (Iterator<List<Player>> it = teams.iterator(); it.hasNext();) {
                for (Player pl : it.next()) {
                    if (pl.getName().equals(Util.convertName(params[1]))) {
                        success = true;
                        it.remove();
                        break;
                    }
                }
            }

            if (success) {
                message(admin, params[1] + "'s group has been removed.");
                return;
            }
            else {
                message(admin, "No player with the name " + params[1]
                    + " is in any registered group.");
                return;
            }
        }
        else if ("start".startsWith(params[0].toLowerCase())) {
            if (bg.createTournament(teams)) {
                LadderService.getInstance().registerBg(bg);
                announce("The tournament has been started! If you are not participating, go observe it with .spectate join "
                    + bg.getBgId());

                this.bg = null;
                this.starting = false;
                this.teams = null;

                return;
            }
            else {
                message(
                    admin,
                    "The Tournament could NOT be started! Check that you have added enough teams and that the BG supports Tournament mode!");
                return;
            }
        }
        else if ("cancel".startsWith(params[0].toLowerCase())) {
            if (!starting) {
                message(admin, "There is no Tournament to cancel.");
                return;
            }

            starting = false;
            bg = null;
            teams.clear();

            announce("The Tournament has been cancelled.");
        }
        else if ("status".startsWith(params[0].toLowerCase())) {
            if (!starting) {
                message(admin, "No tournament has been created.");
                return;
            }

            String msg = "----- TOURNAMENT STATUS -----\n";
            for (List<Player> team : teams) {
                msg += "TEAM: ";
                for (Player pl : team)
                    msg += pl.getName() + ", ";
                msg.substring(0, msg.length() - 2);
                msg += "\n";
            }

            message(admin, msg);
        }
    }

    private boolean createBattleground(Player admin, String name) {
        try {
            if (Battleground.getAliases().containsKey(name.toLowerCase()))
                bg = (Battleground) Battleground.getAliases().get(name.toLowerCase()).newInstance();
            else
                bg = (Battleground) getClass().getClassLoader()
                    .loadClass("gameserver.model.pvpevents." + name).newInstance();
        }
        catch (ClassNotFoundException e) {
            PacketSendUtility
                .sendMessage(admin, "Error: No battleground with that name was found.");
            return false;
        }
        catch (InstantiationException e) {
            PacketSendUtility
                .sendMessage(admin, "Error: No battleground with that name was found.");
            return false;
        }
        catch (IllegalAccessException e) {
            PacketSendUtility
                .sendMessage(admin, "Error: No battleground with that name was found.");
            return false;
        }

        if (bg == null) {
            PacketSendUtility
                .sendMessage(admin, "Error: No battleground with that name was found.");
            return false;
        }

        return true;
    }

    private void message(Player pl, String msg) {
        PacketSendUtility.sendMessage(pl, msg);
    }

    private void announce(final String msg) {
        World.getInstance().doOnAllPlayers(new Executor<Player>() {
            @Override
            public boolean run(Player pl) {
                PacketSendUtility.sendSys2Message(pl, "Tournament", msg);
                return true;
            }
        });
    }
}
