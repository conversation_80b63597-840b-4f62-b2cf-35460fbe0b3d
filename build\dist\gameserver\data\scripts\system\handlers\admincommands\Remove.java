/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.player.Storage;
import gameserver.model.items.ItemId;
import gameserver.network.aion.serverpackets.SM_DELETE_ITEM;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.World;

import java.util.List;

/**
 * <AUTHOR> ATracer
 */
public class Remove extends AdminCommand {

    public Remove() {
        super("remove");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_REMOVE) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command.");
            return;
        }

        if (params.length < 2) {
            PacketSendUtility
                .sendMessage(
                    admin,
                    "Syntax: //remove <player name> <item id> <quantity>"
                        + "\nSyntax: //remove <player name> equipment -- deletes all currently equipped gear"
                        + "\nSyntax: //remove <player name> inventory -- deletes all items from inventory");
            return;
        }

        int itemId = 0;
        long itemCount = 1;
        Player target = World.getInstance().findPlayer(Util.convertName(params[0]));
        if (target == null) {
            PacketSendUtility.sendMessage(admin, "Could not find an online player with that name.");
            return;
        }

        if (params[1].equalsIgnoreCase("equipment") || params[1].equalsIgnoreCase("inventory")) {
            List<Item> items;

            boolean equip = false;

            if (params[1].equalsIgnoreCase("equipment")) {
                items = target.getEquipment().getEquippedItemsWithoutStigma();
                equip = true;
            }
            else if (params[1].equalsIgnoreCase("inventory")) {
                items = target.getInventory().getAllItems();
            }
            else {
                PacketSendUtility.sendMessage(admin, "Something went wrong!");
                return;
            }

            for (Item item : items) {
                if (item.getItemId() == ItemId.KINAH.value())
                    continue;

                if (equip) {
                    target.getEquipment().unEquipItem(item.getObjectId(), true);
                }

                target.getInventory().removeFromBag(item, true);
                PacketSendUtility.sendPacket(target, new SM_DELETE_ITEM(item.getObjectId()));

                PacketSendUtility.sendMessage(admin, "Removed [item:" + item.getItemId() + "] ("
                    + item.getItemId() + ") from " + target.getName());
            }

            return;
        }

        try {
            if (params[1].startsWith("[item: "))
                itemId = Integer.parseInt(params[1].substring(7, 16));
            else if (params[1].startsWith("[item:"))
                itemId = Integer.parseInt(params[1].substring(6, 15));
            else
                itemId = Integer.parseInt(params[1]);

            if (params.length == 3) {
                itemCount = Long.parseLong(params[2]);
            }
        }
        catch (NumberFormatException e) {
            PacketSendUtility.sendMessage(admin, "Parameter needs to be an integer.");
            return;
        }

        Storage bag = target.getInventory();

        int removed = 0;
        while (removed < itemCount) {
            long itemsInBag = bag.getItemCountByItemId(itemId);
            if (removed == 0 && itemsInBag == 0) {
                if (target.getEquipment().getEquippedItemsByItemId(itemId).isEmpty()) {
                    // Kinah cannot be removed from player's inventory using this command, bug?
                    PacketSendUtility.sendMessage(admin,
                        "Items with that id are not found in that player's inventory.");
                    return;
                }
            }

            Item item = null;
            if (itemsInBag > 0)
                item = bag.getFirstItemByItemId(itemId);
            else if (!target.getEquipment().getEquippedItemsByItemId(itemId).isEmpty())
                item = target.getEquipment().getEquippedItemsByItemId(itemId).get(0);

            if (item == null)
                break;

            long count = item.getItemCount();

            if (itemsInBag == 0) {
                target.getEquipment().unEquipItem(item.getObjectId(), true);
                // bag.removeFromBag(item, true);
                // PacketSendUtility.sendPacket(target, new SM_DELETE_ITEM(item.getObjectId()));
            }

            bag.removeFromBagByObjectId(item.getObjectId(), itemCount - removed);

            removed += Math.min(itemCount - removed, count);
        }

        PacketSendUtility.sendMessage(admin, "Removed " + removed + " [item:" + itemId + "] ("
            + itemId + ") from player " + target.getName());
        PacketSendUtility.sendMessage(target, "Admin " + admin.getName()
            + " removed an item from you");
    }
}
