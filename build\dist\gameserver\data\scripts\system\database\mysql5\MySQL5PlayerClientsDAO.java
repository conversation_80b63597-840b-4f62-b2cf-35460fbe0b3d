/*
 * This file is part of aion-emu <aion-emu.com>.
 *
 *  aion-emu is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  aion-emu is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with aion-emu.  If not, see <http://www.gnu.org/licenses/>.
 */
package mysql5;

import gameserver.dao.PlayerClientsDAO;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.DatabaseFactory;

/**
 * <AUTHOR>
 * 
 */
public class MySQL5PlayerClientsDAO extends PlayerClientsDAO {
    private static final Logger log = Logger.getLogger(MySQL5PlayerClientsDAO.class);

    @Override
    public boolean saveMacAndVersion(int accountId, String mac, String version) {
        Connection con = null;

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con
                .prepareStatement("REPLACE INTO player_clients (account_id, mac, version) VALUES (?,?,?)");
            stmt.setInt(1, accountId);
            stmt.setString(2, mac);
            stmt.setString(3, version);
            stmt.execute();
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        return true;
    }
    
    @Override
    public String getMac(int accountId) {
        String mac = "";
        Connection con = null;
        
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con
                .prepareStatement("SELECT mac FROM player_clients WHERE account_id = ?");
            stmt.setInt(1, accountId);
            
            ResultSet rs = stmt.executeQuery();
            if (rs.next())
                mac = rs.getString("mac");
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        return mac;
    }
    
    @Override
    public String getVersion(int accountId) {
        String version = "";
        Connection con = null;
        
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con
                .prepareStatement("SELECT version FROM player_clients WHERE account_id = ?");
            stmt.setInt(1, accountId);
            
            ResultSet rs = stmt.executeQuery();
            if (rs.next())
                version = rs.getString("version");
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        return version;
    }

    @Override
    public boolean supports(String databaseName, int majorVersion, int minorVersion) {
        return MySQL5DAOUtils.supports(databaseName, majorVersion, minorVersion);
    }

}
