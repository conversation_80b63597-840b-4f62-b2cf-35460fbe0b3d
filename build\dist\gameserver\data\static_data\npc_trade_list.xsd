<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" version="1.0">
	<xs:element name="npc_trade_list">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="tradelist_template" type="TradelistTemplate" minOccurs="1"
					 maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="TradelistTemplate">
		<xs:sequence>
			<xs:element name="tradelist" minOccurs="0" maxOccurs="unbounded">
				<xs:complexType>
					<xs:attribute name="id" type="xs:int"/>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="category" type="xs:int"/>
		<xs:attribute name="count" type="xs:int"/>
		<xs:attribute name="name" type="xs:string"/>
		<xs:attribute name="npc_id" type="xs:int"/>
		<xs:attribute name="sell_price_rate" type="xs:float"/>
		<xs:attribute name="buy_price_rate" type="xs:float"/>
	</xs:complexType>
</xs:schema>
