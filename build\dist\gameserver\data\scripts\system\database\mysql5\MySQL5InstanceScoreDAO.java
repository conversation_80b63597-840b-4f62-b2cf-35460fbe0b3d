/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package mysql5;

import gameserver.dao.InstanceScoreDAO;
import gameserver.model.gameobjects.player.Player;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.DB;

/**
 * AccountTime DAO implementation for MySQL5
 * 
 * <AUTHOR>
 */
public class MySQL5InstanceScoreDAO extends InstanceScoreDAO {
    private static final Logger log = Logger.getLogger(MySQL5InstanceScoreDAO.class);

    @Override
    public boolean insertScore(Player player, int instanceId, int score, int objectives,
        int secondsLeft, int extra) {
        PreparedStatement ps = null;

        try {
            ps = DB
                .prepareStatement("INSERT INTO instance_scores (player_id, instance_id, score, objectives, seconds_left, extra) VALUES (?,?,?,?,?,?)");

            ps.setInt(1, player.getObjectId());
            ps.setInt(2, instanceId);
            ps.setInt(3, score);
            ps.setInt(4, objectives);
            ps.setInt(5, secondsLeft);
            ps.setInt(6, extra);

            ps.execute();
        }
        catch (Exception e) {
            log.error("Error inserting score for player " + player.getObjectId() + ", instance "
                + instanceId + " with score " + score, e);
            return false;
        }
        finally {
            DB.close(ps);
        }

        return true;
    }

    @Override
    public Collection<InstanceScore> getScores(Player player) {
        List<InstanceScore> scores = new ArrayList<InstanceScore>();

        PreparedStatement ps = null;

        try {
            ps = DB.prepareStatement("SELECT * FROM instance_scores WHERE player_id = ?");

            ps.setInt(1, player.getObjectId());

            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                scores.add(new InstanceScore(rs.getInt("player_id"), rs.getInt("instance_id"), rs
                    .getInt("score"), rs.getInt("objectives"), rs.getInt("seconds_left"), rs
                    .getInt("extra")));
            }

            rs.close();
        }
        catch (Exception e) {
            log.error("Error fetching scores for player " + player.getObjectId(), e);
        }
        finally {
            DB.close(ps);
        }

        return scores;
    }

    @Override
    public boolean deleteScores(int playerObjId) {
        PreparedStatement ps = null;

        try {
            ps = DB.prepareStatement("DELETE FROM instance_scores WHERE player_id = ?");

            ps.setInt(1, playerObjId);

            ps.execute();
        }
        catch (Exception e) {
            log.error("Error deleting scores for player " + playerObjId, e);
            return false;
        }
        finally {
            DB.close(ps);
        }

        return true;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean supports(String s, int i, int i1) {
        return MySQL5DAOUtils.supports(s, i, i1);
    }
}
