package usercommands;

import gameserver.model.gameobjects.player.Player;
import gameserver.services.ArenaService;
import gameserver.services.ItemRemodelService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.UserCommand;

public class PreviewCommand extends UserCommand {
    private static final int REMODEL_PREVIEW_DURATION = 30;

    public PreviewCommand() {
        super("preview");
    }

    public void executeCommand(Player player, String param) {
        String[] params = Util.splitCommandArgs(param);

        if (player.getBattleground() != null || ArenaService.getInstance().isInArena(player)
            || player.isInCombatLong()) {
            PacketSendUtility.sendMessage(player,
                "You cannot preview item skins while in BG, FFA, 1v1 or simply combat.");
            return;
        }

        if (params.length < 1) {
            PacketSendUtility.sendMessage(player, "Syntax: .preview <itemid> -- previews last "
                + REMODEL_PREVIEW_DURATION + " seconds.");
            return;
        }
        else {
            int itemId = 0;
            try {
                if (params[0].startsWith("[item: "))
                    itemId = Integer.parseInt(params[0].substring(7, 16));
                else if (params[0].startsWith("[item:"))
                    itemId = Integer.parseInt(params[0].substring(6, 15));
                else
                    itemId = Integer.parseInt(params[0]);
            }
            catch (Exception e) {
                PacketSendUtility.sendMessage(player,
                    "Error! Item id's are numbers like ********* or [item:*********]!");
                return;
            }

            if (ItemRemodelService.commandPreviewRemodelItem(player, itemId,
                REMODEL_PREVIEW_DURATION)) {
                PacketSendUtility.sendMessage(player, "The remodel preview will stay for "
                    + REMODEL_PREVIEW_DURATION + " seconds.");
            }
            else {
                PacketSendUtility.sendMessage(player,
                    "It was not possible to remodel any of your equipped items to [item: " + itemId
                        + "]!");
            }
        }
    }
}