<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">

	<xs:include schemaLocation="../global_types.xsd" />
	<xs:include schemaLocation="../import.xsd" />

	<xs:element name="quests" type="Quests" />

	<xs:complexType name="Quests">
		<xs:sequence>
			<xs:element ref="import" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="quest" type="Quest" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="Quest">
		<xs:sequence>
			<xs:element name="collect_items" type="CollectItems" minOccurs="0" maxOccurs="1" />
			<xs:element name="rewards" type="Rewards" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="extrewards" type="Rewards" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="quest_drop" type="QuestDrop" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="finished_quest_conds" type="QuestStartConditions" minOccurs="0" maxOccurs="1" />
			<xs:element name="unfinished_quest_conds" type="QuestStartConditions" minOccurs="0" maxOccurs="1" />
			<xs:element name="acquired_quest_conds" type="QuestStartConditions" minOccurs="0" maxOccurs="1" />
			<xs:element name="noacquired_quest_conds" type="QuestStartConditions" minOccurs="0" maxOccurs="1" />
			<xs:element name="class_permitted" type="ClassListType" minOccurs="0" maxOccurs="1" />
			<xs:element name="gender_permitted" type="Gender" minOccurs="0" maxOccurs="1" />
			<xs:element name="quest_work_items" type="QuestWorkItems" minOccurs="0" maxOccurs="1" />
			<xs:element name="fighter_selectable_reward" type="QuestItems" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="knight_selectable_reward" type="QuestItems" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="ranger_selectable_reward" type="QuestItems" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="assassin_selectable_reward" type="QuestItems" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="wizard_selectable_reward" type="QuestItems" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="elementalist_selectable_reward" type="QuestItems" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="priest_selectable_reward" type="QuestItems" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="chanter_selectable_reward" type="QuestItems" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="id" type="xs:int" use="required" />
		<xs:attribute name="name" type="xs:string" />
		<xs:attribute name="nameId" type="xs:int" />
		<xs:attribute name="minlevel_permitted" type="xs:int" />
		<xs:attribute name="maxlevel_permitted" type="xs:int" default="0" />
		<xs:attribute name="max_repeat_count" type="xs:int" />
		<xs:attribute name="repeat_day" type="xs:int" />
		<xs:attribute name="cannot_share" type="xs:boolean" default="false" />
		<xs:attribute name="cannot_giveup" type="xs:boolean" default="false" />
		<xs:attribute name="use_class_reward" type="xs:int" default="0" />
		<xs:attribute name="race_permitted" type="Race" />
		<xs:attribute name="combineskill" type="xs:int" />
		<xs:attribute name="combine_skillpoint" type="xs:int" />
		<xs:attribute name="timer" type="xs:boolean" default="false" />
	</xs:complexType>

	<xs:complexType name="CollectItems">
		<xs:sequence>
			<xs:element name="collect_item" type="CollectItem" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="QuestStartConditions">
		<xs:sequence>
			<xs:element name="condition" type="QuestStartCondition" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="QuestDrop">
		<xs:attribute name="npc_id" type="xs:int" />
		<xs:attribute name="item_id" type="xs:int" />
		<xs:attribute name="chance" type="xs:int" />
		<xs:attribute name="drop_each_member" type="xs:boolean" />
		<xs:attribute name="mentor" type="xs:boolean" />
	</xs:complexType>

	<xs:complexType name="Rewards">
		<xs:sequence>
			<xs:element name="selectable_reward_item" type="QuestItems" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="reward_item" type="QuestItems" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="gold" type="xs:int" />
		<xs:attribute name="exp" type="xs:int" />
		<xs:attribute name="reward_abyss_point" type="xs:int" />
		<xs:attribute name="title" type="xs:int" />
		<xs:attribute name="extend_inventory" type="xs:int" />
		<xs:attribute name="extend_stigma" type="xs:int" />
	</xs:complexType>

	<xs:complexType name="CollectItem">
		<xs:attribute name="item_id" type="xs:int" />
		<xs:attribute name="count" type="xs:int" />
	</xs:complexType>

	<xs:complexType name="QuestWorkItems">
		<xs:sequence>
			<xs:element name="quest_work_item" type="QuestItems" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="QuestItems">
		<xs:attribute name="item_id" type="xs:int" />
		<xs:attribute name="count" type="xs:int" />
	</xs:complexType>

	<xs:simpleType name="NumberListType">
		<xs:list itemType="xs:int" />
	</xs:simpleType>

	<xs:complexType name="QuestStartCondition">
		<xs:sequence>
			<xs:element name="quest" type="QuestStep" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="QuestStep">
		<xs:attribute name="id" type="xs:int" />
		<xs:attribute name="rewardNo" type="xs:int" />
	</xs:complexType>

	<xs:simpleType name="Gender">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MALE" />
			<xs:enumeration value="FEMALE" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="ClassListType">
		<xs:list itemType="playerClass" />
	</xs:simpleType>
</xs:schema>
