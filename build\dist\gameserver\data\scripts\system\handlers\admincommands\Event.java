/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.configs.main.CustomConfig;
import gameserver.model.Race;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.pvpevents.Battleground;
import gameserver.services.ArcadeUpgradeService;
import gameserver.services.ArenaService;
import gameserver.services.EventService;
import gameserver.services.LadderService;
import gameserver.services.OpenWorldService;
import gameserver.services.OutlawService;
import gameserver.services.OutpostService;
import gameserver.services.TeleportService;
import gameserver.services.VortexService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.Executor;
import gameserver.world.World;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 */
public class Event extends AdminCommand {
    public Event() {
        super("event");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.GM_LEVEL) {
            PacketSendUtility.sendMessage(admin,
                "You don't have enough rights to execute this command.");
            return;
        }

        if (params.length < 1) {
            PacketSendUtility
                .sendMessage(
                    admin,
                    "Syntax: //event <create | status | cancel | pausesystem | abyss | eye | kysis | panesterra | sub | queue | return | vortex | kill | duoffa | instances | doom | invasionfix | antizerg | arcade | ffanames | recharger | 2v2>"
                        + "\nSyntax: //event return will return all players on map to their bind point.");
            return;
        }

        if ("create".startsWith(params[0])) {
            if (params.length < 2) {
                StringBuilder sb = new StringBuilder();
                sb.append("Aliases:");
                for (String a : Battleground.getAliases().keySet())
                    sb.append(" " + a + ",");

                PacketSendUtility.sendMessage(admin,
                    "Syntax: //event create <battleground> [group entry] - class name or alias");
                PacketSendUtility.sendMessage(admin, sb.toString());
                PacketSendUtility.sendMessage(admin,
                    "To allow group entries, simply specify ANY 3rd parameter.");

                return;
            }

            Battleground bg;
            try {
                if (Battleground.getAliases().containsKey(params[1].toLowerCase()))
                    bg = (Battleground) Battleground.getAliases().get(params[1].toLowerCase())
                        .newInstance();
                else
                    bg = (Battleground) getClass().getClassLoader()
                        .loadClass("gameserver.model.pvpevents." + params[1]).newInstance();
            }
            catch (ClassNotFoundException e) {
                PacketSendUtility.sendMessage(admin,
                    "Error: No battleground with that name was found.");
                return;
            }
            catch (InstantiationException e) {
                PacketSendUtility.sendMessage(admin,
                    "Error: No battleground with that name was found.");
                return;
            }
            catch (IllegalAccessException e) {
                PacketSendUtility.sendMessage(admin,
                    "Error: No battleground with that name was found.");
                return;
            }

            if (bg == null) {
                PacketSendUtility.sendMessage(admin,
                    "Error: No battleground with that name was found.");
                return;
            }

            if (!LadderService.getInstance().createEventBg(bg, params.length >= 3, false)) {
                PacketSendUtility.sendMessage(admin,
                    "Please wait until the current event has started.");
                return;
            }
        }
        else if ("status".startsWith(params[0])) {
            if (!LadderService.getInstance().isEventReady()) {
                PacketSendUtility.sendMessage(admin,
                    "There is currently no event open for registration.");
                return;
            }

            int entries = LadderService.getInstance().getEventQueueSize();
            int size = LadderService.getInstance().getEventQueuePlayers();
            PacketSendUtility.sendMessage(admin, size + " players in " + entries
                + " entries have signed up for the event so far.");
        }
        else if ("cancel".startsWith(params[0])) {
            if (LadderService.getInstance().isEventReady()) {
                LadderService.getInstance().cancelEvent();
                PacketSendUtility.sendMessage(admin, "The event has been cancelled!");
            }
            else {
                PacketSendUtility.sendMessage(admin, "There is no event to cancel.");
            }
        }
        else if ("pausesystem".startsWith(params[0])) {
            if (EventService.getInstance().pauseResumeSystem())
                PacketSendUtility.sendMessage(admin, "The event system has been paused!");
            else
                PacketSendUtility.sendMessage(admin, "The event system has been resumed!");
        }
        else if ("abyss".startsWith(params[0])) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //event abyss <start | stop>");
                return;
            }

            if ("start".startsWith(params[1])) {
                if (EventService.getInstance().startAbyssEvent())
                    PacketSendUtility.sendMessage(admin, "The Abyss Event has been started!");
                else
                    PacketSendUtility.sendMessage(admin,
                        "Unable to start the Abyss Event. Maybe it is already running!");
            }
            else if ("stop".startsWith(params[1])) {
                if (EventService.getInstance().stopAbyssEvent())
                    PacketSendUtility.sendMessage(admin, "The Abyss Event has been stopped!");
                else
                    PacketSendUtility.sendMessage(admin,
                        "Unable to stop the Abyss Event. Maybe it wasn't started!");
            }
        }
        else if ("eye".startsWith(params[0])) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //event eye <start | stop>");
                return;
            }

            if ("start".startsWith(params[1])) {
                if (EventService.getInstance().startEyeEvent())
                    PacketSendUtility.sendMessage(admin,
                        "The Tiamaranta's Eye Event has been started!");
                else
                    PacketSendUtility.sendMessage(admin,
                        "Unable to start the Tiamaranta's Eye Event. Maybe it is already running!");
            }
            else if ("stop".startsWith(params[1])) {
                if (EventService.getInstance().stopEyeEvent())
                    PacketSendUtility.sendMessage(admin,
                        "The Tiamaranta's Eye Event has been stopped!");
                else
                    PacketSendUtility.sendMessage(admin,
                        "Unable to stop the Tiamaranta's Eye. Maybe it wasn't started!");
            }
        }
        else if ("kysis".startsWith(params[0])) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //event kysis <start | stop>");
                return;
            }

            if ("start".startsWith(params[1])) {
                if (EventService.getInstance().startKysisEvent())
                    PacketSendUtility.sendMessage(admin, "The Kysis FFA Event has been started!");
                else
                    PacketSendUtility.sendMessage(admin,
                        "Unable to start the Kysis FFA Event. Maybe it is already running!");
            }
            else if ("stop".startsWith(params[1])) {
                if (EventService.getInstance().stopKysisEvent())
                    PacketSendUtility.sendMessage(admin, "The Kysis FFA Event has been stopped!");
                else
                    PacketSendUtility.sendMessage(admin,
                        "Unable to stop the Kysis FFA Event. Maybe it wasn't started!");
            }
        }
        else if ("panesterra".startsWith(params[0])) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //event panesterra <start | stop>");
                return;
            }

            if ("start".startsWith(params[1])) {
                if (EventService.getInstance().startPanesterraEvent())
                    PacketSendUtility.sendMessage(admin, "The Panesterra Event has been started!");
                else
                    PacketSendUtility.sendMessage(admin,
                        "Unable to start the Panesterra Event. Maybe it is already running!");
            }
            else if ("stop".startsWith(params[1])) {
                if (EventService.getInstance().stopPanesterraEvent())
                    PacketSendUtility.sendMessage(admin, "The Panesterra Event has been stopped!");
                else
                    PacketSendUtility.sendMessage(admin,
                        "Unable to stop the Panesterra Event. Maybe it wasn't started!");
            }
        }
        else if ("sub".startsWith(params[0])) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //event sub <start | stop>");
                return;
            }

            if ("start".startsWith(params[1])) {
                if (EventService.getInstance().startSubEvent())
                    PacketSendUtility.sendMessage(admin,
                        "The Sub Panesterra Event has been started!");
                else
                    PacketSendUtility.sendMessage(admin,
                        "Unable to start the Sub Panesterra Event. Maybe it is already running!");
            }
            else if ("stop".startsWith(params[1])) {
                if (EventService.getInstance().stopSubEvent())
                    PacketSendUtility.sendMessage(admin,
                        "The Sub Panesterra Event has been stopped!");
                else
                    PacketSendUtility.sendMessage(admin,
                        "Unable to stop the Sub Panesterra Event. Maybe it wasn't started!");
            }
        }
        else if ("queue".startsWith(params[0])) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin,
                    "Syntax: //event queue <start | racestart | status | stop | teleport | map>");
                PacketSendUtility
                    .sendMessage(
                        admin,
                        "Syntax: To specify limit, do //event queue start [limit] [lockout time in minutes] -- otherwise no limit."
                            + "\nSyntax: Or //event queue racestart <ely | asmo> [limit] [lockout time in minutes]");
                return;
            }

            if ("start".startsWith(params[1])) {
                if (params.length >= 4) {
                    int lockout = Integer.parseInt(params[3]);
                    int limit = Integer.parseInt(params[2]);
                    EventService.getInstance().startEventQueue(null, limit, lockout);
                }
                else if (params.length == 3) {
                    int limit = Integer.parseInt(params[2]);
                    EventService.getInstance().startEventQueue(null, limit, -1);
                }
                else {
                    EventService.getInstance().startEventQueue(null, -1, -1);
                }

                worldAnnounce("Event", "You can now register for an event by typing .queue!");
            }
            else if ("racestart".startsWith(params[1])) {
                if (params.length < 3) {
                    PacketSendUtility
                        .sendMessage(admin,
                            "Syntax: //event queue racestart <ely | asmo> [limit] [lockout time in minutes]");
                    return;
                }

                Race race = null;
                if ("elyos".startsWith(params[2]))
                    race = Race.ELYOS;
                else if ("asmodian".startsWith(params[2]))
                    race = Race.ASMODIANS;

                if (params.length >= 5) {
                    int lockout = Integer.parseInt(params[4]);
                    int limit = Integer.parseInt(params[3]);
                    EventService.getInstance().startEventQueue(race, limit, lockout);
                }
                else if (params.length == 4) {
                    int limit = Integer.parseInt(params[3]);
                    EventService.getInstance().startEventQueue(race, limit, -1);
                }
                else {
                    EventService.getInstance().startEventQueue(race, -1, -1);
                }

                worldAnnounce(race, "Event", "You can now register for an event by typing .queue!");
            }
            else if ("status".startsWith(params[1])) {
                List<Player> queue = EventService.getInstance().getEventQueue();

                int countAll = 0, countEly = 0, countAsmo = 0;
                for (Iterator<Player> it = queue.iterator(); it.hasNext();) {
                    Player pl = it.next();

                    if (!pl.isOnline()) {
                        it.remove();
                        continue;
                    }

                    countAll++;

                    if (pl.getCommonData().getRace() == Race.ELYOS)
                        countEly++;
                    else if (pl.getCommonData().getRace() == Race.ASMODIANS)
                        countAsmo++;
                }

                PacketSendUtility.sendMessage(admin, "[QUEUE STATUS (" + countAll + ")]"
                    + "\nElyos: " + countEly + "\nAsmos: " + countAsmo);
            }
            else if ("stop".startsWith(params[1])) {
                EventService.getInstance().stopEventQueue();

                worldAnnounce("Event", "It is no longer possible to register for the event.");
            }
            else if ("teleport".startsWith(params[1])) {
                List<Player> queue = EventService.getInstance().getEventQueue();

                for (Iterator<Player> it = queue.iterator(); it.hasNext();) {
                    Player pl = it.next();

                    if (!pl.isOnline()) {
                        it.remove();
                        continue;
                    }

                    EventService.getInstance().blackListAccount(pl.getPlayerAccount().getId(),
                        System.currentTimeMillis());

                    TeleportService.teleportTo(pl, admin.getWorldId(), admin.getInstanceId(),
                        admin.getX(), admin.getY(), admin.getZ(),
                        TeleportService.TELEPORT_PORTAL_DELAY);
                    PacketSendUtility.sendMessage(pl, "You are being teleported to the event!");
                }
            }
            else if ("map".startsWith(params[1])) {
                EventService.getInstance().startEventQueue(null, -1, -1);

                admin.getWorldMapInstance().doOnAllPlayers(new Executor<Player>() {
                    @Override
                    public boolean run(Player pl) {
                        EventService.getInstance().addToEventQueue(pl);
                        return true;
                    }
                }, true);

                PacketSendUtility.sendMessage(admin, "Event queue has been filled with "
                    + EventService.getInstance().getEventQueue().size()
                    + " players on your current map.");
            }
        }
        else if ("return".startsWith(params[0])) {
            admin.getWorldMapInstance().doOnAllPlayers(new Executor<Player>() {
                @Override
                public boolean run(Player pl) {
                    if (pl.getAccessLevel() <= 0)
                        TeleportService.moveToBindLocation(pl, true,
                            TeleportService.TELEPORT_PORTAL_DELAY);

                    PacketSendUtility.sendMessage(pl, "You are being returned to your bind point.");

                    return true;
                }
            });

            PacketSendUtility.sendMessage(admin,
                "All players on the map have been returned to their bind point.");
        }
        else if ("vortex".startsWith(params[0])) {
            VortexService.getInstance().spawnVortexes();

            PacketSendUtility.sendMessage(admin, "Dimensional Vortexes have been spawned.");
        }
        else if ("kill".startsWith(params[0])) {
            if (params.length < 2) {
                PacketSendUtility
                    .sendMessage(admin,
                        "Syntax: //event kill <bg id | player name> -- terminates BG with id or player is in");
                return;
            }

            Map<Integer, Battleground> bgMap = LadderService.getInstance().getBattlegrounds();

            Integer bgId;
            try {
                bgId = Integer.parseInt(params[1]);

                if (bgMap.containsKey(bgId)) {
                    bgMap.get(bgId).killBattleground();
                    PacketSendUtility.sendMessage(admin, "The battleground has been terminated.");
                }
                else
                    PacketSendUtility.sendMessage(admin,
                        "There is no battleground running with that id!");
            }
            catch (Exception e) {
                String name = Util.convertName(params[1]);
                Player pl = World.getInstance().findPlayer(name);

                if (pl == null) {
                    PacketSendUtility.sendMessage(admin, "There is no player named " + params[1]
                        + " online.");
                    return;
                }
                else if (pl.getBattleground() == null) {
                    PacketSendUtility.sendMessage(admin,
                        "The specified player is not in a battleground.");
                    return;
                }

                pl.getBattleground().killBattleground();
                PacketSendUtility.sendMessage(admin, "The battleground has been terminated.");
            }
        }
        else if ("duoffa".startsWith(params[0])) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //event duoffa <start | stop>");
                return;
            }

            if ("start".startsWith(params[1])) {
                if (!ArenaService.getInstance().isDuoOpen()) {
                    ArenaService.getInstance().openDuoMap();
                    PacketSendUtility.sendMessage(admin, "The Duo FFA map has been opened!");
                }
                else
                    PacketSendUtility.sendMessage(admin,
                        "Unable to open the Duo FFA map. Perhaps it already is!");
            }
            else if ("stop".startsWith(params[1])) {
                if (ArenaService.getInstance().isDuoOpen()) {
                    ArenaService.getInstance().closeDuoMap();
                    PacketSendUtility.sendMessage(admin, "The Duo FFA map has been closed!");
                }
                else
                    PacketSendUtility.sendMessage(admin,
                        "Unable to close the Duo FFA map. Perhaps it already is!");
            }
        }
        else if ("instances".startsWith(params[0])) {
            if (params.length < 2) {
                PacketSendUtility
                    .sendMessage(admin, "Syntax: //event instances <enable | disable>");
                return;
            }

            if ("enable".startsWith(params[1])) {
                EventService.getInstance().setInstancesEnabled(true);
                PacketSendUtility.sendMessage(admin, "Instances are now enabled.");
            }
            else if ("disable".startsWith(params[1])) {
                EventService.getInstance().setInstancesEnabled(false);
                PacketSendUtility.sendMessage(admin, "Instances are now disabled.");
            }
        }
        else if ("doom".startsWith(params[0])) {
            if (OutpostService.getInstance().letThereBeDoom()) {
                PacketSendUtility.sendMessage(admin, "DOOM invasion has been started!");
            }
            else {
                PacketSendUtility.sendMessage(admin,
                    "You cannot start DOOM invasion while other invasions are running.");
            }
        }
        else if ("invasionfix".startsWith(params[0])) {
            OutpostService.getInstance().clearInvasions();
            PacketSendUtility.sendMessage(admin, "Cleared invasion lists. All should be good now!");
        }
        else if ("antizerg".startsWith(params[0])) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //event antizerg <enable | disable>"
                    + "\nAnti-Zerg system is currently "
                    + (OutlawService.getInstance().isAntiZergEnabled() ? "ENABLED" : "DISABLED"));
                return;
            }

            if ("enable".startsWith(params[1])) {
                OutlawService.getInstance().setAntiZergEnabled(true);
                PacketSendUtility.sendMessage(admin, "Anti-Zerg system has been enabled.");
            }
            else if ("disable".startsWith(params[1])) {
                OutlawService.getInstance().setAntiZergEnabled(false);
                PacketSendUtility.sendMessage(admin, "Anti-Zerg system has been disabled.");
            }
            else {
                PacketSendUtility.sendMessage(admin, "Error! Please specify a valid parameter.");
            }
        }
        else if ("arcade".startsWith(params[0])) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //event arcade <enable | disable>"
                    + "\nArcade Upgrade is currently "
                    + (ArcadeUpgradeService.getInstance().isEnabled() ? "ENABLED" : "DISABLED"));
                return;
            }

            if ("enable".startsWith(params[1])) {
                ArcadeUpgradeService.getInstance().enable();
                PacketSendUtility.sendMessage(admin, "Arcade Upgrade has been enabled.");
            }
            else if ("disable".startsWith(params[1])) {
                ArcadeUpgradeService.getInstance().disable();
                PacketSendUtility.sendMessage(admin, "Arcade Upgrade has been disabled.");
            }
            else {
                PacketSendUtility.sendMessage(admin, "Error! Please specify a valid parameter.");
            }
        }
        else if ("openworld".startsWith(params[0])) {
            if (!CustomConfig.OLD_SCHOOL) {
                PacketSendUtility.sendMessage(admin,
                    "You can only use this command on the Old School server.");
                return;
            }

            OpenWorldService.getInstance().pickOldSchoolMap(false);

            PacketSendUtility.sendMessage(admin, "The Open World map will transition to "
                + OpenWorldService.getInstance().getName() + " in 20 seconds.");
        }
        else if ("ffanames".startsWith(params[0])) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //event ffanames <enable | disable>"
                    + "\nFFA names are currently "
                    + (ArenaService.getInstance().showNames() ? "ENABLED" : "DISABLED"));
                return;
            }

            if ("enable".startsWith(params[1])) {
                ArenaService.getInstance().setShowNames(true);
                PacketSendUtility.sendMessage(admin, "FFA names have been enabled.");
            }
            else if ("disable".startsWith(params[1])) {
                ArenaService.getInstance().setShowNames(false);
                PacketSendUtility.sendMessage(admin, "FFA names have been disabled.");
            }
            else {
                PacketSendUtility.sendMessage(admin, "Error! Please specify a valid parameter.");
            }
        }
        else if ("recharger".startsWith(params[0])) {
            if (EventService.getInstance().isRechargerQueueOn()) {
                if (params.length < 2) {
                    PacketSendUtility
                        .sendMessage(admin,
                            "There is already a recharger queue active. Use //event recharger force if it's bugging.");
                    return;
                }
                else if ("force".equalsIgnoreCase(params[1])) {
                    PacketSendUtility.sendMessage(admin,
                        "Starting new recharger queue with override/force.");
                }
                else {
                    PacketSendUtility.sendMessage(admin, "Invalid argument specified.");
                    return;
                }
            }

            EventService.getInstance().startRechargerQueue();

            PacketSendUtility.sendMessage(admin, "Recharger queue has been started.");
        }
        else if ("2v2".startsWith(params[0])) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //event 2v2 <enable | disable>"
                    + "\n2v2 is currently "
                    + (EventService.getInstance().is2v2Enabled() ? "ENABLED" : "DISABLED"));
                return;
            }

            if ("enable".startsWith(params[1])) {
                EventService.getInstance().set2v2Enabled(true);
                PacketSendUtility.sendMessage(admin, "2v2 has been enabled.");

                worldAnnounce("2v2",
                    "The 2v2 queue is now open. Sign up in a group of 2 by typing .2v2!");
            }
            else if ("disable".startsWith(params[1])) {
                EventService.getInstance().set2v2Enabled(false);
                PacketSendUtility.sendMessage(admin, "2v2 has been disabled.");

                worldAnnounce("2v2", "The 2v2 queue has been closed.");
            }
            else {
                PacketSendUtility.sendMessage(admin, "Error! Please specify a valid parameter.");
            }
        }
    }

    private void worldAnnounce(final Race race, final String sender, final String message) {
        World.getInstance().doOnAllPlayers(new Executor<Player>() {
            @Override
            public boolean run(Player pl) {
                if (race != null) {
                    if (pl.getCommonData().getRace() == race)
                        PacketSendUtility.sendSys2Message(pl, sender, message);
                }
                else {
                    PacketSendUtility.sendSys2Message(pl, sender, message);
                }

                return true;
            }
        });
    }

    private void worldAnnounce(final String sender, final String message) {
        this.worldAnnounce(null, sender, message);
    }
}
