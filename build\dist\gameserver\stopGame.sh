#!/bin/bash

if [ -e gameserver.pid ]
then
  gspid=`cat gameserver.pid`
  closed=1
  if (( 0 != `pidof -x -s game_loop.sh | wc -l`))
  then
	kill `pidof -x -s game_loop.sh`
  fi
   if ((0 != `ps u -p ${gspid} | grep 'gameserver' | wc -l`))
  then
    kill ${gspid}
    echo "GameServer stop signal sent"
    until [ $closed == 0 ];
    do
	  closed=`ps ax | cut -d" " -f1-2 | grep ${gspid} | wc -l`
	  sleep 1
    done
    echo "GameServer Stopped!"
  else
    echo "gameserver.pid doesn't match any running process!"
  fi
else
  echo "GameServer is not running."
fi
exit 0