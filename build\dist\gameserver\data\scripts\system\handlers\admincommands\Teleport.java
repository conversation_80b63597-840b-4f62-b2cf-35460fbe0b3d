/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.TeleportService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.DatabaseFactory;

/**
 * <AUTHOR> y PikC
 * 
 */
public class Teleport extends AdminCommand {
    private static final Logger _logger = Logger.getLogger(Teleport.class);

    private List<String> _locations = new ArrayList<String>();

    public Teleport() {
        super("teleport");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_GOTO) {
            PacketSendUtility.sendMessage(admin,
                "You don't have enough rights to execute this command");

            return;
        }

        if (params == null || params.length < 1) {
            PacketSendUtility.sendMessage(admin, "Syntax: //teleport <add | remove | list> [name]");

            return;
        }

        if (!params[0].toLowerCase().equals("list") & !params[0].toLowerCase().equals("add")
            & !params[0].toLowerCase().equals("remove")) {
            reloadLocations();

            if (!_locations.contains(params[0].toLowerCase())) {
                PacketSendUtility.sendMessage(admin, "Teleport: There's no such location!");

                return;
            }

            Connection connection = null;

            try {
                connection = DatabaseFactory.getConnection();

                PreparedStatement preparedStatement = connection
                    .prepareStatement("SELECT * FROM admin_teleport WHERE name LIKE ?");

                preparedStatement.setString(1, params[0]);

                ResultSet resultSet = preparedStatement.executeQuery();

                while (resultSet.next())
                    TeleportService.teleportTo(admin, resultSet.getInt("map_id"),
                        resultSet.getInt("x"), resultSet.getInt("y"), resultSet.getInt("z"), 0);

                preparedStatement.close();

                resultSet.close();
            }

            catch (Exception exception) {
                _logger.error("Error: Couldn't select location! " + exception.getMessage());

                exception.printStackTrace();
            }

            finally {
                try {
                    connection.close();
                }

                catch (Exception exception) {
                    _logger.error("Error: Couldn't close connection! " + exception.getMessage());

                    exception.printStackTrace();
                }
            }
        }

        if (params[0].toLowerCase().equals("add")) {
            if (admin.getAccessLevel() < 1) {
                PacketSendUtility.sendMessage(admin, "You cannot add new //teleport's as a tester.");
                return;
            }
            
            if (params.length < 2 || params.length > 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //teleport add <name>");
                return;
            }

            reloadLocations();

            if (_locations.contains(params[1].toLowerCase())) {
                PacketSendUtility.sendMessage(admin, "Teleport: Such location already exists!");

                return;
            }

            Connection connection = null;

            try {
                connection = DatabaseFactory.getConnection();

                PreparedStatement preparedStatement = connection
                    .prepareStatement("INSERT INTO admin_teleport (name, map_id, x, y, z) VALUES (?, ?, ?, ?, ?)");

                preparedStatement.setString(1, params[1]);
                preparedStatement.setInt(2, admin.getWorldId());
                preparedStatement.setFloat(3, admin.getX());
                preparedStatement.setFloat(4, admin.getY());
                preparedStatement.setFloat(5, admin.getZ());

                preparedStatement.execute();
                preparedStatement.close();
            }
            catch (Exception exception) {
                _logger.error("Error: Couldn't add location! " + exception.getMessage());

                exception.printStackTrace();
            }
            finally {
                try {
                    connection.close();
                }

                catch (Exception exception) {
                    _logger.error("Error: Couldn't close connection! " + exception.getMessage());

                    exception.printStackTrace();
                }
            }
        }

        if (params[0].toLowerCase().equals("list")) {
            reloadLocations();

            PacketSendUtility.sendMessage(admin, "Locations:");

            for (int i = 0; i <= _locations.size(); i++)
                PacketSendUtility.sendMessage(admin, _locations.get(i));
        }

        if (params[0].toLowerCase().equals("remove")) {
            if (admin.getAccessLevel() < 1) {
                PacketSendUtility.sendMessage(admin, "You cannot remove //teleport's as a tester.");
                return;
            }
            
            if (params.length < 2 || params.length > 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //teleport remove <name>");
                return;
            }

            reloadLocations();

            if (!_locations.contains(params[1].toLowerCase())) {
                PacketSendUtility.sendMessage(admin, "Teleport: There's no such location!");

                return;
            }

            Connection connection = null;

            try {
                connection = DatabaseFactory.getConnection();

                PreparedStatement preparedStatement = connection
                    .prepareStatement("DELETE FROM admin_teleport where name = ?");

                preparedStatement.setString(1, params[1]);

                preparedStatement.execute();
                preparedStatement.close();
            }

            catch (Exception exception) {
                _logger.error("Error: Couldn't delete location! " + exception.getMessage());

                exception.printStackTrace();
            }

            finally {
                try {
                    connection.close();
                }

                catch (Exception exception) {
                    _logger.error("Error: Couldn't close connection! " + exception.getMessage());

                    exception.printStackTrace();
                }
            }
        }
    }

    private void reloadLocations() {
        _locations.clear();

        Connection connection = null;

        try {
            connection = DatabaseFactory.getConnection();

            PreparedStatement preparedStatement = connection
                .prepareStatement("SELECT name FROM admin_teleport");

            ResultSet resultSet = preparedStatement.executeQuery();

            while (resultSet.next())
                _locations.add(resultSet.getString("name").toLowerCase());

            preparedStatement.close();

            resultSet.close();
        }

        catch (Exception exception) {
            _logger.error("Error: Couldn't select location! " + exception.getMessage());

            exception.printStackTrace();
        }

        finally {
            try {
                connection.close();
            }

            catch (Exception exception) {
                _logger.error("Error: Couldn't close connection! " + exception.getMessage());

                exception.printStackTrace();
            }
        }
    }
}