-- Create missing exchange_request table for MarketExchangeService
-- This table is used to track market exchange requests

CREATE TABLE IF NOT EXISTS `exchange_request` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_object_id` int(11) NOT NULL,
  `player_id` int(11) NOT NULL,
  `account_id` int(11) NOT NULL,
  `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `done` tinyint(1) NOT NULL DEFAULT 0,
  `success` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `done` (`done`),
  KEY `player_id` (`player_id`),
  KEY `account_id` (`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
