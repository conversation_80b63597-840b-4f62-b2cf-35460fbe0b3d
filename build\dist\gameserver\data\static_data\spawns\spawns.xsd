<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">

    <xs:include schemaLocation="../import.xsd"/>

    <xs:element name="spawns">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="import" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="spawn" type="SpawnGroup" minOccurs="0"
                            maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="SpawnGroup">
        <xs:sequence>
            <xs:element name="object" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                    <xs:attribute name="x" type="xs:float"/>
                    <xs:attribute name="y" type="xs:float"/>
                    <xs:attribute name="z" type="xs:float"/>
                    <xs:attribute name="h" type="Heading"/>
                    <xs:attribute name="w" type="xs:int"/>
                    <xs:attribute name="rw" type="xs:int"/>
                    <xs:attribute name="staticid" type="xs:int"/>
                    <xs:attribute name="fly" type="xs:int"/>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="time" type="SpawnTime"/>
        <xs:attribute name="anchor" type="xs:string"/>
        <xs:attribute name="handler" type="SpawnHandlerType"/>
        <xs:attribute name="interval" type="xs:int"/>
        <xs:attribute name="pool" type="xs:int"/>
        <xs:attribute name="npcid" type="xs:int"/>
        <xs:attribute name="map" type="xs:int"/>
        <xs:attribute name="rw" type="xs:int"/>
        <xs:attribute name="npcid_dr" type="xs:int" default="0"/>
        <xs:attribute name="npcid_da" type="xs:int" default="0"/>
        <xs:attribute name="npcid_li" type="xs:int" default="0"/>
        <xs:attribute name="boss" type="xs:boolean" default="false"/>
    </xs:complexType>

    <xs:simpleType name="Heading">
        <xs:restriction base="xs:byte">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="120"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="SpawnHandlerType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="RIFT"/>
            <xs:enumeration value="STATIC"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="SpawnTime">
        <xs:restriction base="xs:string">
            <xs:enumeration value="DAY"/>
            <xs:enumeration value="NIGHT"/>
        </xs:restriction>
    </xs:simpleType>

</xs:schema>

