-- Fix for stigma stone disappearing issue
-- The root cause is a misspelled column name in the inventory table
-- This script renames 'isEquiped' to 'isEquipped' to fix equipment loading

-- Rename the misspelled column to the correct spelling
ALTER TABLE `inventory` CHANGE COLUMN `isEquiped` `isEquipped` TINYINT(1) NOT NULL DEFAULT 0;

-- Verify the change was successful
-- You can run this query to check: DESCRIBE inventory;
