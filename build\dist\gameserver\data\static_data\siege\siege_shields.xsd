<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" version="1.0">
	<xs:include schemaLocation="../import.xsd"/>
	<xs:element name="shields">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="shield" type="Shield" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="Shield">
		<xs:sequence>
			<xs:element name="center" type="ShieldPoint"/>
		</xs:sequence>
		<xs:attribute name="name" type="xs:string"/>
		<xs:attribute name="map" type="xs:int"/>
		<xs:attribute name="id" type="xs:int"/>
		<xs:attribute name="radius" type="xs:float"/>
	</xs:complexType>
	<xs:complexType name="ShieldPoint">
		<xs:attribute name="x" type="xs:float"/>
		<xs:attribute name="y" type="xs:float"/>
		<xs:attribute name="z" type="xs:float"/>
	</xs:complexType>
</xs:schema>