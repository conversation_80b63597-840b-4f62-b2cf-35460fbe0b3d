/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import java.util.Map;

import javolution.util.FastMap;
import gameserver.dao.LadderDAO;
import gameserver.dao.PlayerDailiesDAO;
import gameserver.dao.LadderDAO.PlayerLadderData;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.GloryService.PvPMode;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * 
 * <AUTHOR>
 */
public class LadderCommand extends UserCommand {
    private static Map<Integer, Long> lastExecute = new FastMap<Integer, Long>();

    public LadderCommand() {
        super("ladder");
    }

    public void executeCommand(Player player, String param) {
        String[] params = param.split(" ");

        if (lastExecute.containsKey(player.getObjectId())) {
            if ((System.currentTimeMillis() - lastExecute.get(player.getObjectId())) < 10000) {
                PacketSendUtility.sendMessage(player,
                    "You cannot use this command more than every 10 seconds!");
                return;
            }
        }

        PlayerLadderData data = getLadderDAO().getPlayerLadderData(player);
        int rating = data.getRating();
        int rank = data.getRank();
        int wins = data.getWins();
        int losses = data.getLosses();
        int leaves = data.getLeaves();

        String rankString;
        if (rank > 0)
            rankString = "#" + String.valueOf(rank);
        else
            rankString = "N/A";

        StringBuilder sb = new StringBuilder();

        sb.append("Ladder Stats of " + player.getName() + ":\n");
        sb.append("Rank: " + rankString + " with " + rating + " rating\n");
        sb.append("Wins/Losses/Leaves: " + wins + " / " + losses + " / " + leaves + "\n");
        sb.append("Daily BG's: "
            + DAOManager.getDAO(PlayerDailiesDAO.class)
                .getDailyValue(player, PvPMode.DAILY_BG_DONE));

        message(player, sb.toString());

        lastExecute.put(player.getObjectId(), System.currentTimeMillis());
    }

    private void message(Player player, String msg) {
        PacketSendUtility.sendMessage(player, msg);
    }

    private LadderDAO getLadderDAO() {
        return DAOManager.getDAO(LadderDAO.class);
    }
}