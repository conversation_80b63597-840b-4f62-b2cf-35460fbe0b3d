<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:include schemaLocation="../import.xsd"/>
    <xs:include schemaLocation="../global_types.xsd"/>
    <xs:element name="skill_tree">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="import" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="skill" type="SkillTreeEntry" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="SkillTreeEntry">
        <xs:attribute name="classId" type="skillPlayerClass" use="required"/>
        <xs:attribute name="skillId" type="xs:int" use="required"/>
        <xs:attribute name="skillLevel" type="xs:int" use="required"/>
        <xs:attribute name="name" type="xs:string" use="optional"/>
        <xs:attribute name="autolearn" type="xs:boolean"/>
        <xs:attribute name="stigma" type="xs:boolean" default="false"/>
        <xs:attribute name="race" type="skillRace" use="required"/>
        <xs:attribute name="minLevel" type="xs:int" use="required"/>
        <xs:attribute name="type" type="skillUsageType" use="required"/>
		<xs:attribute name="display" type="xs:int" default="0"/>
    </xs:complexType>

    <!-- skillRace is used in item templates also-->
    <xs:simpleType name="skillRace">
        <xs:restriction base="xs:string">
            <xs:enumeration value="PC_LIGHT"/>
            <xs:enumeration value="PC_DARK"/>
            <xs:enumeration value="ALL"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="skillUsageType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ACTIVE"/>
            <xs:enumeration value="PASSIVE"/>
            <xs:enumeration value="TOGGLE"/>
            <xs:enumeration value="MAINTAIN"/>
			<xs:enumeration value="CHARGE"/>
        </xs:restriction>
    </xs:simpleType>

    <!-- skillPlayerClass is used in item templates also-->
    <xs:simpleType name="skillPlayerClass">
        <xs:restriction base="xs:string">
            <xs:enumeration value="WARRIOR"/>
            <xs:enumeration value="FIGHTER"/>
            <xs:enumeration value="KNIGHT"/>
            <xs:enumeration value="SCOUT"/>
            <xs:enumeration value="ASSASSIN"/>
            <xs:enumeration value="RANGER"/>
            <xs:enumeration value="MAGE"/>
            <xs:enumeration value="WIZARD"/>
            <xs:enumeration value="ELEMENTALLIST"/>
            <xs:enumeration value="PRIEST"/>
            <xs:enumeration value="CLERIC"/>
            <xs:enumeration value="CHANTER"/>
			<xs:enumeration value="ENGINEER"/>
			<xs:enumeration value="GUNNER"/>
			<xs:enumeration value="RIDER"/>
			<xs:enumeration value="ARTIST"/>
			<xs:enumeration value="BARD"/>
            <xs:enumeration value="ALL"/>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>