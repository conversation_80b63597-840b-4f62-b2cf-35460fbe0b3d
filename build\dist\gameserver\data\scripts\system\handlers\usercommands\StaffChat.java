/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.model.ChatType;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_MESSAGE;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.UserCommand;
import gameserver.world.World;

/**
 * 
 * <AUTHOR>
 */
public class StaffChat extends UserCommand {
    public StaffChat() {
        super("s");
    }

    public void executeCommand(Player player, String param) {
        if (player.getAccessLevel() < 1)
            return;

        String text = Util.convertLinksInText(param);

        for (Player pl : World.getInstance().getPlayers())
            if (pl.getAccessLevel() > 0)
                PacketSendUtility.sendPacket(pl, new SM_MESSAGE(0, "(STAFF) " + player.getName(),
                    text, ChatType.LEAGUE));
    }
}