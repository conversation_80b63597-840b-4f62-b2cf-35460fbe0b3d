/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.AionClientPacket;
import gameserver.network.factories.AionPacketHandlerFactory;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;

/**
 * 
 * <AUTHOR>
 */

public class SendFakeClientPacket extends AdminCommand {
    public SendFakeClientPacket() {
        super("fcs");
    }

    /**
     * {@inheritDoc}
     */

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_SENDFAKESERVERPACKET) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command");
            return;
        }

        if (params.length < 2) {
            PacketSendUtility.sendMessage(admin, "Syntax: //fcs <opcode> <data>");
            return;
        }

        byte[] opcode = hexStringToByteArray(params[0]);
        byte[] data = hexStringToByteArray(params[1]);

        byte[] packet = new byte[5 + data.length];

        System.arraycopy(opcode, 0, packet, 0, 2);
        System.arraycopy(data, 0, packet, 5, data.length);

        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < packet.length; i++)
            sb.append(String.format("%02X", packet[i]));

        PacketSendUtility.sendMessage(admin, "C2S Packet: " + sb.toString());

        ByteBuffer buffer = ByteBuffer.wrap(packet);
        buffer.order(ByteOrder.LITTLE_ENDIAN);

        AionClientPacket pck = AionPacketHandlerFactory.getInstance().getPacketHandler()
            .handle(buffer, admin.getClientConnection());

        PacketSendUtility.sendMessage(admin,
            "C2S PacketHandler: " + (pck == null ? "null" : pck.toString()));

        if (pck != null)
            admin.getClientConnection().getPacketQueue().execute(pck);
    }

    public static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];

        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4) + Character.digit(
                s.charAt(i + 1), 16));
        }

        return data;
    }
}
