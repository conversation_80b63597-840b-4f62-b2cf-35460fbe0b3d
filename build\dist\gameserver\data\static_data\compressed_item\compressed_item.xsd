<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:include schemaLocation="../import.xsd" />
	
	<xs:element name="compressed_items" type="CompressedItems" />
	<xs:complexType name="CompressedItems">
		<xs:sequence>
			<xs:element ref="import" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="compressed_item" type="CompressedItem" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="CompressedItem">
		<xs:sequence>
			<xs:element name="production" type="Production" minOccurs="1" maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="id" type="xs:int" use="required" />
		<xs:attribute name="maxproduction" type="xs:int" use="optional" />
	</xs:complexType>
	
	<xs:complexType name="Production">
		<xs:attribute name="chance" type="xs:int" use="required"/>
		<xs:attribute name="item_id" type="xs:int" use="required"/>
		<xs:attribute name="count" type="xs:int" use="optional"/>
		<xs:attribute name="min" type="xs:int" use="optional"/>
		<xs:attribute name="max" type="xs:int" use="optional"/>
	</xs:complexType>
</xs:schema>
