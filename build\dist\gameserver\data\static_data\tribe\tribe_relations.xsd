<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	xmlns:jxb="http://java.sun.com/xml/ns/jaxb" jxb:version="2.1">

	<xs:include schemaLocation="../import.xsd" />

	<xs:element name="tribe_relations">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="import" minOccurs="0" maxOccurs="unbounded" />
				<xs:element name="tribe" type="Tribe" minOccurs="0"
					maxOccurs="unbounded" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:complexType name="Tribe">
		<xs:sequence>
			<xs:element name="aggro" type="Aggro" minOccurs="0" maxOccurs="1"/>
			<xs:element name="hostile" type="Hostile" minOccurs="0" maxOccurs="1"/>
			<xs:element name="friend" type="Friend" minOccurs="0" maxOccurs="1"/>
			<xs:element name="neutral" type="Neutral" minOccurs="0" maxOccurs="1"/>
			<xs:element name="support" type="Support" minOccurs="0" maxOccurs="1"/>
		</xs:sequence>
		<xs:attribute name="base" type="xs:string"/>
		<xs:attribute name="name" type="xs:string" use="required"/>
	</xs:complexType>	
	
	<xs:complexType name="Aggro">
		<xs:sequence>
			<xs:element name="to" type="xs:string" minOccurs="1" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="Hostile">
		<xs:sequence>
			<xs:element name="to" type="xs:string" minOccurs="1" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="Friend">
		<xs:sequence>
			<xs:element name="to" type="xs:string" minOccurs="1" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="Neutral">
		<xs:sequence>
			<xs:element name="to" type="xs:string" minOccurs="1" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="Support">
		<xs:sequence>
			<xs:element name="to" type="xs:string" minOccurs="1" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>

</xs:schema>