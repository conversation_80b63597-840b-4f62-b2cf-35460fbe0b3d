/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package mysql5;

import gameserver.dao.InventoryDAO;
import gameserver.dataholders.DataManager;
import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.PersistentState;
import gameserver.model.gameobjects.player.Equipment;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.player.Storage;
import gameserver.model.gameobjects.player.StorageType;
import gameserver.model.templates.item.ItemTemplate;
import gameserver.services.ItemService;
import gameserver.services.RentalService;
import gameserver.utils.idfactory.IDFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.DB;
import com.aionemu.commons.database.DatabaseFactory;

/**
 * <AUTHOR>
 */
public class MySQL5InventoryDAO extends InventoryDAO {
    private static final Logger log = Logger.getLogger(MySQL5InventoryDAO.class);

    public static final String SELECT_QUERY = "SELECT * FROM `inventory` WHERE `itemOwner`=? AND `itemLocation`=? AND `isEquiped`=?";
    public static final String INSERT_QUERY = "INSERT INTO `inventory` (`itemUniqueId`, `itemId`, `itemCount`, `itemColor`, `itemOwner`, `isEquiped`, isSoulBound, `slot`, `itemLocation`, `enchant`, `itemCreator`, `itemSkin`, `fusionedItem`, `optionalSocket`, `optionalFusionSocket`, `conditioning`, `temperance`, `randomOption`, `bonusEnchant`, `randomFusionOption`, `wrapped`, `expireTime`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    public static final String UPDATE_QUERY = "UPDATE `inventory` SET  `itemCount`=?, `itemColor`=?, `itemOwner`=?, `isEquiped`=?, `isSoulBound`=?, `slot`=?, `itemLocation`=?, `enchant`=?, `itemCreator`=?, `itemSkin`=?, `fusionedItem`=?, `optionalSocket`=?, `optionalFusionSocket`=?, `conditioning`=?, `temperance`=?, `randomOption`=?, `bonusEnchant`=?, `randomFusionOption`=?, `wrapped`=?, `expireTime`=? WHERE `itemUniqueId`=?";
    public static final String DELETE_QUERY = "DELETE FROM `inventory` WHERE `itemUniqueId`=?";
    public static final String DELETE_CLEAN_QUERY = "DELETE FROM `inventory` WHERE `itemOwner`=? AND (`itemLocation`=0 OR `itemLocation`=1)";
    public static final String SELECT_ACCOUNT_QUERY = "SELECT `account_id` FROM `players` WHERE `id`=?";
    public static final String SELECT_LEGION_QUERY = "SELECT `legion_id` FROM `legion_members` WHERE `player_id`=?";

    @Override
    public Storage loadStorage(Player player, StorageType storageType) {
        final Storage inventory = new Storage(player, storageType);
        int playerId = player.getObjectId();
        final int storage = storageType.getId();
        final int equipped = 0;

        if (storageType == StorageType.ACCOUNT_WAREHOUSE) {
            playerId = getPlayerAccountId(playerId);
        }

        final int owner = playerId;
        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con.prepareStatement(SELECT_QUERY);
            stmt.setInt(1, owner);
            stmt.setInt(2, storage);
            stmt.setInt(3, equipped);
            ResultSet rset = stmt.executeQuery();
            while (rset.next()) {
                int itemUniqueId = rset.getInt("itemUniqueId");
                int itemId = rset.getInt("itemId");
                long itemCount = rset.getLong("itemCount");
                int itemColor = rset.getInt("itemColor");
                int isEquiped = rset.getInt("isEquiped");
                int isSoulBound = rset.getInt("isSoulBound");
                long slot = rset.getLong("slot");
                int enchant = rset.getInt("enchant");
                int itemSkin = rset.getInt("itemSkin");
                int fusionedItem = rset.getInt("fusionedItem");
                int optionalSocket = rset.getInt("optionalSocket");
                int optionalFusionSocket = rset.getInt("optionalFusionSocket");
                int conditioning = rset.getInt("conditioning");
                int temperanceLevel = rset.getInt("temperance");
                int randomOption = rset.getInt("randomOption");
                int bonusEnchant = rset.getInt("bonusEnchant");
                int randomFusionOption = rset.getInt("randomFusionOption");
                boolean wrapped = rset.getBoolean("wrapped");
                Timestamp expireTime;
                try {
                    expireTime = rset.getTimestamp("expireTime");
                }
                catch (SQLException e) {
                    expireTime = null;
                }
                String itemCreator = rset.getString("itemCreator");
                Item item = new Item(itemUniqueId, itemId, itemCount, itemColor, itemCreator,
                    (isEquiped == 1), (isSoulBound == 1), slot, storage, enchant, itemSkin,
                    fusionedItem, optionalSocket, optionalFusionSocket, conditioning,
                    temperanceLevel, randomOption, bonusEnchant, randomFusionOption, wrapped, expireTime);
                item.setPersistentState(PersistentState.UPDATED);
                inventory.onLoadHandler(item);

                if (RentalService.getInstance().isRentalItem(item))
                    RentalService.getInstance().addRentalItem(owner, item);
            }
            rset.close();
            stmt.close();
        }
        catch (Exception e) {
            log.fatal(
                "Could not restore storage data for player: " + playerId + " from DB: "
                    + e.getMessage(), e);
        }
        finally {
            DatabaseFactory.close(con);
        }
        return inventory;
    }

    @Override
    public Equipment loadEquipment(Player player) {
        final Equipment equipment = new Equipment(player);

        int playerId = player.getObjectId();
        final int storage = 0;
        final int equipped = 1;
        final int owner = playerId;

        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con.prepareStatement(SELECT_QUERY);
            stmt.setInt(1, owner);
            stmt.setInt(2, storage);
            stmt.setInt(3, equipped);
            ResultSet rset = stmt.executeQuery();
            while (rset.next()) {
                int itemUniqueId = rset.getInt("itemUniqueId");
                int itemId = rset.getInt("itemId");
                long itemCount = rset.getLong("itemCount");
                int itemColor = rset.getInt("itemColor");
                int isSoulBound = rset.getInt("isSoulBound");
                long slot = rset.getLong("slot");
                int enchant = rset.getInt("enchant");
                int itemSkin = rset.getInt("itemSkin");
                int fusionedItem = rset.getInt("fusionedItem");
                int optionalSocket = rset.getInt("optionalSocket");
                int optionalFusionSocket = rset.getInt("optionalFusionSocket");
                int conditioning = rset.getInt("conditioning");
                int temperanceLevel = rset.getInt("temperance");
                int randomOption = rset.getInt("randomOption");
                int bonusEnchant = rset.getInt("bonusEnchant");
                int randomFusionOption = rset.getInt("randomFusionOption");
                boolean wrapped = rset.getBoolean("wrapped");
                Timestamp expireTime;
                try {
                    expireTime = rset.getTimestamp("expireTime");
                }
                catch (SQLException e) {
                    expireTime = null;
                }
                String itemCreator = rset.getString("itemCreator");
                Item item = new Item(itemUniqueId, itemId, itemCount, itemColor, itemCreator, true,
                    (isSoulBound == 1), slot, storage, enchant, itemSkin, fusionedItem,
                    optionalSocket, optionalFusionSocket, conditioning, temperanceLevel,
                    randomOption, bonusEnchant, randomFusionOption, wrapped, expireTime);
                item.setPersistentState(PersistentState.UPDATED);
                equipment.onLoadHandler(item);

                if (RentalService.getInstance().isRentalItem(item))
                    RentalService.getInstance().addRentalItem(owner, item);
            }
            rset.close();
            stmt.close();
        }
        catch (Exception e) {
            log.fatal(
                "Could not restore Equipment data for player: " + playerId + " from DB: "
                    + e.getMessage(), e);
        }
        finally {
            DatabaseFactory.close(con);
        }
        return equipment;
    }

    @Override
    public List<Item> loadEquipment(int playerId) {
        final List<Item> items = new ArrayList<Item>();
        final int storage = 0;
        final int equipped = 1;
        final int owner = playerId;

        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con.prepareStatement(SELECT_QUERY);
            stmt.setInt(1, owner);
            stmt.setInt(2, storage);
            stmt.setInt(3, equipped);
            ResultSet rset = stmt.executeQuery();
            while (rset.next()) {
                int itemUniqueId = rset.getInt("itemUniqueId");
                int itemId = rset.getInt("itemId");
                long itemCount = rset.getLong("itemCount");
                int itemColor = rset.getInt("itemColor");
                int isSoulBound = rset.getInt("isSoulBound");
                long slot = rset.getLong("slot");
                int enchant = rset.getInt("enchant");
                int itemSkin = rset.getInt("itemSkin");
                int fusionedItem = rset.getInt("fusionedItem");
                int optionalSocket = rset.getInt("optionalSocket");
                int optionalFusionSocket = rset.getInt("optionalFusionSocket");
                int conditioning = rset.getInt("conditioning");
                int temperanceLevel = rset.getInt("temperance");
                int randomOption = rset.getInt("randomOption");
                int bonusEnchant = rset.getInt("bonusEnchant");
                int randomFusionOption = rset.getInt("randomFusionOption");
                boolean wrapped = rset.getBoolean("wrapped");
                Timestamp expireTime;
                try {
                    expireTime = rset.getTimestamp("expireTime");
                }
                catch (SQLException e) {
                    expireTime = null;
                }
                String itemCreator = rset.getString("itemCreator");
                Item item = new Item(itemUniqueId, itemId, itemCount, itemColor, itemCreator, true,
                    (isSoulBound == 1), slot, storage, enchant, itemSkin, fusionedItem,
                    optionalSocket, optionalFusionSocket, conditioning, temperanceLevel,
                    randomOption, bonusEnchant, randomFusionOption, wrapped, expireTime);
                items.add(item);

                if (RentalService.getInstance().isRentalItem(item))
                    RentalService.getInstance().addRentalItem(owner, item);
            }
            rset.close();
            stmt.close();
        }
        catch (Exception e) {
            log.fatal(
                "Could not restore Equipment data for player: " + playerId + " from DB: "
                    + e.getMessage(), e);
        }
        finally {
            DatabaseFactory.close(con);
        }
        return items;
    }

    public int getPlayerAccountId(final int playerId) {
        Connection con = null;
        int accountId = 0;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con.prepareStatement(SELECT_ACCOUNT_QUERY);
            stmt.setInt(1, playerId);
            ResultSet rset = stmt.executeQuery();
            if (rset.next()) {
                accountId = rset.getInt("account_id");
            }
            rset.close();
            stmt.close();
        }
        catch (Exception e) {
            log.fatal(
                "Could not restore accountId data for player: " + playerId + " from DB: "
                    + e.getMessage(), e);
        }
        finally {
            DatabaseFactory.close(con);
        }
        return accountId;
    }

    public int getLegionId(final int playerId) {
        Connection con = null;
        int legionId = 0;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con.prepareStatement(SELECT_LEGION_QUERY);
            stmt.setInt(1, playerId);
            ResultSet rset = stmt.executeQuery();
            if (rset.next()) {
                legionId = rset.getInt("legion_id");
            }
            rset.close();
            stmt.close();
        }
        catch (Exception e) {
            log.fatal(
                "Could not restore legionId data for player: " + playerId + " from DB: "
                    + e.getMessage(), e);
        }
        finally {
            DatabaseFactory.close(con);
        }
        return legionId;
    }

    @Override
    public boolean store(Player player) {
        int playerId = player.getObjectId();

        List<Item> allPlayerItems = player.getDirtyItemsToUpdate();

        boolean resultSuccess = true;
        for (Item item : allPlayerItems) {
            if (item != null)
                resultSuccess = store(item, playerId);
        }
        return resultSuccess;
    }

    /**
     * @param item
     *            The item that needs to be stored
     * @param ownerId
     *            The playerObjectId of the owner of the item
     * @return true if storing succeeded
     */
    @Override
    public boolean store(final Item item, int ownerId) {

        boolean result = false;
        
        if (item.isTemporary())
            return result;

        if (item.getItemLocation() == StorageType.ACCOUNT_WAREHOUSE.getId()) {
            ownerId = getPlayerAccountId(ownerId);
        }

        if (item.getItemLocation() == StorageType.LEGION_WAREHOUSE.getId()) {
            if (getLegionId(ownerId) > 0)
                ownerId = getLegionId(ownerId);
        }

        switch (item.getPersistentState()) {
            case NEW:
                result = insertItem(item, ownerId);
                break;
            case UPDATE_REQUIRED:
                result = updateItem(item, ownerId);
                break;
            case DELETED:
                result = deleteItem(item);
                break;
        }
        item.setPersistentState(PersistentState.UPDATED);
        return result;
    }

    /**
     * @param item
     * @param playerId
     * @return
     */
    private boolean insertItem(final Item item, final int ownerId) {
        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con.prepareStatement(INSERT_QUERY);
            stmt.setInt(1, item.getObjectId());
            stmt.setInt(2, item.getItemTemplate().getTemplateId());
            stmt.setLong(3, item.getItemCount());
            stmt.setInt(4, item.getItemColor());
            stmt.setInt(5, ownerId);
            stmt.setBoolean(6, item.isEquipped());
            stmt.setInt(7, item.isSoulBound() ? 1 : 0);
            stmt.setLong(8, item.getEquipmentSlot());
            stmt.setInt(9, item.getItemLocation());
            stmt.setInt(10, item.getEnchantLevel());
            stmt.setString(11, item.getItemCreator());
            stmt.setInt(12, item.getItemSkinTemplate().getTemplateId());
            stmt.setInt(13, item.getFusionedItem());
            stmt.setInt(14, item.getOptionalSocket());
            stmt.setInt(15, item.getOptionalFusionSocket());
            stmt.setInt(16, item.getConditioning());
            stmt.setInt(17, item.getTemperanceLevel());
            stmt.setInt(18, item.getRandomOption());
            stmt.setInt(19, item.getBonusEnchant());
            stmt.setInt(20, item.getRandomFusionOption());
            stmt.setBoolean(21, item.isWrapped());
            if (item.getExpireTime() == null)
                stmt.setNull(22, Types.TIMESTAMP);
            else
                stmt.setTimestamp(22, item.getExpireTime());
            stmt.execute();
            stmt.close();
        }
        catch (Exception e) {
            log.error("Error insert item ItemObjId: " + item.getObjectId(), e);
            return false;
        }
        finally {
            DatabaseFactory.close(con);
        }
        return true;
    }

    /**
     * @param item
     * @return
     */
    private boolean updateItem(final Item item, final int ownerId) {
        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con.prepareStatement(UPDATE_QUERY);
            stmt.setLong(1, item.getItemCount());
            stmt.setInt(2, item.getItemColor());
            stmt.setInt(3, ownerId);
            stmt.setBoolean(4, item.isEquipped());
            stmt.setInt(5, item.isSoulBound() ? 1 : 0);
            stmt.setLong(6, item.getEquipmentSlot());
            stmt.setInt(7, item.getItemLocation());
            stmt.setInt(8, item.getEnchantLevel());
            stmt.setString(9, item.getItemCreator());
            stmt.setInt(10, item.getItemSkinTemplate().getTemplateId());
            stmt.setInt(11, item.getFusionedItem());
            stmt.setInt(12, item.getOptionalSocket());
            stmt.setInt(13, item.getOptionalFusionSocket());
            stmt.setInt(14, item.getConditioning());
            stmt.setInt(15, item.getTemperanceLevel());
            stmt.setInt(16, item.getRandomOption());
            stmt.setInt(17, item.getBonusEnchant());
            stmt.setInt(18, item.getRandomFusionOption());
            stmt.setBoolean(19, item.isWrapped());
            if (item.getExpireTime() == null)
                stmt.setNull(20, Types.TIMESTAMP);
            else
                stmt.setTimestamp(20, item.getExpireTime());
            stmt.setInt(21, item.getObjectId());
            stmt.execute();
            stmt.close();
        }
        catch (Exception e) {
            log.error("Error update item ItemObjId: " + item.getObjectId(), e);
            return false;
        }
        finally {
            DatabaseFactory.close(con);
        }
        return true;
    }

    /**
     * @param item
     */
    private boolean deleteItem(final Item item) {
        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con.prepareStatement(DELETE_QUERY);
            stmt.setInt(1, item.getObjectId());
            stmt.execute();
            stmt.close();
            ItemService.releaseItemId(item);
        }
        catch (Exception e) {
            log.error("Error delete item. ItemObjId: " + item.getObjectId(), e);
            return false;
        }
        finally {
            DatabaseFactory.close(con);
        }
        return true;
    }

    /**
     * 
     */
    public int removeItemCount(int itemUniqueId, int itemOwner, int amount) {
        int removed = 0;
        Connection con = null;

        try {
            con = DatabaseFactory.getConnection();

            PreparedStatement stmt = con
                .prepareStatement("SELECT itemCount FROM inventory WHERE itemUniqueId = ? AND itemOwner = ?");
            stmt.setInt(1, itemUniqueId);
            stmt.setInt(2, itemOwner);

            ResultSet rset = stmt.executeQuery();
            if (!rset.next())
                return 0;

            long itemCount = rset.getLong("itemCount");

            PreparedStatement st;
            if (itemCount <= amount) {
                st = con.prepareStatement("DELETE FROM inventory WHERE itemUniqueId = ?");
                st.setInt(1, itemUniqueId);
                st.executeUpdate();

                removed = (int) itemCount;
            }
            else {
                st = con
                    .prepareStatement("UPDATE inventory SET itemCount = itemCount - ? WHERE itemUniqueId = ?");
                st.setInt(1, amount);
                st.setInt(2, itemUniqueId);
                st.executeUpdate();

                removed = amount;
            }
        }
        catch (Exception e) {
            log.error("Error removing item count. ItemUniqueId: " + itemUniqueId, e);
            return 0;
        }
        finally {
            DatabaseFactory.close(con);
        }

        return removed;
    }

    /**
     * 
     */
    public boolean addItemCount(int itemId, int itemOwner, String ownerName, long amount) {
        ItemTemplate template = DataManager.ITEM_DATA.getItemTemplate(itemId);
        if (template == null)
            return false;

        Connection con = null;

        long count = amount;

        try {
            con = DatabaseFactory.getConnection();

            PreparedStatement stmt = con
                .prepareStatement("SELECT itemUniqueId,itemCount FROM inventory WHERE itemOwner = ? AND itemLocation = ? AND itemId = ?");
            stmt.setInt(1, itemOwner);
            stmt.setInt(2, StorageType.CUBE.getId());
            stmt.setInt(3, itemId);

            ResultSet rset = stmt.executeQuery();

            while (rset.next()) {
                long itemCount = rset.getInt("itemCount");

                if (itemCount >= template.getMaxStackCount())
                    continue;

                int itemUniqueId = rset.getInt("itemUniqueId");

                long add = (count + itemCount > template.getMaxStackCount()) ? (template
                    .getMaxStackCount() - itemCount) : (count);

                PreparedStatement st = con
                    .prepareStatement("UPDATE inventory SET itemCount = itemCount + ? WHERE itemUniqueId = ?");
                st.setLong(1, add);
                st.setInt(2, itemUniqueId);

                if (st.executeUpdate() > 0)
                    count -= add;
            }

            while (count > 0) {
                long add = (count > template.getMaxStackCount()) ? template.getMaxStackCount()
                    : count;

                Item item = new Item(IDFactory.getInstance().nextItemId(), itemId, template, add,
                    ownerName, false, 0);

                if (template.isWeapon() || template.isArmor()) {
                    if (template.getRandomOption() != 0)
                        item.setOptionalSocket(-1);
                    else {
                        item.setOptionalSocket(template.getOptionSlotBonus());
                        item.setBonusEnchant(template.getMaxEnchantBonus());
                    }
                }

                if (insertItem(item, itemOwner))
                    count -= add;
            }
        }
        catch (Exception e) {
            log.error("Error adding item " + itemId + " to " + itemOwner, e);
            return false;
        }
        finally {
            DatabaseFactory.close(con);
        }

        return true;
    }

    /**
     * Since inventory is not using FK - need to clean items
     */
    @Override
    public boolean deletePlayerItems(final int playerId) {
        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stmt = con.prepareStatement(DELETE_CLEAN_QUERY);
            stmt.setInt(1, playerId);
            stmt.execute();
            stmt.close();
        }
        catch (Exception e) {
            log.error("Error Player all items. PlayerObjId: " + playerId, e);
            return false;
        }
        finally {
            DatabaseFactory.close(con);
        }
        return true;
    }

    @Override
    public int[] getUsedIDs() {
        PreparedStatement statement = DB.prepareStatement("SELECT itemUniqueId FROM inventory",
            ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);

        try {
            ResultSet rs = statement.executeQuery();
            rs.last();
            int count = rs.getRow();
            rs.beforeFirst();
            int[] ids = new int[count];
            for (int i = 0; i < count; i++) {
                rs.next();
                ids[i] = rs.getInt("itemUniqueId");
            }
            return ids;
        }
        catch (SQLException e) {
            log.error("Can't get list of id's from inventory table", e);
        }
        finally {
            DB.close(statement);
        }

        return new int[0];
    }

    @Override
    public boolean updateItemOwnership(int itemUniqueId, int ownerId, long itemCount, long slot, int itemLocation) {
        PreparedStatement ps = DB.prepareStatement("UPDATE inventory SET itemOwner = ?, itemCount = ?, slot = ?, itemLocation = ? WHERE itemUniqueId = ?");

        try {
            ps.setInt(1, ownerId);
            ps.setLong(2, itemCount);
            ps.setLong(3, slot);
            ps.setInt(4, itemLocation);
            ps.setInt(5, itemUniqueId);

            ps.executeUpdate();
        }
        catch (Exception e) {
            log.error("Error updating item ownership", e);
            return false;
        }
        finally {
            DB.close(ps);
        }

        return true;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean supports(String s, int i, int i1) {
        return MySQL5DAOUtils.supports(s, i, i1);
    }
}
