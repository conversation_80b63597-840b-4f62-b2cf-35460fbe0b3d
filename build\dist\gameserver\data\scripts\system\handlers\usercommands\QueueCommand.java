/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.model.gameobjects.player.Player;
import gameserver.services.EventService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

/**
 * 
 * <AUTHOR>
 */
public class QueueCommand extends UserCommand {

    public QueueCommand() {
        super("queue");
    }

    public void executeCommand(Player player, String param) {
        if (player.isTemporary())
            return;

        if (player.getBattleground() != null) {
            PacketSendUtility.sendMessage(player,
                "You cannot queue for an event while in a Battleground.");
            return;
        }

        if (!EventService.getInstance().isEventQueueOn()) {
            PacketSendUtility.sendMessage(player, "There is no event to queue for.");
            return;
        }

        if (EventService.getInstance().getEventQueue().contains(player)) {
            PacketSendUtility.sendMessage(player, "You are already registered for the event!");
            return;
        }
        else if (EventService.getInstance().addToEventQueue(player)) {
            PacketSendUtility.sendMessage(player, "You have been registered for the event!");
            return;
        }
        else {
            PacketSendUtility.sendMessage(player,
                "There is no more room in the event. Better luck next time.");
            return;
        }
    }
}