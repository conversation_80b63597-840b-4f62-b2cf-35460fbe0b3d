/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package admincommands;

import gameserver.model.DescriptionId;
import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.PersistentState;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import gameserver.network.aion.serverpackets.SM_UPDATE_ITEM;
import gameserver.services.EnchantService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;

import java.util.List;

/**
 * <AUTHOR>
 * 
 */
public class Temperance extends AdminCommand {

    public Temperance() {
        super("temperance");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < 5) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command.");
            return;
        }

        if (params.length < 2) {
            PacketSendUtility.sendMessage(admin,
                "Syntax: //temperance <item> <level> [lowest | highest]");
            return;
        }

        int itemId = Integer.parseInt(params[0]);
        int level = Integer.parseInt(params[1]);

        List<Item> items = admin.getEquipment().getEquippedItemsByItemId(itemId);
        if (items.isEmpty())
            items = admin.getInventory().getAllItemsByItemId(itemId);

        if (items.isEmpty()) {
            PacketSendUtility.sendMessage(admin,
                "You do not have the item equipped nor in your inventory.");
            return;
        }

        int mode = 0;

        if (params.length >= 3) {
            if ("lowest".startsWith(params[2].toLowerCase()))
                mode = 1;
            else if ("highest".startsWith(params[2].toLowerCase()))
                mode = 2;
        }

        Item selectedItem = null;
        boolean breakout = false;

        for (Item item : items) {
            if (item.getItemTemplate().getMaxTemperance() == 0)
                continue;

            switch (mode) {
                case 0:
                    if (selectedItem == null) {
                        selectedItem = item;
                        breakout = true;
                    }
                    break;
                case 1:
                    if (selectedItem != null
                        && selectedItem.getTemperanceLevel() > item.getTemperanceLevel())
                        selectedItem = item;
                    else if (selectedItem == null)
                        selectedItem = item;
                    break;
                case 2:
                    if (selectedItem != null
                        && selectedItem.getTemperanceLevel() < item.getTemperanceLevel())
                        selectedItem = item;
                    else if (selectedItem == null)
                        selectedItem = item;
                    break;
            }

            if (breakout)
                break;
        }

        if (selectedItem == null) {
            PacketSendUtility.sendMessage(admin, "Couldn't find a suitable item.");
            return;
        }

        if (selectedItem.isEquipped())
            EnchantService.onItemUnequip(admin, selectedItem);

        selectedItem.setTemperanceLevel(Math.min(level, selectedItem.getItemTemplate()
            .getMaxTemperance()));

        if (selectedItem.isEquipped() && !selectedItem.isWeaponSwapped(admin))
            EnchantService.onItemEquip(admin, selectedItem);

        PacketSendUtility.sendPacket(admin, new SM_UPDATE_ITEM(selectedItem));

        if (selectedItem.isEquipped())
            admin.getEquipment().setPersistentState(PersistentState.UPDATE_REQUIRED);
        else
            admin.getInventory().setPersistentState(PersistentState.UPDATE_REQUIRED);

        PacketSendUtility.sendPacket(
            admin,
            SM_SYSTEM_MESSAGE.STR_MSG_ITEM_AUTHORIZE_SUCCEEDED(
                new DescriptionId(selectedItem.getNameID()), level));
    }
}
