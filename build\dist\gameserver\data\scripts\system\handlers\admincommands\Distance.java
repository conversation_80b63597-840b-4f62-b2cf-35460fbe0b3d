/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.geoEngine2.GeoEngine2;
import gameserver.model.gameobjects.player.Player;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;

/**
 * <AUTHOR>
 * 
 */
public class Distance extends AdminCommand {

    public Distance() {
        super("distance");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.GM_LEVEL) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command.");
            return;
        }

        if (params.length < 1) {
            PacketSendUtility.sendMessage(admin,
                "Syntax: //distance <location link> - remember to put link in \"quotation\" signs."
                    + "\nOR Syntax: //distance target - reports distance to target");
            return;
        }
        else if (!params[0].startsWith("[pos:") && !"target".equalsIgnoreCase(params[0])) {
            PacketSendUtility.sendMessage(admin,
                "Please specify a proper location link in quotation signs.");
            return;
        }

        int worldId;
        float x, y, z;

        try {
            if ("target".equalsIgnoreCase(params[0])) {
                worldId = admin.getTarget().getWorldId();
                x = admin.getTarget().getX();
                y = admin.getTarget().getY();
                z = admin.getTarget().getZ();
            }
            else {
                String loc = params[0].substring(3 + params[0].indexOf(';'));
                String[] l = loc.split(" ");
                worldId = Integer.parseInt(l[0]);
                x = Float.parseFloat(l[1]);
                y = Float.parseFloat(l[2]);
                z = GeoEngine2.getInstance().getHighestZ(worldId, x, y);
            }
        }
        catch (Exception e) {
            PacketSendUtility.sendMessage(admin, "Failed parsing your specified location link.");
            return;
        }

        if (worldId != admin.getWorldId()) {
            PacketSendUtility.sendMessage(admin,
                "Cannot compute distance to a location in another map.");
            return;
        }

        double distance2D = MathUtil.getDistance(admin.getX(), admin.getY(), x, y);
        double distance3D = MathUtil.getDistance(admin, x, y, z);

        PacketSendUtility.sendMessage(admin, String.format("Distance to %.02f %.02f %.02f\n"
            + "- 2D: %.02f\n" + "- 3D: %.02f\n", x, y, z, distance2D, distance3D));
    }
}
