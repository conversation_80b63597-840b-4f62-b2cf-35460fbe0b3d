/*
 * This file is part of aion-emu <aion-emu.com>.
 *
 *  aion-emu is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  aion-emu is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with aion-emu.  If not, see <http://www.gnu.org/licenses/>.
 */
package mysql5;

import java.sql.PreparedStatement;
import java.sql.ResultSet;

import gameserver.dao.WeekliesDAO;
import gameserver.services.GloryService.PvPMode;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.DB;

/**
 * <AUTHOR>
 * 
 */
public class MySQL5WeekliesDAO extends WeekliesDAO {
    private static final Logger log = Logger.getLogger(MySQL5WeekliesDAO.class);

    @Override
    public int getWeeklyValue(int objectId, WeeklyType type) {
        PreparedStatement ps = DB
            .prepareStatement("SELECT value FROM weeklies WHERE object_id = ? AND type = ?");

        try {
            ps.setInt(1, objectId);
            ps.setInt(2, type.getId());

            ResultSet rs = ps.executeQuery();

            if (rs.next())
                return rs.getInt("value");
        }
        catch (Exception e) {
            log.error("Error getting Weekly value: ", e);
        }
        finally {
            DB.close(ps);
        }

        return 0;
    }

    @Override
    public boolean addWeeklyValue(int objectId, WeeklyType type, int value) {
        PreparedStatement ps = DB
            .prepareStatement("INSERT INTO weeklies (object_id, type, value) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE value = value + ?");

        try {
            ps.setInt(1, objectId);
            ps.setInt(2, type.getId());
            ps.setInt(3, value);
            ps.setInt(4, value);

            return ps.executeUpdate() > 0;
        }
        catch (Exception e) {
            log.error("Error adding Weekly value: ", e);
        }
        finally {
            DB.close(ps);
        }

        return false;
    }

    @Override
    public boolean resetWeeklies(int objectId, WeeklyType... types) {
        for (WeeklyType type : types) {
            PreparedStatement ps = DB
                .prepareStatement("DELETE FROM weeklies WHERE type = ? AND object_id = ?");

            try {
                ps.setInt(1, type.getId());
                ps.setInt(2, objectId);

                ps.executeUpdate();
            }
            catch (Exception e) {
                log.error("Error resetting Weekly values: ", e);
            }
            finally {
                DB.close(ps);
            }
        }

        return true;
    }

    @Override
    public boolean resetWeeklies(WeeklyType... types) {
        for (WeeklyType type : types) {
            PreparedStatement ps = DB.prepareStatement("DELETE FROM weeklies WHERE type = ?");

            try {
                ps.setInt(1, type.getId());

                ps.executeUpdate();
            }
            catch (Exception e) {
                log.error("Error resetting Weekly values: ", e);
            }
            finally {
                DB.close(ps);
            }
        }

        return true;
    }

    @Override
    public boolean resetAllWeeklies() {
        PreparedStatement ps = DB.prepareStatement("DELETE FROM weeklies");

        try {
            ps.executeUpdate();
        }
        catch (Exception e) {
            log.error("Error resetting Weekly values: ", e);
        }
        finally {
            DB.close(ps);
        }

        return true;
    }

    @Override
    public boolean supports(String databaseName, int majorVersion, int minorVersion) {
        return MySQL5DAOUtils.supports(databaseName, majorVersion, minorVersion);
    }
}
