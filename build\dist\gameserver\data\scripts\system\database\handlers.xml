<?xml version='1.0' encoding='UTF-8' ?>
<!--
  ~ This file is part of Aion X Emu <aionxemu.com>
  ~
  ~ aion-engine is Private Software: you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by
  ~ the Private Software Foundation, either version 3 of the License, or
  ~ (at your option) any later version.
  ~
  ~ This software is distributed in the hope that it will be useful,
  ~ but WITHOUT ANY WARRANTY; without even the implied warranty of
  ~ MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  ~ GNU General Public License for more details.
  ~
  ~ You should have received a copy of the GNU General Public License
  ~ along with this software. If not, see <http://www.gnu.org/licenses/>.
  -->

<scriptlist xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
            xsi:noNamespaceSchemaLocation="../contexts.xsd">

    <scriptinfo root="./data/scripts/system/handlers/admincommands"/>
    <scriptinfo root="./data/scripts/system/handlers/usercommands"/>

</scriptlist>