<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:include schemaLocation="../import.xsd"/>

	<xs:element name="polymorphs" type="Polymorphs"/>

	<xs:complexType name="Polymorphs">
		<xs:sequence>
			<xs:element name="polymorph" type="Polymorph" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="Polymorph">
		<xs:sequence>
			<xs:element name="skill" type="Skill" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="id" type="xs:int"/>
	</xs:complexType>

	<xs:complexType name="Skill">
		<xs:attribute name="skill_id" type="xs:int"/>
		<xs:attribute name="level" type="xs:int"/>
	</xs:complexType>
</xs:schema>
