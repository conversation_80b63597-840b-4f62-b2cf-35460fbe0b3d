/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.dao.BannedMacDAO;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.BannedMacEntry;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.World;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * <AUTHOR>
 */
public class BanMac extends AdminCommand {

    public BanMac() {
        super("banmac");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_BAN) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command!");
            return;
        }

        if (params == null || params.length < 2) {
            PacketSendUtility.sendMessage(admin, "Help: //banmac <player> <reason>");
            PacketSendUtility.sendMessage(admin, "Help: This ban is permanent!");
            return;
        }

        Player player = World.getInstance().findPlayer(Util.convertName(params[0]));
        if (player == null) {
            PacketSendUtility.sendMessage(admin,
                "Error! You can only ban online players with this command.");
            return;
        }

        String details = String.format("%s (%s) banned %s (%s) with reason: ", admin.getName(),
            admin.getAccountName(), player.getName(), player.getAccountName());

        for (int i = 1; i < params.length; i++)
            details += params[i] + " ";

        DAOManager.getDAO(BannedMacDAO.class).update(
            new BannedMacEntry(player.getClientConnection().getMacAddress(), details));

        PacketSendUtility.sendMessage(admin, player.getName() + " has been MAC-banned!");

        player.getClientConnection().close(true);
    }
}
