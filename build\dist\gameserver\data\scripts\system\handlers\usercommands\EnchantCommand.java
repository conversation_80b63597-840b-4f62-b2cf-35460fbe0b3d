/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.configs.network.NetworkConfig;
import gameserver.dao.MightDAO;
import gameserver.dao.ShopDAO;
import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.PersistentState;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.templates.item.ArmorType;
import gameserver.network.aion.serverpackets.SM_UPDATE_ITEM;
import gameserver.services.EnchantService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

import java.util.Map;

import javolution.util.FastMap;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * 
 * <AUTHOR>
 */
public class EnchantCommand extends UserCommand {
    private static Map<Integer, Long> lastExecute = new FastMap<Integer, Long>();

    private int enchantCost = 5;

    public EnchantCommand() {
        super("enchant");

        if (NetworkConfig.GAMESERVER_ID == 100)
            enchantCost = 0;
    }

    public void executeCommand(Player player, String param) {
        String[] params = param.split(" ");

        if (player.isTemporary())
            return;

        if (lastExecute.containsKey(player.getObjectId())) {
            if ((System.currentTimeMillis() - lastExecute.get(player.getObjectId())) < 3000) {
                PacketSendUtility.sendMessage(player,
                    "You cannot use this command more than every 3 seconds!");
                return;
            }
        }

        if (params.length < 1 || params[0] == "") {
            PacketSendUtility.sendMessage(player, "Syntax: .enchant <times>"
                + "\nSyntax: This will enchant all your equipment by <times> for " + enchantCost
                + " Might per +");
            return;
        }
        else {
            int times;
            try {
                times = Integer.parseInt(params[0]);
            }
            catch (NumberFormatException e) {
                PacketSendUtility
                    .sendMessage(player,
                        "Error! Please use a number to indicate how many times you want your equipment to be enchanted.");
                return;
            }

            if (times < 1) {
                PacketSendUtility.sendMessage(player, "Error! Please specify a number above 0");
                return;
            }

            purchaseEnchantAll(player, times);
        }

        lastExecute.put(player.getObjectId(), System.currentTimeMillis());
    }

    private void purchaseEnchantAll(Player player, int times) {
        int might = DAOManager.getDAO(MightDAO.class).getMight(player);
        if (might < (enchantCost * times) || times > 15 || times < 1) {
            PacketSendUtility
                .sendMessage(
                    player,
                    "Error! You do not have enough Might to purchase the Full-gear Enchant or you specified a wrong number!");
            return;
        }

        int mightCost = 0;

        for (Item targetItem : player.getEquipment().getEquippedItemsWithoutStigma()) {
            if (isUpgradeable(targetItem)) {
                if (targetItem.getEnchantLevel() >= targetItem.getItemTemplate().getMaxEnchant()
                    + targetItem.getBonusEnchant())
                    continue;

                if ((targetItem.getEnchantLevel() + times) > targetItem.getItemTemplate()
                    .getMaxEnchant() + targetItem.getBonusEnchant()) {
                    if ((mightCost + enchantCost
                        * ((targetItem.getItemTemplate().getMaxEnchant() + targetItem
                            .getBonusEnchant()) - targetItem.getEnchantLevel())) > might)
                        break;

                    mightCost += enchantCost
                        * ((targetItem.getItemTemplate().getMaxEnchant() + targetItem
                            .getBonusEnchant()) - targetItem.getEnchantLevel());

                    if (targetItem.isEquipped())
                        EnchantService.onItemUnequip(player, targetItem);
                    targetItem.setEnchantLevel(targetItem.getItemTemplate().getMaxEnchant()
                        + targetItem.getBonusEnchant());
                    if (targetItem.isEquipped() && !targetItem.isWeaponSwapped(player))
                        EnchantService.onItemEquip(player, targetItem);

                    PacketSendUtility.sendPacket(player, new SM_UPDATE_ITEM(targetItem));

                    if (targetItem.isEquipped())
                        player.getEquipment().setPersistentState(PersistentState.UPDATE_REQUIRED);
                    else
                        player.getInventory().setPersistentState(PersistentState.UPDATE_REQUIRED);
                }
                else {
                    if ((mightCost + enchantCost * times) > might)
                        break;

                    mightCost += enchantCost * times;

                    if (targetItem.isEquipped())
                        EnchantService.onItemUnequip(player, targetItem);
                    targetItem.setEnchantLevel(targetItem.getEnchantLevel() + times);
                    if (targetItem.isEquipped() && !targetItem.isWeaponSwapped(player))
                        EnchantService.onItemEquip(player, targetItem);

                    PacketSendUtility.sendPacket(player, new SM_UPDATE_ITEM(targetItem));

                    if (targetItem.isEquipped())
                        player.getEquipment().setPersistentState(PersistentState.UPDATE_REQUIRED);
                    else
                        player.getInventory().setPersistentState(PersistentState.UPDATE_REQUIRED);
                }
            }
        }

        if (mightCost == 0)
            return;

        DAOManager.getDAO(MightDAO.class).addMight(player, -mightCost);

        PacketSendUtility.sendMessage(player, "You have purchased " + (mightCost / enchantCost)
            + " enchant(s). Your items have been upgraded!");
        PacketSendUtility.sendMessage(player, "You have " + (might - mightCost) + " Might left.");

        DAOManager.getDAO(ShopDAO.class).logPurchase(player, -1, times, mightCost);
    }

    private static boolean isUpgradeable(Item item) {
        if (item.getItemTemplate().isNoEnchant())
            return false;
        if (item.getItemTemplate().getArmorType() == ArmorType.ARROW)
            return false;
        if (item.getItemTemplate().getArmorType() == ArmorType.SHARD)
            return false;
        if (item.getItemTemplate().isWeapon())
            return true;
        if (item.getItemTemplate().isArmor()) {
            int at = item.getItemTemplate().getItemSlot();
            if (at == 1 || /* Main Hand */
            at == 2 || /* Sub Hand */
            at == 8 || /* Jacket */
            at == 16 || /* Gloves */
            at == 32 || /* Boots */
            at == 2048 || /* Shoulder */
            at == 4096 || /* Pants */
            at == 131072 || /* Main Off Hand */
            at == 262144) /* Sub Off Hand */
                return true;
        }
        return false;
    }
}