/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.geoEngine2.GeoEngine2;
import gameserver.geoEngine2.math.Vector3f;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.model.gameobjects.stats.id.StoneStatEffectId;
import gameserver.model.gameobjects.stats.modifiers.AddModifier;
import gameserver.model.gameobjects.stats.modifiers.StatModifier;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.skillengine.SkillEngine;
import gameserver.skillengine.model.Skill;
import gameserver.spawnengine.SpawnEngine;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.World;

import java.util.TreeSet;

/**
 * <AUTHOR>
 */
public class DoomCommand extends AdminCommand {

    public DoomCommand() {
        super("doom");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_ADMIN) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command!");
            return;
        }

        if ((admin.getTarget() == null || !(admin.getTarget() instanceof Creature))
            && params.length < 1) {
            PacketSendUtility
                .sendMessage(
                    admin,
                    "Syntax: //doom [name] -- either target a player or specify name!"
                        + "\nOR Syntax: //doom <name> <radius> -- dooms within specified radius of player");
            return;
        }

        Creature target = null;
        if (params.length >= 1)
            target = World.getInstance().findPlayer(Util.convertName(params[0]));
        else if (admin.getTarget() != null && admin.getTarget() instanceof Creature)
            target = (Creature) admin.getTarget();

        if (target == null) {
            PacketSendUtility.sendMessage(admin,
                "No player with that name online or something went wrong!");
            return;
        }

        if (params.length >= 2
            && World.getInstance().findPlayer(Util.convertName(params[0])) != null) {
            int radius = 0;

            try {
                radius = Integer.parseInt(params[1]);
            }
            catch (Exception e) {
                PacketSendUtility.sendMessage(admin, "Please specify a number as radius!");
                return;
            }

            if (radius > 100)
                radius = 100;
            else if (radius < 1) {
                PacketSendUtility.sendMessage(admin, "Please specify a positive number as radius!");
                return;
            }

            for (Player pl : target.getKnownList().getPlayers()) {
                if (pl.isGM() || !MathUtil.isIn3dRange(target, pl, radius))
                    continue;

                Creature knight = spawn(284259, pl, 0);
                Creature darkness = spawn(282728, pl, 0);

                Creature reaper1 = spawn(211247, pl, Math.PI * 1 / 4);
                Creature reaper2 = spawn(211247, pl, Math.PI * 3 / 4);
                Creature reaper3 = spawn(211247, pl, Math.PI * 5 / 4);
                Creature reaper4 = spawn(211247, pl, Math.PI * 7 / 4);

                TreeSet<StatModifier> mods = new TreeSet<StatModifier>();
                mods.add(AddModifier.newInstance(StatEnum.MAGICAL_ACCURACY, 5000, true));
                mods.add(AddModifier.newInstance(StatEnum.MAXHP, 5000000, true));

                knight.getGameStats().addModifiers(StoneStatEffectId.getInstance(0, 0), mods);
                darkness.getGameStats().addModifiers(StoneStatEffectId.getInstance(0, 0), mods);

                skill(knight, pl, 8256, 0, 500);
                skill(knight, pl, 8256, 0, 2500);
                skill(knight, pl, 8256, 0, 4500);
                skill(knight, pl, 20987, 1100, 3900);

                kill(pl, 5000);

                delete(knight, 6500);
                delete(darkness, 6500);
                delete(reaper1, 6500);
                delete(reaper2, 6500);
                delete(reaper3, 6500);
                delete(reaper4, 6500);
            }
        }

        Creature knight = spawn(284259, target, 0);
        Creature darkness = spawn(282728, target, 0);

        Creature reaper1 = spawn(211247, target, Math.PI * 1 / 4);
        Creature reaper2 = spawn(211247, target, Math.PI * 3 / 4);
        Creature reaper3 = spawn(211247, target, Math.PI * 5 / 4);
        Creature reaper4 = spawn(211247, target, Math.PI * 7 / 4);

        TreeSet<StatModifier> mods = new TreeSet<StatModifier>();
        mods.add(AddModifier.newInstance(StatEnum.MAGICAL_ACCURACY, 5000, true));
        mods.add(AddModifier.newInstance(StatEnum.MAXHP, 5000000, true));

        knight.getGameStats().addModifiers(StoneStatEffectId.getInstance(0, 0), mods);
        darkness.getGameStats().addModifiers(StoneStatEffectId.getInstance(0, 0), mods);

        skill(knight, target, 8256, 0, 500);
        skill(knight, target, 8256, 0, 2500);
        skill(knight, target, 8256, 0, 4500);
        skill(knight, target, 20987, 1100, 3900);

        kill(target, 5000);

        delete(knight, 6500);
        delete(darkness, 6500);
        delete(reaper1, 6500);
        delete(reaper2, 6500);
        delete(reaper3, 6500);
        delete(reaper4, 6500);
    }

    private void kill(final Creature target, int delay) {
        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                target.getController().die();
            }
        }, delay);
    }

    private void delete(final Creature target, int delay) {
        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                target.getController().delete();
            }
        }, delay);
    }

    private void skill(final Creature castor, final Creature target, final int skillId,
        final int skillTime, int delay) {
        if (delay == 0) {
            Skill skill = SkillEngine.getInstance().getSkill(castor, skillId, 1, target);
            skill.setTime(skillTime);
            skill.setFirstTargetRangeCheck(false);

            if (skill != null)
                skill.useSkill();
        }
        else {
            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    Skill skill = SkillEngine.getInstance().getSkill(castor, skillId, 1, target);
                    skill.setTime(skillTime);
                    skill.setFirstTargetRangeCheck(false);

                    if (skill != null)
                        skill.useSkill();
                }
            }, delay);
        }
    }

    private Creature spawn(int npcId, Creature target, double angle) {
        int worldId = target.getWorldId();
        int instanceId = target.getInstanceId();

        double radian = Math.toRadians(MathUtil.convertHeadingToDegree(target.getHeading()));
        float x = target.getX() + (float) (15 * Math.cos(angle + radian));
        float y = target.getY() + (float) (15 * Math.sin(angle + radian));
        float z = target.getZ();

        Vector3f coll = GeoEngine2.getInstance().getClosestCollision(target, x, y, z);

        x = coll.getX();
        y = coll.getY();
        z = coll.getZ();

        byte heading = target.getHeading();
        if (heading > 60)
            heading -= 60;
        else
            heading += 60;

        SpawnTemplate gST = SpawnEngine.getInstance().addNewSpawn(worldId, instanceId, npcId, x, y,
            GeoEngine2.getInstance().getZ(worldId, x, y, z), heading, 0, 0, true);

        Creature result = (Creature) SpawnEngine.getInstance().spawnObject(gST, instanceId);
        if (result != null)
            ((Creature) result).getAggroList().addHate(target, 50000);

        return result;
    }
}
