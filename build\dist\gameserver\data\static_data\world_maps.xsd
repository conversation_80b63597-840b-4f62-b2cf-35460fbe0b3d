<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" version="1.0">
	<xs:element name="world_maps">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="map" minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence/>
						<xs:attribute name="name" type="xs:string"/>
						<xs:attribute name="id" type="xs:int" use="required"/>
						<xs:attribute name="twin_count" type="xs:int"/>
						<xs:attribute name="max_user" type="xs:int"/>
						<xs:attribute name="prison" type="xs:boolean"/>
						<xs:attribute name="instance" type="xs:boolean"/>
						<xs:attribute name="instanceId" type="xs:int"/>
						<xs:attribute name="cooldown" type="xs:int"/>
						<xs:attribute name="water_level" type="xs:int" use="required"/>
						<xs:attribute name="death_level" type="xs:int" use="required"/>
						<xs:attribute name="world_type" type="worldType"/>
						<xs:attribute name="world_size" type="xs:int"/>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:simpleType name="worldType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ASMODAE"/>
			<xs:enumeration value="ELYSEA"/>
			<xs:enumeration value="BALAUREA"/>
			<xs:enumeration value="ABYSS"/>
			<xs:enumeration value="NONE"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
