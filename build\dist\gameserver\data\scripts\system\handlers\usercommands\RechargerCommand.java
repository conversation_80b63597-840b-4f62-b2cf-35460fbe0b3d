/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.model.gameobjects.player.Player;
import gameserver.services.ArenaService;
import gameserver.services.EventService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

/**
 * 
 * <AUTHOR>
 */
public class RechargerCommand extends UserCommand {

    public RechargerCommand() {
        super("recharger");
    }

    public void executeCommand(Player player, String param) {
        if (player.isTemporary())
            return;

        if (player.getBattleground() != null || ArenaService.getInstance().isInArena(player)) {
            PacketSendUtility.sendMessage(player,
                "You cannot queue for the recharger while in a Battleground or FFA.");
            return;
        }

        if (!EventService.getInstance().isRechargerQueueOn()) {
            PacketSendUtility.sendMessage(player, "There is no recharger to queue for.");
            return;
        }

        if (EventService.getInstance().getRechargerQueue().contains(player)) {
            PacketSendUtility.sendMessage(player, "You are already registered for the recharger.");
            return;
        }
        else if (EventService.getInstance().addToRechargerQueue(player)) {
            PacketSendUtility.sendMessage(player, "You have registered for the recharger.");
            return;
        }
    }
}