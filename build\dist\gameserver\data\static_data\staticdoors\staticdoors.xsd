<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" version="1.0">
  <xs:element name="staticdoors">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="staticdoor" maxOccurs="unbounded" minOccurs="0">
          <xs:complexType mixed="true">
            <xs:sequence>
              <xs:element name="bounding_box" minOccurs="0">
                <xs:complexType>
                  <xs:simpleContent>
                    <xs:extension base="xs:string">
                      <xs:attribute type="xs:float" name="x1" use="optional"/>
                      <xs:attribute type="xs:float" name="y1" use="optional"/>
                      <xs:attribute type="xs:float" name="z1" use="optional"/>
                      <xs:attribute type="xs:float" name="x2" use="optional"/>
                      <xs:attribute type="xs:float" name="y2" use="optional"/>
                      <xs:attribute type="xs:float" name="z2" use="optional"/>
                    </xs:extension>
                  </xs:simpleContent>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
            <xs:attribute type="xs:short" name="id" use="optional"/>
            <xs:attribute type="DoorType" name="type" use="optional" default="DOOR"/>
            <xs:attribute type="xs:string" name="name" use="optional"/>
            <xs:attribute type="xs:int" name="world_id" use="optional"/>
            <xs:attribute type="xs:float" name="x" use="optional"/>
            <xs:attribute type="xs:float" name="y" use="optional"/>
            <xs:attribute type="xs:float" name="z" use="optional"/>
			<xs:attribute type="xs:byte" name="h" use="optional"/>
            <xs:attribute type="xs:string" name="mesh" use="optional"/>
            <xs:attribute type="xs:byte" name="state" use="optional" default="0"/>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:simpleType name="DoorType">
     <xs:restriction base="xs:string">
		<xs:enumeration value="DOOR"/>
		<xs:enumeration value="ABYSS"/>
     </xs:restriction>
  </xs:simpleType>
</xs:schema>