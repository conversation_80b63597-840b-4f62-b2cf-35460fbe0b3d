<?xml version="1.0" encoding="utf-8"?>
<quest_scripts>
<!--
   This file is part of Aion X Emu <aionxemu.com>.

	This is free software: you can redistribute it and/or modify
	it under the terms of the GNU Lesser Public License as published by
	the Free Software Foundation, either version 3 of the License, or
	(at your option) any later version.

	This software is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY; without even the implied warranty of
	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
	GNU Lesser Public License for more details.

	You should have received a copy of the GNU Lesser Public License
	along with this software.  If not, see <http://www.gnu.org/licenses/>.
-->

	<!-- 1000: Prologue handled by script -->
	<!-- 1001: The Kerub Threat handled by script -->
	<!-- 1002: Request of the Elim handled by script -->
	<!-- 1003: Illegal Logging handled by script -->
	<!-- 1004: Neutralizing Odium handled by script -->
	<!-- 1005: Barring the Gate handled by script -->
	<!-- 1100: <PERSON><PERSON>'s Call handled by script -->
	<!-- Sleeping on the Job -->
	<report_to id="1101" start_npc_id="203049" end_npc_id="203057" />
	<!-- Kerubar Hunt -->
	<monster_hunt id="1102" start_npc_id="203057">
		<monster_infos var_id="0" max_kill="3" npc_id="210133" />
		<monster_infos var_id="0" max_kill="3" npc_id="210134" />
		<monster_infos var_id="0" max_kill="3" npc_id="210705" />
	</monster_hunt>
	<!-- Grain Thieves -->
	<item_collecting id="1103" start_npc_id="203057" action_item_id="700105" />
	<!-- Report to Polinia -->
	<report_to id="1104" start_npc_id="203057" end_npc_id="203059" />
	<!-- The Snuffler Headache -->
	<item_collecting id="1105" start_npc_id="203050" />
	<!-- Helping Kales -->
	<report_to id="1106" start_npc_id="203050" end_npc_id="203061" item_id="182200203" />
	<!-- 1107: The Lost Axe handled by script -->
	<!-- Uno's Ingredients -->
	<item_collecting id="1108" start_npc_id="203061" />
	<!-- Abandoned Goods -->
	<item_collecting id="1109" start_npc_id="798007" action_item_id="700106" />
	<!-- A Book for Namus -->
	<report_to id="1110" start_npc_id="203065" end_npc_id="203075" item_id="182200206" />
	<!-- 1111: Insomnia Medicine handled by script -->
	<!-- To Fish in Peace -->
	<monster_hunt id="1112" start_npc_id="203072">
		<monster_infos var_id="0" max_kill="5" npc_id="210259" />
		<monster_infos var_id="0" max_kill="5" npc_id="210260" />
		<monster_infos var_id="0" max_kill="5" npc_id="210672" />
		<monster_infos var_id="1" max_kill="5" npc_id="210065" />
		<monster_infos var_id="1" max_kill="5" npc_id="210066" />
	</monster_hunt>
	<!-- Mushroom Thieves -->
	<monster_hunt id="1113" start_npc_id="203076">
		<monster_infos var_id="0" max_kill="8" npc_id="210262" />
		<monster_infos var_id="0" max_kill="8" npc_id="210675" />
	</monster_hunt>
	<!-- 1114: The Nymph's Gown handled by script -->
	<!-- 1115: The Elim's Message handled by script-->
	<!-- Pernos's Robe -->
	<item_collecting id="1116" start_npc_id="203060" />
	<!-- Light up the Night -->
	<item_collecting id="1117" start_npc_id="203074" />
	<!-- Polinia's Ointment -->
	<report_to id="1118" start_npc_id="203059" end_npc_id="203079" item_id="182200224" />
	<!-- A Taste of Namus's Medicine -->
	<report_to id="1119" start_npc_id="203075" end_npc_id="203080" item_id="182200225" />
	<!-- Thinning out Worgs -->
	<monster_hunt id="1120" start_npc_id="203082">
		<monster_infos var_id="0" max_kill="9" npc_id="210143" />
		<monster_infos var_id="0" max_kill="9" npc_id="210142" />
	</monster_hunt>
	<!-- Oz's Prayer Beads -->
	<item_collecting id="1121" start_npc_id="203081" />
	<!-- 1122: Delivering Pernos's Robe handled by script -->
	<!-- 1123: Where's Tutty? handled by script -->
	<!-- Avenging Tutty -->
	<item_collecting id="1124" start_npc_id="790001" />
	<!-- Suspicious Ore -->
	<item_collecting id="1125" start_npc_id="203085" action_item_id="700107" />
	<!-- Mushroom Research -->
	<item_collecting id="1126" start_npc_id="203079" />
	<!-- 1127: Ancient Cube handled by script-->
	<!-- 1128: TODO: The Amazing Escape -->
	<!-- Scouting Timolia Mine -->
	<item_collecting id="1129" start_npc_id="203085" />
	<!-- 1205: A New Skill handled by script -->
	<!-- Collecting Aria -->
	<item_collecting id="1206" start_npc_id="203059" />
	<!-- Tula's Music Box -->
	<item_collecting id="1207" start_npc_id="203082" end_npc_id="203085" />
</quest_scripts>