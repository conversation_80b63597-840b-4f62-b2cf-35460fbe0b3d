/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package mysql5;

import gameserver.dao.BannedMacDAO;
import gameserver.network.aion.BannedMacEntry;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.DatabaseFactory;

/**
 * 
 * <AUTHOR>
 */
public class MySQL5BannedMacDAO extends BannedMacDAO {
    private static final Logger log = Logger.getLogger(MySQL5MightDAO.class);

    @Override
    public boolean update(BannedMacEntry entry) {
        boolean success = false;
        Connection con = null;

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement ps = con
                .prepareStatement("REPLACE INTO `banned_mac` (`address`,`details`) VALUES (?,?)");

            ps.setString(1, entry.getMac());
            ps.setString(2, entry.getDetails());

            success = ps.executeUpdate() > 0;
        }
        catch (SQLException e) {
            log.error("Error storing BannedMacEntry " + entry.getMac(), e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        return success;
    }

    @Override
    public boolean isBanned(String mac) {
        boolean isBanned = false;
        Connection con = null;

        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement ps = con
                .prepareStatement("SELECT `id` FROM `banned_mac` WHERE `address` = ?");

            ps.setString(1, mac);
            ResultSet rset = ps.executeQuery();

            while (rset.next())
                isBanned = true;
        }
        catch (Exception e) {
            log.error("Error checking if MAC " + mac + " is banned!", e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        return isBanned;
    }

    @Override
    public boolean supports(String databaseName, int majorVersion, int minorVersion) {
        return MySQL5DAOUtils.supports(databaseName, majorVersion, minorVersion);
    }
}