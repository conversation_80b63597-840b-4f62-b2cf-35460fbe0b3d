/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package mysql5;

import gameserver.dao.BetDAO;
import gameserver.model.bet.Bet;
import gameserver.model.bet.Condition;
import gameserver.model.bet.Event;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.DatabaseFactory;
import com.mysql.jdbc.Statement;

/**
 * <AUTHOR>
 * 
 */
public class MySQL5BetDAO extends BetDAO {
    private static final Logger log = Logger.getLogger(MySQL5BetDAO.class);

    /*
     * (non-Javadoc)
     * @see com.aionemu.commons.database.dao.DAO#supports(java.lang.String, int, int)
     */
    @Override
    public boolean supports(String databaseName, int majorVersion, int minorVersion) {
        return MySQL5DAOUtils.supports(databaseName, majorVersion, minorVersion);
    }

    private List<Condition> getConditions(int conditionsId, Connection con) throws SQLException {
        List<Condition> conditions = new ArrayList<Condition>();

        PreparedStatement stm = con
            .prepareStatement("Select c.eventID, c.expectedValue From betsystem_events e, betsystem_conditions c Where c.conditionID = ? AND c.eventID = e.id");
        stm.setInt(1, conditionsId);

        ResultSet result = stm.executeQuery();

        while (result.next()) {
            conditions.add(new Condition(result.getInt("eventID"), result.getInt("expectedValue")));
        }

        return conditions;
    }

    /*
     * (non-Javadoc)
     * @see gameserver.dao.BetDAO#getBet(int)
     */
    @Override
    public Bet getBet(int id) {
        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stm = con
                .prepareStatement("Select * From betsystem_slips Where id = ?");
            stm.setInt(1, id);

            ResultSet result = stm.executeQuery();

            int player = 0;
            int value = 0;
            double rate = 0;
            Date endDate = null;
            List<Condition> conditions = new ArrayList<Condition>();

            while (result.next()) {
                player = result.getInt("playerID");
                value = result.getInt("bet");
                rate = result.getDouble("rate");
                endDate = result.getDate("endDate");
                conditions = getConditions(id, con);
            }

            return new Bet(player, value, rate, endDate, conditions);
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }
        return null;
    }

    /*
     * (non-Javadoc)
     * @see gameserver.dao.BetDAO#addBet(gameserver.model.bet.Bet)
     */
    @Override
    public int addBet(Bet bet) {
        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement slip_stm = con.prepareStatement(
                "INSERT INTO betsystem_slips (playerID, bet, rate, endDate) VALUES(?,?,?,?)",
                Statement.RETURN_GENERATED_KEYS);
            PreparedStatement condition_stm = con
                .prepareStatement("INSERT INTO betsystem_conditions (conditionID, eventID, expectedValue) VALUES(?,?,?)");

            int playerID = bet.getOwnerObjectId();
            long value = bet.getValue();
            double rate = bet.getRate();
            Date endDate = bet.getEndDate();
            List<Condition> conditions = bet.getConditions();

            int conditionID = 0;

            slip_stm.setInt(1, playerID);
            slip_stm.setLong(2, value);
            slip_stm.setDouble(3, rate);
            slip_stm.setDate(4, new java.sql.Date(endDate.getTime()));

            slip_stm.executeUpdate();
            ResultSet insertRS = slip_stm.getGeneratedKeys();

            while (insertRS.next()) {
                conditionID = insertRS.getInt(1);
            }

            for (Condition condition : conditions) {
                condition_stm.setInt(1, conditionID);
                condition_stm.setInt(2, condition.getEventID());
                condition_stm.setInt(3, condition.getExpectedValue());
                condition_stm.executeUpdate();
            }

            return conditionID;

        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }
        return -1;
    }

    /*
     * (non-Javadoc)
     * @see gameserver.dao.BetDAO#deleteBet(int)
     */
    @Override
    public boolean deleteBet(int id) {
        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement slip_stm = con
                .prepareStatement("DELETE FROM betsystem_slips WHERE id = ?");
            PreparedStatement condition_stm = con
                .prepareStatement("DELETE FROM betsystem_conditions WHERE conditionID = ?");

            slip_stm.setInt(1, id);
            condition_stm.setInt(1, id);

            slip_stm.executeUpdate();
            condition_stm.executeUpdate();

            return true;
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        return false;
    }

    /*
     * (non-Javadoc)
     * @see gameserver.dao.BetDAO#changeBet(gameserver.model.bet.Bet, int)
     */
    @Override
    public boolean changeBet(Bet bet, int id) {
        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement slip_stm = con
                .prepareStatement("UPDATE betsystem_slips SET playerID=?,bet=?,rate=?,endDate=? WHERE id = ?");
            PreparedStatement condition_del_stm = con
                .prepareStatement("DELETE FROM betsystem_conditions WHERE conditionID = ?");
            PreparedStatement condition_stm = con
                .prepareStatement("INSERT INTO betsystem_conditions (conditionID, eventID, expectedValue) VALUES(?,?,?)");

            slip_stm.setInt(1, bet.getOwnerObjectId());
            slip_stm.setInt(2, bet.getValue());
            slip_stm.setDouble(3, bet.getRate());
            slip_stm.setDate(4, new java.sql.Date(bet.getEndDate().getTime()));
            slip_stm.setInt(5, id);
            condition_del_stm.setInt(1, id);

            slip_stm.executeUpdate();
            condition_del_stm.executeUpdate();

            for (Condition condition : bet.getConditions()) {
                condition_stm.setInt(1, id);
                condition_stm.setInt(2, condition.getEventID());
                condition_stm.setInt(3, condition.getExpectedValue());
                condition_stm.executeUpdate();
            }

            return true;

        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }
        return false;
    }

    /*
     * (non-Javadoc)
     * @see gameserver.dao.BetDAO#getBetIds()
     */
    @Override
    public List<Integer> getBetIds() {
        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement slip_stm = con.prepareStatement("Select id FROM betsystem_slips");

            List<Integer> ids = new ArrayList<Integer>();

            ResultSet result = slip_stm.executeQuery();

            while (result.next()) {
                ids.add(result.getInt(1));
            }

            return ids;

        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }
        return null;
    }

    /*
     * (non-Javadoc)
     * @see gameserver.dao.BetDAO#getEvent(int)
     */
    @Override
    public Event getEvent(int id) {
        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stm = con
                .prepareStatement("Select * From betsystem_events Where id = ?");
            stm.setInt(1, id);

            ResultSet result = stm.executeQuery();

            int value = -1;
            String name = "N/A";
            String groupname = "N/A";

            while (result.next()) {
                value = result.getInt("value");
                name = result.getString("name");
                groupname = result.getString("groupname");
            }

            return new Event(name, groupname, value);
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }
        return null;
    }

    /*
     * (non-Javadoc)
     * @see gameserver.dao.BetDAO#addEvent(gameserver.model.bet.Event)
     */
    @Override
    public int addEvent(Event event) {
        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stm = con.prepareStatement(
                "INSERT INTO betsystem_events (value,name,groupname) VALUES(?,?,?)",
                Statement.RETURN_GENERATED_KEYS);

            stm.setInt(1, event.getValue());
            stm.setString(2, event.getName());
            stm.setString(3, event.getGroupname());

            stm.executeUpdate();
            ResultSet insertRS = stm.getGeneratedKeys();

            int id = -1;

            while (insertRS.next()) {
                id = insertRS.getInt(1);
            }

            return id;
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }
        return -1;
    }

    /*
     * (non-Javadoc)
     * @see gameserver.dao.BetDAO#deleteEvent(int)
     */
    @Override
    public boolean deleteEvent(int id) {
        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stm = con.prepareStatement("DELETE FROM betsystem_events WHERE id=?");

            stm.setInt(1, id);
            stm.executeUpdate();

            return true;
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }
        return false;
    }

    /*
     * (non-Javadoc)
     * @see gameserver.dao.BetDAO#changeEvent(gameserver.model.bet.Event, int)
     */
    @Override
    public boolean changeEvent(Event event, int id) {
        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement stm = con
                .prepareStatement("UPDATE betsystem_events SET value=?,name=?,groupname=? WHERE id = ?");
            stm.setInt(1, event.getValue());
            stm.setString(2, event.getName());
            stm.setString(3, event.getGroupname());
            stm.setInt(4, id);
            stm.executeUpdate();

            return true;
        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }
        return false;
    }

    /*
     * (non-Javadoc)
     * @see gameserver.dao.BetDAO#getEventIds()
     */
    @Override
    public List<Integer> getEventIds() {
        Connection con = null;
        try {
            con = DatabaseFactory.getConnection();
            PreparedStatement slip_stm = con.prepareStatement("Select id FROM betsystem_events");

            List<Integer> ids = new ArrayList<Integer>();

            ResultSet result = slip_stm.executeQuery();

            while (result.next()) {
                ids.add(result.getInt(1));
            }

            return ids;

        }
        catch (Exception e) {
            log.error(e);
        }
        finally {
            DatabaseFactory.close(con);
        }
        return null;
    }
}
