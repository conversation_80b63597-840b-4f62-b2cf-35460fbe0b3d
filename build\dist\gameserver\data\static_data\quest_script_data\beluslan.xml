<?xml version="1.0" encoding="utf-8"?>
<quest_scripts>
<!--
   This file is part of Aion X Emu <aionxemu.com>.

	This is free software: you can redistribute it and/or modify
	it under the terms of the GNU Lesser Public License as published by
	the Free Software Foundation, either version 3 of the License, or
	(at your option) any later version.

	This software is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY; without even the implied warranty of
	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
	GNU Lesser Public License for more details.

	You should have received a copy of the GNU Lesser Public License
	along with this software.  If not, see <http://www.gnu.org/licenses/>.
-->

	<!-- 2051: Saving Beluslan Fortress handled by script -->
	<!-- 2052: An Undead Occupation handled by script -->
	<!-- 2053: A Missing Father handled by script -->
	<!-- 2054: Light up the Lighthouse handled by script -->
	<!-- 2055: The Seiren's Treasure handled by script -->
	<!-- 2056: Thawing <PERSON> handled by script -->
	<!-- 2057: G<PERSON>ciont the Hardy handled by script -->
	<!-- 2058: A Spy Among the Lepharists handled by script -->
	<!-- 2059: A Peace Offering handled by script -->
	<!-- 2060: Restoring Beluslan Observatory handled by script -->
	<!-- 2061: Suppressing the Bakarma Legion handled by script -->
	<!-- 2500: Orders From Nerita handled by script -->
	<!-- 2501: Falling Fragments handled by script -->
	<!-- Monitor Glue -->
	<item_collecting id="2502" start_npc_id="204701" />
	<!-- 2503: The Fragment Hazard -->
	<item_collecting id="2503" start_npc_id="204733" action_item_id="700327" />
	<!-- Only for Her -->
	<item_collecting id="2504" start_npc_id="204720" />
	<!-- 2505: TODO: Let Cooking Experts Cook -->
	<!-- The Outlaw of Black Plains -->
	<monster_hunt id="2506" start_npc_id="204702">
		<monster_infos var_id="0" max_kill="10" npc_id="213017" />
	</monster_hunt>
	<!-- Nerita's Encouragement -->
	<report_to id="2507" start_npc_id="204702" end_npc_id="204801" item_id="182204406" />
	<!-- Shugo Grass -->
	<report_to id="2508" start_npc_id="204734" end_npc_id="204802" />
	<!-- Gigrite's Drakie Problem -->
	<monster_hunt id="2509" start_npc_id="204703" end_npc_id="204801">
		<monster_infos var_id="0" max_kill="15" npc_id="213020" />
	</monster_hunt>
	<!-- 2510: TODO: Vanished Quest -->
	<!-- Ambitions for Success -->
	<item_collecting id="2511" start_npc_id="204711" />
	<!-- 2512: Food for Remote Places handled by script -->
	<!-- 2513: TODO: The Strange Cottage -->
	<!-- 2514: The Red Mane Report handled by script -->
	<!-- 2515: Leanor's Errand handled by script -->
	<!-- Beluslan Pest Hunt -->
	<monster_hunt id="2516" start_npc_id="204781">
		<monster_infos var_id="0" max_kill="24" npc_id="213007" />
		<monster_infos var_id="1" max_kill="17" npc_id="213013" />
	</monster_hunt>
	<!-- 2517: TODO: [Spend Coin] Warrior/Scout -->
	<!-- 2518: TODO: [Spend Coin] Mage/Priest -->
	<!-- 2519: TODO: [Manastone] Cadoc's Exchange -->
	<!-- 2520: TODO: [Manastone] Cadoc's Trade -->
	<!-- Gigrite's Secret -->
	<monster_hunt id="2521" start_npc_id="204801">
		<monster_infos var_id="0" max_kill="15" npc_id="213015" />
	</monster_hunt>
	<!-- Decreasing Drakies -->
	<monster_hunt id="2522" start_npc_id="204801">
		<monster_infos var_id="0" max_kill="15" npc_id="213054" />
		<monster_infos var_id="1" max_kill="3" npc_id="213246" />
	</monster_hunt>
	<!-- 2523: Grass for the Grave Robbers handled by script -->
	<!-- The Mosbear Frenzy -->
	<monster_hunt id="2524" start_npc_id="204803">
		<monster_infos var_id="0" max_kill="15" npc_id="213022" />
		<monster_infos var_id="0" max_kill="15" npc_id="213023" />
		<monster_infos var_id="1" max_kill="3" npc_id="213024" />
	</monster_hunt>
	<!-- The Mosbear Trophy -->
	<item_collecting id="2525" start_npc_id="204803" />
	<!-- A Grateful Sentinel -->
	<report_to id="2526" start_npc_id="204804" end_npc_id="204811" />
	<!-- The Starving Sprigg -->
	<item_collecting id="2527" start_npc_id="204811" action_item_id="700328" />
	<!-- Snowfield Beasts -->
	<monster_hunt id="2528" start_npc_id="204804">
		<monster_infos var_id="0" max_kill="15" npc_id="213027" />
		<monster_infos var_id="1" max_kill="11" npc_id="213035" />
	</monster_hunt>
	<!-- Mutant Earth Spirits -->
	<item_collecting id="2529" start_npc_id="204802" />
	<!-- Hunting the Undead -->
	<monster_hunt id="2530" start_npc_id="204801">
		<monster_infos var_id="0" max_kill="10" npc_id="213042" />
		<monster_infos var_id="1" max_kill="7" npc_id="213044" />
		<monster_infos var_id="2" max_kill="3" npc_id="213046" />
	</monster_hunt>
	<!-- Containing the Poison -->
	<item_collecting id="2531" start_npc_id="204102" />
	<!-- A Poison Container -->
	<report_to id="2532" start_npc_id="204102" end_npc_id="204801" item_id="182204424" />
	<!-- 2533: TODO: Beritra's Curse -->
	<!-- A Fortunate Medicine -->
	<report_to id="2534" start_npc_id="204801" end_npc_id="204768" item_id="182204427" />
	<!-- Some of My Best Friends -->
	<monster_hunt id="2535" start_npc_id="204804">
		<monster_infos var_id="0" max_kill="1" npc_id="213708" />
		<monster_infos var_id="1" max_kill="1" npc_id="213709" />
	</monster_hunt>
	<!-- 2536: TODO: Offering Business to Grave Robbers -->
	<!-- [Group] Freki's Request -->
	<monster_hunt id="2537" start_npc_id="204826">
		<monster_infos var_id="0" max_kill="5" npc_id="213524" />
		<monster_infos var_id="1" max_kill="3" npc_id="213523" />
		<monster_infos var_id="2" max_kill="4" npc_id="213540" />
	</monster_hunt>
	<!-- 2538: TODO: Curse of the Were-Ribbit -->
	<!-- 2539: Tryst of the Shadow handled by script -->
	<!-- Granker for Gark -->
	<item_collecting id="2540" start_npc_id="204811" />
	<!-- Ore-Eating Wriggots -->
	<item_collecting id="2541" start_npc_id="798118" />
	<!-- Pure Malek -->
	<item_collecting id="2542" start_npc_id="798117" action_item_id="700347" />
	<!-- Mahisha's Heart -->
	<item_collecting id="2543" start_npc_id="798117" />
	<!-- Ice Tail Kirrins -->
	<monster_hunt id="2544" start_npc_id="798131">
		<monster_infos var_id="0" max_kill="15" npc_id="213050" />
	</monster_hunt>
	<!-- Deformed Malek -->
	<item_collecting id="2545" start_npc_id="798131" end_npc_id="204734" />
	<!-- A Gift in Return -->
	<report_to id="2546" start_npc_id="204734" end_npc_id="798131" item_id="182204432" />
	<!-- Barosunerk's New Business -->
	<item_collecting id="2547" start_npc_id="798119" end_npc_id="798083"/>
	<!-- Repaying the Debt -->
	<item_collecting id="2548" start_npc_id="798083" end_npc_id="798119" />
	<!-- 2549 TODO: A Gift For Amma -->
	<!-- Revenge of the Displaced -->
	<monster_hunt id="2550" start_npc_id="204811">
		<monster_infos var_id="0" max_kill="30" npc_id="213022" />
		<monster_infos var_id="1" max_kill="7" npc_id="213024" />
	</monster_hunt>
	<!-- Mist Valley Temple -->
	<monster_hunt id="2551" start_npc_id="798119">
		<monster_infos var_id="0" max_kill="11" npc_id="213073" />
		<monster_infos var_id="1" max_kill="11" npc_id="213075" />
		<monster_infos var_id="2" max_kill="1" npc_id="700329" />
	</monster_hunt>
	<!-- Lepharist Miners -->
	<monster_hunt id="2552" start_npc_id="798117">
		<monster_infos var_id="0" max_kill="10" npc_id="213263" />
		<monster_infos var_id="1" max_kill="10" npc_id="213278" />
	</monster_hunt>
	<!-- 2553: The Brigade General's Call handled by script -->
	<!-- Malek Nuggets -->
	<item_collecting id="2554" start_npc_id="798119" action_item_id="700330" />
	<!-- [Group] Supervisor Yotunmork -->
	<monster_hunt id="2555" start_npc_id="798119">
		<monster_infos var_id="0" max_kill="1" npc_id="213714" />
	</monster_hunt>
	<!-- Finding Stua -->
	<report_to id="2556" start_npc_id="798118" end_npc_id="204809" />
	<!-- [Group] An Expensive Spellbook -->
	<item_collecting id="2557" start_npc_id="798117" />
	<!-- Stua's Reinforcements -->
	<item_collecting id="2558" start_npc_id="204809" />
	<!-- [Group] Malek Transport Plan -->
	<item_collecting id="2559" start_npc_id="204809" />
	<!-- [Coin] Combat Miners -->
	<monster_hunt id="2560" start_npc_id="204809">
		<monster_infos var_id="0" max_kill="7" npc_id="213259" />
		<monster_infos var_id="1" max_kill="6" npc_id="213268" />
		<monster_infos var_id="2" max_kill="6" npc_id="213261" />
	</monster_hunt>
	<!-- Frozen Ghosts -->
	<monster_hunt id="2561" start_npc_id="204753">
		<monster_infos var_id="0" max_kill="10" npc_id="213084" />
		<monster_infos var_id="1" max_kill="10" npc_id="213086" />
		<monster_infos var_id="2" max_kill="10" npc_id="213088" />
	</monster_hunt>
	<!-- Furs For Warmth -->
	<item_collecting id="2562" start_npc_id="204786" />
	<!-- The Freezing Orb -->
	<item_collecting id="2563" start_npc_id="204753" />
	<!-- 2564: TODO: With the Power of Flame -->
	<!-- Good for Frostbite -->
	<item_collecting id="2565" start_npc_id="798094" />
	<!-- The Roar of the Ettins -->
	<monster_hunt id="2566" start_npc_id="790016">
		<monster_infos var_id="0" max_kill="7" npc_id="213095" />
	</monster_hunt>
	<!-- Why Etun Can't Meditate -->
	<monster_hunt id="2567" start_npc_id="790016">
		<monster_infos var_id="0" max_kill="9" npc_id="213096" />
		<monster_infos var_id="1" max_kill="15" npc_id="213092" />
	</monster_hunt>
	<!-- Misdirected Letter -->
	<report_to id="2568" start_npc_id="204755" end_npc_id="204763" item_id="182204446" />
	<!-- 2569: Tafi's Flame Scroll handled by script -->
	<!-- Malek Drakes and Drakies -->
	<item_collecting id="2570" start_npc_id="798119" />
	<!-- Delivering Medical Supplies -->
	<report_to id="2571" start_npc_id="204768" end_npc_id="204776" item_id="182204448" />
	<!-- To Build a Fire -->
	<item_collecting id="2572" start_npc_id="204746" />
	<!-- Undead Wildlife -->
	<monster_hunt id="2573" start_npc_id="204768">
		<monster_infos var_id="0" max_kill="6" npc_id="213146" />
		<monster_infos var_id="1" max_kill="10" npc_id="213148" />
	</monster_hunt>
	<!-- Let No Tether Remain -->
	<item_collecting id="2574" start_npc_id="204768" end_npc_id="204825" />
	<!-- The Nervous Sentry -->
	<monster_hunt id="2575" start_npc_id="204740">
		<monster_infos var_id="0" max_kill="10" npc_id="213142" />
	</monster_hunt>
	<!-- A Fearsome Forest -->
	<monster_hunt id="2576" start_npc_id="204769">
		<monster_infos var_id="0" max_kill="7" npc_id="213166" />
		<monster_infos var_id="1" max_kill="11" npc_id="213168" />
	</monster_hunt>
	<!-- Benyar's Request -->
	<monster_hunt id="2577" start_npc_id="204735">
		<monster_infos var_id="0" max_kill="13" npc_id="213170" />
	</monster_hunt>
	<!-- 2578: A Ring for Luck handled by script -->
	<!-- When I Seafood, I Eat It -->
	<item_collecting id="2579" start_npc_id="790017" />
	<!-- The Lighthouse Ghost -->
	<report_to id="2580" start_npc_id="204748" end_npc_id="204771" />
	<!-- Brilliant Kernel of the Night -->
	<item_collecting id="2581" start_npc_id="204771" />
	<!-- Safe Harbor -->
	<monster_hunt id="2582" start_npc_id="204769">
		<monster_infos var_id="0" max_kill="7" npc_id="213188" />
		<monster_infos var_id="1" max_kill="10" npc_id="213202" />
	</monster_hunt>
	<!-- 2583: A Polite Request handled by script -->
	<!-- 2584: TODO: The Starturtle and the Sprigg -->
	<!-- Priceless Coral -->
	<item_collecting id="2585" start_npc_id="204739" action_item_id="700331" />
	<!-- [Group] Dangerous Sea Creature -->
	<item_collecting id="2586" start_npc_id="204743" />
	<!-- [Group] Rubelik's Request -->
	<monster_hunt id="2587" start_npc_id="204743">
		<monster_infos var_id="0" max_kill="7" npc_id="213355" />
		<monster_infos var_id="1" max_kill="13" npc_id="213367" />
	</monster_hunt>
	<!-- [Group] The Elixir of Life -->
	<item_collecting id="2588" start_npc_id="204741" />
	<!-- [Coin] Ghosts of Anair Harbor -->
	<monster_hunt id="2589" start_npc_id="204762">
		<monster_infos var_id="0" max_kill="12" npc_id="213204" />
		<monster_infos var_id="1" max_kill="8" npc_id="213240" />
		<monster_infos var_id="2" max_kill="11" npc_id="213205" />
	</monster_hunt>
	<!-- 2590: TODO: [Coin] Safety of the Village -->
	<!-- 2591: TODO: [Spend Coin] Gold (Warrior/Scout 41) -->
	<!-- 2592: TODO: [Spend Coin] Gold (Mage/Priest 41) -->
	<!-- Wandering Souls -->
	<monster_hunt id="2593" start_npc_id="204771">
		<monster_infos var_id="0" max_kill="10" npc_id="213164" />
		<monster_infos var_id="1" max_kill="16" npc_id="213162" />
	</monster_hunt>
	<!-- A Procession of the Dead -->
	<monster_hunt id="2594" start_npc_id="204776">
		<monster_infos var_id="0" max_kill="7" npc_id="213173" />
		<monster_infos var_id="1" max_kill="3" npc_id="213172" />
		<monster_infos var_id="2" max_kill="5" npc_id="213782" />
	</monster_hunt>
	<!-- Crisis of Reyrinirerk -->
	<item_collecting id="2595" start_npc_id="798090" />
	<!-- [Group] Power Shard Materials -->
	<item_collecting id="2596" start_npc_id="204735" />
	<!-- [Group] Carapaces for Armor -->
	<item_collecting id="2597" start_npc_id="204735" />
	<!-- A Preventive Measure -->
	<item_collecting id="2599" start_npc_id="204725" />
	<!-- 2600 TODO: Humongous Malek -->
	<!-- [Group] Pollution in the Palace -->
	<monster_hunt id="2601" start_npc_id="204808">
		<monster_infos var_id="0" max_kill="20" npc_id="213356" />
		<monster_infos var_id="1" max_kill="4" npc_id="213781" />
	</monster_hunt>
	<!-- Blue Pearls -->
	<item_collecting id="2602" start_npc_id="204808" action_item_id="700332"/>
	<!-- The House on the Cliff -->
	<report_to id="2605" start_npc_id="204732" end_npc_id="204826" />
	<!-- A Ribbit in Need -->
	<report_to id="2606" start_npc_id="204732" end_npc_id="204827" />
	<!-- Silencing the Hermit -->
	<report_to id="2607" start_npc_id="204732" end_npc_id="790022" />
	<!-- 2608: Halt the Contamination -->
	<monster_hunt id="2608" start_npc_id="204804">
		<monster_infos var_id="0" max_kill="6" npc_id="213034" />
		<monster_infos var_id="1" max_kill="10" npc_id="213033" />
	</monster_hunt>
	<!-- 2611: Consulting the Leaders handled by script -->
	<!-- A Tonic for Nargatal -->
	<item_collecting id="2612" start_npc_id="204790" />
	<!-- 2613: Devoted Husband Akagitan -->
	<report_to id="2613" start_npc_id="204790" end_npc_id="204787" item_id="182204465" />
	<!-- Decorative Skulls -->
	<item_collecting id="2614" start_npc_id="204784" />
	<!-- Assuming Con-Troll -->
	<monster_hunt id="2615" start_npc_id="204783">
		<monster_infos var_id="0" max_kill="5" npc_id="700333" />
	</monster_hunt>
	<!-- Eradication Pa-Troll -->
	<monster_hunt id="2616" start_npc_id="204783">
		<monster_infos var_id="0" max_kill="20" npc_id="213211" />
		<monster_infos var_id="1" max_kill="30" npc_id="213213" />
	</monster_hunt>
	<!-- Mamut's Bones -->
	<item_collecting id="2617" start_npc_id="204750" action_item_id="700334" />
	<!-- Freyja's Desire -->
	<monster_hunt id="2618" start_npc_id="204763">
		<monster_infos var_id="0" max_kill="11" npc_id="213105" />
		<monster_infos var_id="1" max_kill="7" npc_id="213107" />
	</monster_hunt>
	<!-- Hoarfrost Diplomacy -->
	<report_to id="2619" start_npc_id="204700" end_npc_id="204817" item_id="182204502" />
	<!-- 2620: TODO: Summoning Phagrasul -->
	<!-- A Family Letter -->
	<report_to id="2621" start_npc_id="204763" end_npc_id="204059" item_id="182204468" />
	<!-- Hunting the Hunters -->
	<monster_hunt id="2622" start_npc_id="204751">
		<monster_infos var_id="0" max_kill="12" npc_id="213133" />
		<monster_infos var_id="1" max_kill="6" npc_id="213135" />
	</monster_hunt>
	<!-- Feast Fit for a Skurv -->
	<item_collecting id="2623" start_npc_id="204751"/>
	<!-- Before the Stew Gets Cold -->
	<report_to id="2624" start_npc_id="204751" end_npc_id="204791" item_id="182204471" />
	<!-- Bring Me Down -->
	<item_collecting id="2625" start_npc_id="204792" />
	<!-- Red Mane Tribe's Secret Remedy -->
	<item_collecting id="2626" start_npc_id="204752" />
	<!-- Spirit's Tears -->
	<item_collecting id="2627" start_npc_id="204790" end_npc_id="204748" action_item_id="700335" />
	<!-- Restive Spirits -->
	<monster_hunt id="2628" start_npc_id="204787">
		<monster_infos var_id="0" max_kill="4" npc_id="213119" />
		<monster_infos var_id="1" max_kill="8" npc_id="213237" />
	</monster_hunt>
	<!-- Once a Friend, Now an Enemy -->
	<monster_hunt id="2629" start_npc_id="204787">
		<monster_infos var_id="0" max_kill="10" npc_id="213122" />
		<monster_infos var_id="1" max_kill="7" npc_id="213124" />
	</monster_hunt>
	<!-- 2630: TODO: The Dutiful Spy -->
	<!-- [Group] Jaadis's Bioweapon -->
	<item_collecting id="2631" start_npc_id="204784" />
	<!-- [Group] Get 'Em Young -->
	<monster_hunt id="2632" start_npc_id="204799">
		<monster_infos var_id="0" max_kill="4" npc_id="213563" />
		<monster_infos var_id="1" max_kill="7" npc_id="213564" />
	</monster_hunt>
	<!-- 2633: TODO: [Group] Destroying Balaur Weapons -->
	<!-- 2634: TODO: [Group] The Draupnir Redemption -->
	<!-- [Coin/Group] Bakarma Barracks -->
	<monster_hunt id="2635" start_npc_id="204817">
		<monster_infos var_id="0" max_kill="10" npc_id="213663" />
		<monster_infos var_id="1" max_kill="10" npc_id="213669" />
		<monster_infos var_id="2" max_kill="10" npc_id="213672" />
	</monster_hunt>
	<!-- 2636: TODO: [Spend Coin] Gold (Warrior/Scout 46) -->
	<!-- 2637: TODO: [Spend Coin] Gold (Mage/Priest 46) -->
	<!-- 2638: Hoarfrost's Hunt -->
	<monster_hunt id="2638" start_npc_id="204796">
		<monster_infos var_id="0" max_kill="10" npc_id="213102" />
		<monster_infos var_id="1" max_kill="10" npc_id="213104" />
	</monster_hunt>
	<!-- 2639: Divided They Fall -->
	<monster_hunt id="2639" start_npc_id="204795">
		<monster_infos var_id="0" max_kill="1" npc_id="216894" />
		<monster_infos var_id="1" max_kill="1" npc_id="216895" />
		<monster_infos var_id="2" max_kill="1" npc_id="216896" />
	</monster_hunt>
	<!-- 2640: I'm a Skurv- -Get Me Out of Here! -->
	<item_collecting id="2640" start_npc_id="204792" />
	<!-- 2641: Powwow With the Mau handled by script -->
	<!-- Supplies for the Survivors -->
	<item_collecting id="2642" start_npc_id="204796" />
	<!-- Mau's Lament -->
	<item_collecting id="2643" start_npc_id="204795" />
	<!-- Hoarfrost Tribe's Enemy -->
	<monster_hunt id="2644" start_npc_id="204795">
		<monster_infos var_id="0" max_kill="20" npc_id="213229" />
		<monster_infos var_id="1" max_kill="15" npc_id="213231" />
	</monster_hunt>
	<!-- Hoarfrost Tribe's Symbol -->
	<item_collecting id="2645" start_npc_id="204795" action_item_id="700336" />
	<!-- 2646: TODO: The Inscrutable Stranger -->
	<!-- Belated Regret -->
	<report_to id="2647" start_npc_id="204775" end_npc_id="204785" />
	<!-- [Group] Drakes and Their Breeders -->
	<monster_hunt id="2648" start_npc_id="204795">
		<monster_infos var_id="0" max_kill="15" npc_id="213308" />
		<monster_infos var_id="1" max_kill="10" npc_id="213704" />
	</monster_hunt>
	<!-- [Group] Recovering the Keepsakes -->
	<item_collecting id="2649" start_npc_id="204796" />
	<!-- [Group] Balaur Assassin -->
	<monster_hunt id="2650" start_npc_id="204775">
		<monster_infos var_id="0" max_kill="5" npc_id="213700" />
		<monster_infos var_id="1" max_kill="10" npc_id="213702" />
		<monster_infos var_id="2" max_kill="10" npc_id="213304" />
	</monster_hunt>
	<!-- 2651: [Spy] A Friend's Whereabouts handled by script -->
	<!-- [Spy] Medicine for Nesteto -->
	<item_collecting id="2652" start_npc_id="204650" />
	<!-- 2653: [Spy] Finding Bollvig handled by script -->
	<!-- 2654: [Spy] The Last Persuasion handled by script -->
	<!-- [Spy/Alliance] Reaper Squad Squish -->
	<monster_hunt id="2655" start_npc_id="204655">
		<monster_infos var_id="0" max_kill="7" npc_id="204649" />
	</monster_hunt>
	<!-- [Spy] Letter to a Friend -->
	<report_to id="2656" start_npc_id="204655" end_npc_id="204775" item_id="182204506" />
	<!-- [Group] Materials for All Magic Wards -->
	<item_collecting id="2657" start_npc_id="204817" />
	<!-- 2658: [Spy/Group] No Reinforcements -->
	<item_collecting id="2658" start_npc_id="204785" />
	<!-- 2659: [Spy/Group] Clear the Way -->
	<monster_hunt id="2659" start_npc_id="204651">
		<monster_infos var_id="0" max_kill="11" npc_id="213577" />
		<monster_infos var_id="1" max_kill="20" npc_id="213579" />
	</monster_hunt>
	<!-- 2660: [Spy/Group] Open the Path -->
	<item_collecting id="2660" start_npc_id="204651" />
	<!-- An Unobtrusive Journey -->
	<monster_hunt id="2661" start_npc_id="204777">
		<monster_infos var_id="0" max_kill="11" npc_id="213217" />
		<monster_infos var_id="1" max_kill="10" npc_id="213219" />
	</monster_hunt>
	<!-- [Group] Dead Men Tell No Tales -->
	<monster_hunt id="2662" start_npc_id="204777">
		<monster_infos var_id="0" max_kill="7" npc_id="213324" />
		<monster_infos var_id="1" max_kill="7" npc_id="213328" />
		<monster_infos var_id="2" max_kill="7" npc_id="213332" />
	</monster_hunt>
	<!-- 2663: TODO: Acquiring an Antidote -->
	<!-- 2664: TODO: An Antidote to the Lepharists -->
	<!-- [Group] Closing the Gate -->
	<monster_hunt id="2665" start_npc_id="204777">
		<monster_infos var_id="0" max_kill="1" npc_id="700345" />
		<monster_infos var_id="1" max_kill="1" npc_id="700346" />
	</monster_hunt>
	<!-- 2666: TODO: [Group] Replacing Orders -->
	<!-- Strahein's Letter -->
	<report_to id="2667" start_npc_id="204814" end_npc_id="204749" item_id="182204499" />
	<!-- [Group] Saving Atla -->
	<monster_hunt id="2668" start_npc_id="204777" end_npc_id="204815">
		<monster_infos var_id="0" max_kill="11" npc_id="213934" />
		<monster_infos var_id="1" max_kill="6" npc_id="213947" />
	</monster_hunt>
	<!-- 2669: TODO: [Group] Atla's Escape -->
	<!-- 2670: The Ancient Book handled by script -->
	<!-- [Group] Decimating Bakarma Legion -->
	<monster_hunt id="2671" start_npc_id="204785">
		<monster_infos var_id="0" max_kill="9" npc_id="213624" />
	</monster_hunt>
	<!-- 2672: A Careful Eggs-amination -->
	<item_collecting id="2672" start_npc_id="204785" action_item_id="700337"/>
	<!-- [Spy/Group] Getting Through Indratu -->
	<item_collecting id="2673" start_npc_id="204785" />
	<!-- [Group] Dangerous Drake -->
	<monster_hunt id="2674" start_npc_id="204785">
		<monster_infos var_id="0" max_kill="5" npc_id="213642" />
		<monster_infos var_id="1" max_kill="6" npc_id="213644" />
		<monster_infos var_id="2" max_kill="11" npc_id="213648" />
	</monster_hunt>
	<!-- [Group] Balaur Weapons -->
	<item_collecting id="2675" start_npc_id="204785" />
	<!-- [Group] Loud Nagas and Nagarants -->
	<monster_hunt id="2676" start_npc_id="204785">
		<monster_infos var_id="0" max_kill="12" npc_id="213667" />
		<monster_infos var_id="1" max_kill="12" npc_id="213670" />
		<monster_infos var_id="2" max_kill="7" npc_id="213676" />
		<monster_infos var_id="3" max_kill="7" npc_id="213679" />
	</monster_hunt>
	<!-- [Group] Making a Small Flame Ward -->
	<item_collecting id="2677" start_npc_id="204817" />
	<!-- [Group] Making a Huge Flame Ward -->
	<item_collecting id="2678" start_npc_id="204817" />
	<!-- [Group] Making a Blue Flame Ward -->
	<item_collecting id="2679" start_npc_id="204817" />
	<!-- Amma's Birthstone -->
	<item_collecting id="2680" start_npc_id="204720" />
	<!-- Cure for Stomach Pain -->
	<item_collecting id="2681" start_npc_id="204804" />
	<!-- Comfort Food -->
	<item_collecting id="2682" start_npc_id="798117" />
	<!-- Wishing on the Moon -->
	<item_collecting id="2683" start_npc_id="204745" />
	<!-- Zeller Soup -->
	<item_collecting id="2684" start_npc_id="204786" />
	<!-- Drinking for Two -->
	<item_collecting id="2685" start_npc_id="204788" />
	<!-- Carpe Carpen -->
	<item_collecting id="2686" start_npc_id="204751" />
	<!-- Craving for Almeha -->
	<item_collecting id="2687" start_npc_id="790017" />
	<!-- Divine Asvata Log -->
	<item_collecting id="2688" start_npc_id="204798" />
	<!-- Fruit of Solace -->
	<item_collecting id="2689" start_npc_id="204795" />
	<!-- [Spy] Reliable Report -->
	<report_to id="2690" start_npc_id="204763" end_npc_id="204777" />
	<!-- 2691: [Spy] A Spy's Advice handled by script -->
	<!-- 2692: Broken Music Box handled by script -->
	<!-- 2693: [Spy] [Group] Krall Gone Mad handled by script -->
	<!-- [Spy] Looking for Strange Aether -->
	<item_collecting id="2694" start_npc_id="204803" end_npc_id="204702" />
	<!-- 2695: Atla Rescue Report -->
	<report_to id="2695" start_npc_id="204777" end_npc_id="204208" />
	<!-- [Spy/Group] The Enemy of My Enemy -->
	<monster_hunt id="2696" start_npc_id="204652">
		<monster_infos var_id="0" max_kill="6" npc_id="214521" />
		<monster_infos var_id="1" max_kill="7" npc_id="214516" />
	</monster_hunt>
	<!-- [Group] Contacting Strahein -->
	<report_to id="2697" start_npc_id="204777" end_npc_id="204814" />
	<!-- [Group] Reviving Strahein -->
	<item_collecting id="2698" start_npc_id="204814" action_item_id="700362" />
	<!-- [Group] A Mysterious Book -->
	<item_collecting id="2699" start_npc_id="204774" />
	<!-- 4200: A Suspicious Call handled by script -->
	<!-- The Shugo from Pandaemonium -->
	<report_to id="4221" start_npc_id="204710" end_npc_id="204839" />
	<!-- [Spy/Group] For the Good of All -->
	<item_collecting id="4500" start_npc_id="204652" />
	<!-- 4503 [Group] The Road to Dark Poeta -->
	<item_collecting id="4503" start_npc_id="204837" />
	<!-- 4504 More Priceless Coral -->
	<item_collecting id="4504" start_npc_id="204739" action_item_id="700331"/>
	<!-- 4505 Blue Pearl Dress -->
	<item_collecting id="4505" start_npc_id="204808" action_item_id="700332"/>
	<!-- 4506 Continuing the Hunt -->
	<monster_hunt id="4506" start_npc_id="204763">
		<monster_infos var_id="0" max_kill="11" npc_id="213105" />
		<monster_infos var_id="1" max_kill="7" npc_id="213107" />
	</monster_hunt>
	<!-- 4507 Keep Bringing Me Down! -->
	<item_collecting id="4507" start_npc_id="204792" />
	<!-- 4508 More Crimson Might -->
	<item_collecting id="4508" start_npc_id="204752" />
	<!-- 4509 Aiding the Survivors -->
	<item_collecting id="4509" start_npc_id="204796" />
	<!-- 4510 An Inconspicuous Journey -->
	<monster_hunt id="4510" start_npc_id="204777">
		<monster_infos var_id="0" max_kill="11" npc_id="213217" />
		<monster_infos var_id="1" max_kill="10" npc_id="213219" />
	</monster_hunt>
	<!-- 4511 It's the Wriggots Ore Me! -->
	<item_collecting id="4511" start_npc_id="798118" />
	<!-- 4512 Facing Her Fears -->
	<monster_hunt id="4512" start_npc_id="204801">
		<monster_infos var_id="0" max_kill="15" npc_id="213015" />
	</monster_hunt>
	<!-- 4513 Drakes and Drakies Strike Back! -->
	<item_collecting id="4513" start_npc_id="798119" />
	<!-- 4514 It's Always Cold Season -->
	<item_collecting id="4514" start_npc_id="204725" />
	<!-- 4515 The Cold Earth Rises -->
	<item_collecting id="4515" start_npc_id="204802" />
	<!-- 4516 So Close, Yet So Far -->
	<monster_hunt id="4516" start_npc_id="798119">
		<monster_infos var_id="0" max_kill="11" npc_id="213073" />
		<monster_infos var_id="1" max_kill="11" npc_id="213075" />
		<monster_infos var_id="2" max_kill="1" npc_id="700329" />
	</monster_hunt>
	<!-- 4517 A Big Labor Strike -->
	<monster_hunt id="4517" start_npc_id="204777">
		<monster_infos var_id="0" max_kill="12" npc_id="213513" />
	</monster_hunt>
	<!-- 4518 Luck for Molota's Cub -->
	<item_collecting id="4518" start_npc_id="204798" />
	<!-- 4519 Scarlet Warmth -->
	<item_collecting id="4519" start_npc_id="204790" />
	<!-- 4520 Halting the Blizzards -->
	<monster_hunt id="4520" start_npc_id="204787">
		<monster_infos var_id="0" max_kill="1" npc_id="213118" />
		<monster_infos var_id="1" max_kill="2" npc_id="213121" />
		<monster_infos var_id="2" max_kill="8" npc_id="213236" />
		<monster_infos var_id="3" max_kill="10" npc_id="213239" />
	</monster_hunt>
	<!-- 4521 International News -->
	<report_to id="4521" start_npc_id="204725" end_npc_id="205143" item_id="182204554" />
	<!-- 4522 Gotta Keep Warm! -->
	<item_collecting id="4522" start_npc_id="204777" />
	<!-- 4523 [Coin/Group] Trials By Fire -->
	<monster_hunt id="4523" start_npc_id="204766">
		<monster_infos var_id="0" max_kill="1" npc_id="214028" />
	</monster_hunt>
	<!-- 4524 [Coin/Group] Foolish Recklessness -->
	<monster_hunt id="4524" start_npc_id="204773">
		<monster_infos var_id="0" max_kill="1" npc_id="212588" />
	</monster_hunt>
	<!-- 4525 [Coin/Group] Drakan Gone Qooqoo -->
	<monster_hunt id="4525" start_npc_id="204767">
		<monster_infos var_id="0" max_kill="1" npc_id="213780" />
	</monster_hunt>
	<!-- 4526 [Coin/Group] Menacing Traitor -->
	<monster_hunt id="4526" start_npc_id="204789">
		<monster_infos var_id="0" max_kill="1" npc_id="214598" />
	</monster_hunt>
	<!-- 4527 [Spy/Alliance] To the Island of Eternity -->
	<monster_hunt id="4527" start_npc_id="204702">
		<monster_infos var_id="0" max_kill="1" npc_id="280755" />
		<monster_infos var_id="1" max_kill="1" npc_id="280757" />
		<monster_infos var_id="2" max_kill="1" npc_id="280759" />
	</monster_hunt>
	<!-- 4528 [Coin/Group] It's Alive! Undead? -->
	<monster_hunt id="4528" start_npc_id="204781">
		<monster_infos var_id="0" max_kill="1" npc_id="212211" />
	</monster_hunt>
	<!-- 28600: Suspicious Errand handled by script -->
	<!-- Nightmares and Dreamscapes -->
	<report_to id="28601" start_npc_id="204702" end_npc_id="205234" item_id="182213006" />
	<!-- 28602 Into the Unknown --> 
	<!-- 28604 TODO: Recovering Rotan --> 
	<!-- 28605 Seeking Slumber --> 
	<monster_hunt id="28605" start_npc_id="205235"> 
		<monster_infos npc_id="216971" var_id="0" max_kill="8"/> 
	</monster_hunt>	
	<!-- 28606 An Honorable Death--> 
	<monster_hunt id="28606" start_npc_id="205236"> 
		<monster_infos npc_id="216968" var_id="0" max_kill="1"/> 
	</monster_hunt>	
	<!-- 28607 A Blacksmith's Dream -->
	<item_collecting id="28607" start_npc_id="205237"/>
	<!-- 28608 Judge's Tear-Soaked Sword -->
	<item_collecting id="28608" start_npc_id="205237"/>
	<!-- 28609 Judge's Tear-Soaked Warhammer -->
	<item_collecting id="28609" start_npc_id="205237"/>
	<!-- 28610 Judge's Tear-Soaked Dagger -->
	<item_collecting id="28610" start_npc_id="205237"/>
	<!-- 28611 Judge's Tear-Soaked Jewel -->
	<item_collecting id="28611" start_npc_id="205237"/>
	<!-- 28612 Judge's Tear-Soaked Tome -->
	<item_collecting id="28612" start_npc_id="205237"/>
	<!-- 28613 Judge's Tear-Soaked Greatsword -->
	<item_collecting id="28613" start_npc_id="205237"/>
	<!-- 28614 Judge's Tear-Soaked Spear -->
	<item_collecting id="28614" start_npc_id="205237"/>
	<!-- 28615 Judge's Tear-Soaked Staff -->
	<item_collecting id="28615" start_npc_id="205237"/>
	<!-- 28616 Judge's Tear-Soaked Longbow -->
	<item_collecting id="28616" start_npc_id="205237"/>
	<!-- 28617 [Coin] Confirmation in Dreams --> 
	<monster_hunt id="28617" start_npc_id="204760"> 
		<monster_infos npc_id="217006" var_id="0" max_kill="1"/> 
	</monster_hunt>	
	<!-- 28628 Judge's Flame-Heated Sword -->
	<item_collecting id="28628" start_npc_id="205237"/>
	<!-- 28629 Judge's Flame-Heated Warhammer -->
	<item_collecting id="28629" start_npc_id="205237"/>
	<!-- 28630 Judge's Flame-Heated Dagger -->
	<item_collecting id="28630" start_npc_id="205237"/>
	<!-- 28631 Judge's Flame-Heated Jewel -->
	<item_collecting id="28631" start_npc_id="205237"/>
	<!-- 28632 Judge's Flame-Heated Tome -->
	<item_collecting id="28632" start_npc_id="205237"/>
	<!-- 28633 Judge's Flame-Heated Greatsword -->
	<item_collecting id="28633" start_npc_id="205237"/>
	<!-- 28634 Judge's Flame-Heated Spear -->
	<item_collecting id="28634" start_npc_id="205237"/>
	<!-- 28635 Judge's Flame-Heated Staff -->
	<item_collecting id="28635" start_npc_id="205237"/>
	<!-- 28636 Judge's Flame-Heated Longbow -->
	<item_collecting id="28636" start_npc_id="205237"/>
</quest_scripts>