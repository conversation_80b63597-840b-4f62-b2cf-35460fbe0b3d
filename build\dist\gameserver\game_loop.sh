#!/bin/bash

err=1
until [ $err == 0 ];
do
	java -server -Xmx10240m -ea -XX:+HeapDumpOnOutOfMemoryError -Xbootclasspath/p:./libs/jsr166.jar -cp ./libs/*:ax-game-1.0.1.jar gameserver.GameServer > log/stdout.log 2>&1 &
	err=$?
	gspid=$!
	echo ${gspid} > gameserver.pid
	sleep 10
done

closed=1
until [ $closed == 0 ];

	do
		closed=`ps -p ${gspid} -o comm= | wc -l`
		sleep 5
	done

sleep 2
./game_loop.sh &