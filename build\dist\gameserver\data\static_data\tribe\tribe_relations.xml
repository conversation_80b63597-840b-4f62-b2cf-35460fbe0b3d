<?xml version="1.0" encoding="UTF-8"?>
<tribe_relations>
	<tribe name="PC">
		<friend>
			<to>LIGHT_SUR_MOB</to>
			<to>LIGHT_LICH</to>
		</friend>
	</tribe>
	<tribe name="GENERAL">
		<aggro>
			<to>PC_DARK</to>
			<to>PC_DRAGON</to>
			<to>GUARD_DARK</to>
			<to>GUARD_DRAGON</to>
			<to>GENERAL_DARK</to>
			<to>GENERAL_DRAGON</to>
		</aggro>
		<friend>
			<to>PC</to>
			<to>GUARD</to>
		</friend>
	</tribe>
	<tribe name="GUARD">
		<aggro>
			<to>PC_DARK</to>
			<to>PC_DRAGON</to>
			<to>GUARD_DRAGON</to>
		</aggro>
		<friend>
			<to>PC</to>
		</friend>
		<support>
			<to>GUARD</to>
			<to>GENERAL</to>
			<to>PROTECTGUARD</to>
		</support>
	</tribe>
	<tribe name="PC_DARK">
		<friend>
			<to>DARK_SUR_MOB</to>
			<to>DARK_LICH</to>
		</friend>
		<neutral>
			<to>FIELD_OBJECT_ALL</to>
			<to>FIELD_OBJECT_ALL_HOSTILEMONSTER</to>
		</neutral>
	</tribe>
	<tribe name="GENERAL_DARK">
		<aggro>
			<to>PC</to>
			<to>PC_DRAGON</to>
			<to>GUARD</to>
			<to>GUARD_DRAGON</to>
			<to>GENERAL</to>
			<to>GENERAL_DRAGON</to>
		</aggro>
		<friend>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</friend>
	</tribe>
	<tribe name="GUARD_DARK">
		<aggro>
			<to>PC</to>
			<to>PC_DRAGON</to>
			<to>GUARD_DRAGON</to>
		</aggro>
		<friend>
			<to>PC_DARK</to>
		</friend>
		<support>
			<to>GUARD_DARK</to>
			<to>GENERAL_DARK</to>
			<to>PROTECTGUARD_DARK</to>
		</support>
	</tribe>
	<tribe name="PC_DRAGON">
		<friend>
			<to>USEALL_TELEPORTER_LI</to>
			<to>USEALL_TELEPORTER_DA</to>
		</friend>
	</tribe>
	<tribe name="GENERAL_DRAGON">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>GUARD</to>
			<to>GUARD_DARK</to>
			<to>GENERAL</to>
			<to>GENERAL_DARK</to>
			<to>F4GUARD_DARK</to>
			<to>F4GUARD_LIGHT</to>
		</aggro>
	</tribe>
	<tribe name="GUARD_DRAGON">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>GUARD</to>
			<to>GUARD_DARK</to>
			<to>GENERAL</to>
			<to>GENERAL_DARK</to>
		</aggro>
		<friend>
			<to>USEALL_TELEPORTER_LI</to>
			<to>USEALL_TELEPORTER_DA</to>
		</friend>
		<support>
			<to>GUARD_DRAGON</to>
			<to>GENERAL_DRAGON</to>
		</support>
	</tribe>
	<tribe name="GCHIEF_DRAGON" base="GUARD_DRAGON">
	</tribe>
	<tribe name="GCHIEF_LIGHT" base="GUARD">
	</tribe>
	<tribe name="GCHIEF_DARK" base="GUARD_DARK">
	</tribe>
	<tribe name="WORLDRAID_MONSTER" base="AGGRESSIVESINGLEMONSTER">
	</tribe>
	<!-- <tribe name="GUARD_DRAGON">
		<friend>
			<to>USEALL_TELEPORTER_LI</to>
			<to>USEALL_TELEPORTER_DA</to>
		</friend>
	</tribe> -->
	<tribe name="MONSTER">
		<hostile>
			<to>YUN_GUARD</to>
		</hostile>
		<friend>
			<to>POLYMORPHPARROT</to>
			<to>USEALL_TELEPORTER_LI</to>
			<to>USEALL_TELEPORTER_DA</to>
		</friend>
	</tribe>
	<tribe name="PET" base="PC">
	</tribe>
	<tribe name="PET_DARK" base="PC_DARK">
	</tribe>
	<tribe name="AGGRESSIVESINGLEMONSTER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
	</tribe>
	<tribe name="AGGRESSIVESUPPORTMONSTER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>AGGRESSIVESUPPORTMONSTER</to>
		</support>
	</tribe>
	<tribe name="FRILLFAIMAMCOUPLE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>FRILLFAIMAMCOUPLE</to>
		</support>
	</tribe>
	<tribe name="SPAKY" base="MONSTER">
		<aggro>
			<to>FUNGUS</to>
		</aggro>
		<support>
			<to>SPAKY</to>
		</support>
	</tribe>
	<tribe name="MUTA" base="MONSTER">
		<aggro>
			<to>FUNGUS</to>
		</aggro>
	</tribe>
	<tribe name="FUNGUS" base="MONSTER">
		<friend>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</friend>
	</tribe>
	<tribe name="ORC" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>ORC</to>
			<to>GOBLIN</to>
		</support>
	</tribe>
	<tribe name="GOBLIN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>ORC</to>
			<to>GOBLIN</to>
		</support>
	</tribe>
	<tribe name="POLYMORPHFUNGY" base="PC">
		<friend>
			<to>MONSTER</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>QUESTGUARD_DARK</to>
		</friend>
	</tribe>
	<tribe name="FUNGY" base="MONSTER">
		<aggro>
			<to>POLYMORPHFUNGY</to>
		</aggro>
	</tribe>
	<tribe name="BRAX" base="MONSTER">
		<support>
			<to>BRAX</to>
		</support>
	</tribe>
	<tribe name="HIPPOLIZARD" base="MONSTER">
		<support>
			<to>HIPPOLIZARD</to>
		</support>
	</tribe>
	<tribe name="FRILLFAIMAMBABY" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>FRILLFAIMAMMOM</to>
		</support>
	</tribe>
	<tribe name="FRILLFAIMAMMOM" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>FRILLFAIMAMBABY</to>
		</support>
	</tribe>
	<tribe name="FETHLOT" base="MONSTER">
		<support>
			<to>FETHLOT</to>
		</support>
	</tribe>
	<tribe name="GARGOYLE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>GARGOYLE</to>
		</support>
	</tribe>
	<tribe name="MINX" base="MONSTER">
		<hostile>
			<to>ZAIF</to>
		</hostile>
	</tribe>
	<tribe name="ZAIF" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>MINX</to>
			<to>TOG</to>
		</aggro>
	</tribe>
	<tribe name="TOG" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>MINX</to>
			<to>ZAIF</to>
		</aggro>
	</tribe>
	<tribe name="GUARD_D1NOATTACK" base="GENERAL">
		<friend>
			<to>ZAIF</to>
		</friend>
	</tribe>
	<tribe name="ZAIF_ATOG" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>TOG_AZAIF</to>
		</aggro>
	</tribe>
	<tribe name="TOG_AZAIF" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>ZAIF_ATOG</to>
		</aggro>
	</tribe>
	<tribe name="ZAIF_AMINX" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>MINX_HZAIF</to>
		</aggro>
	</tribe>
	<tribe name="MINX_HZAIF" base="MONSTER">
		<hostile>
			<to>ZAIF_AMINX</to>
		</hostile>
	</tribe>
	<tribe name="MERDION" base="MONSTER">
		<support>
			<to>MERDION</to>
		</support>
	</tribe>
	<tribe name="GUARDIAN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
	</tribe>
	<tribe name="DARU" base="MONSTER">
		<support>
			<to>DARU</to>
		</support>
	</tribe>
	<tribe name="LUPYLLINI" base="MONSTER">
		<support>
			<to>LUPYLLINI</to>
		</support>
	</tribe>
	<tribe name="MOSBEARBABY" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>MOSBEARFATHER</to>
		</support>
	</tribe>
	<tribe name="MOSBEARFATHER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>MOSBEARBABY</to>
		</support>
	</tribe>
	<tribe name="SOULEDSTONE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>SOULEDSTONE_MINI</to>
		</support>
	</tribe>
	<tribe name="SOULEDSTONE_MINI" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
	</tribe>
	<tribe name="MINX_HKALNIF" base="MONSTER">
		<hostile>
			<to>KALNIF_AMINX</to>
		</hostile>
	</tribe>
	<tribe name="KALNIF_AMINX" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>MINX_HKALNIF</to>
		</aggro>
	</tribe>
	<tribe name="KALNIF_ATOG" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>TOG_AKALNIF</to>
		</aggro>
	</tribe>
	<tribe name="TOG_AKALNIF" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>KALNIF_ATOG</to>
		</aggro>
	</tribe>
	<tribe name="FARMER_HKERUBIM_LF1" base="GENERAL">
		<hostile>
			<to>KERUBIM_AFARMER_LF1</to>
		</hostile>
	</tribe>
	<tribe name="KERUBIM_AFARMER_LF1" base="MONSTER">
		<aggro>
			<to>FARMER_HKERUBIM_LF1</to>
		</aggro>
		<hostile>
			<to>GUARD_LIGHT_AKERUBIM_LF1</to>
		</hostile>
	</tribe>
	<tribe name="GUARD_LIGHT_AKERUBIM_LF1" base="GUARD">
		<aggro>
			<to>KERUBIM_AFARMER_LF1</to>
		</aggro>
	</tribe>
	<tribe name="D1_HKERUBIM_LF1" base="MONSTER">
		<hostile>
			<to>KERUBIM_AD1_LF1</to>
		</hostile>
	</tribe>
	<tribe name="KERUBIM_AD1_LF1" base="MONSTER">
		<aggro>
			<to>D1_HKERUBIM_LF1</to>
		</aggro>
	</tribe>
	<tribe name="BROWNIE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>KRALL</to>
			<to>BROWNIE</to>
		</support>
	</tribe>
	<tribe name="BROWNIECOWARD" base="MONSTER">
		<support>
			<to>BROWNIECOWARD</to>
		</support>
	</tribe>
	<tribe name="BROWNIEGUARD" base="MONSTER">
		<support>
			<to>KRALL</to>
			<to>BROWNIE</to>
			<to>BROWNIECOWARD</to>
		</support>
	</tribe>
	<tribe name="BROWNIEFELLER_HZAIF_LF1" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<hostile>
			<to>ZAIF_ABROWNIEFELLER_LF1</to>
		</hostile>
	</tribe>
	<tribe name="ZAIF_ABROWNIEFELLER_LF1" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>BROWNIEFELLER_HZAIF_LF1</to>
		</aggro>
	</tribe>
	<tribe name="KRALL" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>KRALL</to>
			<to>KRALLMASTER</to>
			<to>KRALL_TRAINING</to>
		</support>
	</tribe>
	<tribe name="KRALLMASTER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
	</tribe>
	<tribe name="KRALLWIZARDCY" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>CYCLOPSBOSS</to>
		</aggro>
		<support>
			<to>KRALL</to>
		</support>
	</tribe>
	<tribe name="KRALL_TRAINING" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>TARGETBASFELT_DF1</to>
		</aggro>
		<support>
			<to>KRALL</to>
			<to>KRALLMASTER</to>
			<to>KRALL_TRAINING</to>
		</support>
	</tribe>
	<tribe name="SPRIGG_HROBBERALDER_DF1" base="MONSTER">
		<hostile>
			<to>ROBBERALDER_ASPRIGG_DF1</to>
		</hostile>
	</tribe>
	<tribe name="ROBBERALDER_ASPRIGG_DF1" base="GENERAL_DARK">
		<aggro>
			<to>SPRIGG_HROBBERALDER_DF1</to>
		</aggro>
	</tribe>
	<tribe name="SPRIGGREFUSE_DF1" base="MONSTER">
		<support>
			<to>SPRIGGREFUSE_DF1</to>
		</support>
	</tribe>
	<tribe name="ARCHERYBASFELT_ATARGETBASFELT_DF1" base="GENERAL_DARK">
		<aggro>
			<to>TARGETBASFELT_DF1</to>
		</aggro>
	</tribe>
	<tribe name="ARCHERYBASFELT_ATARGETBASFELT_LF1" base="GENERAL">
		<aggro>
			<to>TARGETBASFELT_DF1</to>
		</aggro>
	</tribe>
	<tribe name="TARGETBASFELT_DF1" base="MONSTER">
	</tribe>
	<tribe name="ARCHERYBASFELT2_ATARGETBASFELT2_DF1" base="GENERAL_DARK">
		<aggro>
			<to>TARGETBASFELT2_DF1</to>
		</aggro>
	</tribe>
	<tribe name="ARCHERYBASFELT2_ATARGETBASFELT2_LF1" base="GENERAL">
		<aggro>
			<to>TARGETBASFELT2_DF1</to>
		</aggro>
	</tribe>
	<tribe name="TARGETBASFELT2_DF1" base="MONSTER">
	</tribe>
	<tribe name="GUARD_FTARGETBASFELT_DF1" base="GUARD_DARK">
		<friend>
			<to>TARGETBASFELT_DF1</to>
			<to>TARGETBASFELT2_DF1</to>
		</friend>
	</tribe>
	<tribe name="GUARD_FTARGETBASFELT_LF1" base="GUARD">
		<friend>
			<to>TARGETBASFELT_DF1</to>
			<to>TARGETBASFELT2_DF1</to>
		</friend>
	</tribe>
	<tribe name="LYCAN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>LYCAN</to>
			<to>LYCAN_MAGE</to>
			<to>LYCAN_PET</to>
			<to>LYCAN_HUNTER</to>
			<to>RATMANWORKER</to>
			<to>LYCANMASTER</to>
			<to>TOWERMAN</to>
			<to>LYCAN_AGUARD_DARK_DF1</to>
			<to>LYCAN_TRAINING</to>
		</support>
	</tribe>
	<tribe name="LYCAN_HUNTER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>LYCAN</to>
			<to>LYCAN_MAGE</to>
			<to>LYCAN_PET</to>
			<to>LYCAN_HUNTER</to>
			<to>RATMANWORKER</to>
			<to>LYCANMASTER</to>
			<to>TOWERMAN</to>
			<to>LYCAN_AGUARD_DARK_DF1</to>
			<to>LYCAN_TRAINING</to>
		</support>
	</tribe>
	<tribe name="LYCAN_MAGE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>LYCAN</to>
			<to>LYCAN_MAGE</to>
			<to>LYCAN_PET</to>
			<to>LYCAN_HUNTER</to>
			<to>RATMANWORKER</to>
			<to>LYCANMASTER</to>
			<to>TOWERMAN</to>
			<to>LYCAN_AGUARD_DARK_DF1</to>
			<to>LYCAN_TRAINING</to>
		</support>
	</tribe>
	<tribe name="LYCAN_AGUARD_DARK_DF1" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>GUARD_DARK_ALYCANARATMAN_DF1</to>
		</aggro>
		<support>
			<to>LYCAN</to>
			<to>LYCAN_MAGE</to>
			<to>LYCAN_PET</to>
			<to>LYCAN_HUNTER</to>
			<to>RATMANWORKER</to>
			<to>LYCANMASTER</to>
			<to>TOWERMAN</to>
			<to>LYCAN_AGUARD_DARK_DF1</to>
			<to>LYCAN_TRAINING</to>
		</support>
	</tribe>
	<tribe name="LYCAN_TRAINING" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>TARGETBASFELT_DF1</to>
		</aggro>
		<support>
			<to>LYCAN</to>
			<to>LYCAN_MAGE</to>
			<to>LYCAN_PET</to>
			<to>LYCAN_HUNTER</to>
			<to>RATMANWORKER</to>
			<to>LYCANMASTER</to>
			<to>TOWERMAN</to>
			<to>LYCAN_AGUARD_DARK_DF1</to>
			<to>LYCAN_TRAINING</to>
		</support>
	</tribe>
	<tribe name="LYCANMASTER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>LYCAN</to>
		</support>
	</tribe>
	<tribe name="LYCAN_PET" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>LYCAN_HUNTER</to>
		</support>
	</tribe>
	<tribe name="LYCAN_PET_TRAINING" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>TARGETBASFELT_DF1</to>
		</aggro>
		<support>
			<to>LYCAN_HUNTER</to>
		</support>
	</tribe>
	<tribe name="LYCAN_SUM" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>LYCAN_MAGE</to>
		</support>
	</tribe>
	<tribe name="LYCANDF2MASTER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>LYCANDF2SLAVE1</to>
		</support>
	</tribe>
	<tribe name="LYCANDF2SLAVE1" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>LYCANDF2MASTER</to>
		</support>
	</tribe>
	<tribe name="LYCANDF2SLAVE2" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>LYCANDF2MASTER</to>
		</support>
	</tribe>
	<tribe name="TOWERMAN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
	</tribe>
	<tribe name="RATMAN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>TOWERMAN</to>
		</support>
	</tribe>
	<tribe name="RATMANWORKER" base="MONSTER">
		<aggro>
			<to>SAMM</to>
		</aggro>
		<support>
			<to>RATMAN</to>
			<to>RATMANWORKER</to>
			<to>LYCANMASTER</to>
			<to>LYCAN</to>
			<to>LYCAN_AGUARD_DARK_DF1</to>
			<to>RATMAN_AGUARD_DARK_DF1</to>
		</support>
	</tribe>
	<tribe name="RATMANDFWORKER" base="MONSTER">
		<aggro>
			<to>SAMM</to>
		</aggro>
		<support>
			<to>RATMAN</to>
			<to>RATMANWORKER</to>
			<to>RATMANDFWORKER</to>
		</support>
	</tribe>
	<tribe name="SAMM" base="MONSTER">
		<hostile>
			<to>RATMANWORKER</to>
		</hostile>
	</tribe>
	<tribe name="RATMAN_AGUARD_DARK_DF1" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK_ALYCANARATMAN_DF1</to>
		</aggro>
		<support>
			<to>TOWERMAN</to>
		</support>
	</tribe>
	<tribe name="GUARD_DARK_ALYCANARATMAN_DF1" base="GUARD_DARK">
		<aggro>
			<to>LYCAN_AGUARD_DARK_DF1</to>
			<to>RATMAN_AGUARD_DARK_DF1</to>
		</aggro>
	</tribe>
	<tribe name="UNDEADGRADIATOR_DF1" base="MONSTER">
		<aggro>
			<to>UNDEADGRADIATOR_DF1</to>
		</aggro>
	</tribe>
	<tribe name="GUARD_LIGHTMA" base="GUARD">
		<friend>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
		</friend>
	</tribe>
	<tribe name="GUARD_DARKMA" base="GUARD_DARK">
		<friend>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</friend>
	</tribe>
	<tribe name="GUARD_DRAGONMA" base="GUARD_DRAGON">
		<friend>
			<to>PC_DRAGON</to>
			<to>GENERAL_DRAGON</to>
			<to>GUARD_DRAGON</to>
		</friend>
	</tribe>
	<tribe name="NEUT" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>NEUT</to>
			<to>NEUTQUEEN</to>
		</support>
	</tribe>
	<tribe name="NEUTQUEEN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>NEUT</to>
			<to>NEUTQUEEN</to>
		</support>
	</tribe>
	<tribe name="NEUTBUG" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>NEUTQUEEN</to>
		</support>
	</tribe>
	<tribe name="AGGRESSIVE_LIGHT" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
		</aggro>
	</tribe>
	<tribe name="AGGRESSIVE_DARK" base="MONSTER">
		<aggro>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
	</tribe>
	<tribe name="AGGRESSIVE_DRAGON" base="MONSTER">
		<aggro>
			<to>PC_DRAGON</to>
			<to>GENERAL_DRAGON</to>
			<to>GUARD_DRAGON</to>
		</aggro>
	</tribe>
	<tribe name="POLYMORPHPARROT" base="PC">
		<friend>
			<to>MONSTER</to>
		</friend>
	</tribe>
	<tribe name="ABDRAGON_AABDARK" base="MONSTER">
		<aggro>
			<to>ABDARK_AABDRAGON</to>
		</aggro>
	</tribe>
	<tribe name="ABDARK_AABDRAGON" base="MONSTER">
		<aggro>
			<to>ABDRAGON_AABDARK</to>
		</aggro>
	</tribe>
	<tribe name="OCTASIDEBABY" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>OCTASIDEBABY</to>
		</support>
	</tribe>
	<tribe name="GUARD_DARKAENEMY" base="GUARD_DARK">
		<aggro>
			<to>ENEMY_AGUARD_DARK</to>
		</aggro>
	</tribe>
	<tribe name="ENEMY_AGUARD_DARK" base="MONSTER">
		<aggro>
			<to>GUARD_DARKAENEMY</to>
		</aggro>
	</tribe>
	<tribe name="GRIFFO" base="MONSTER">
		<support>
			<to>GRIFFON</to>
			<to>GRIFFO</to>
		</support>
	</tribe>
	<tribe name="GRIFFON" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>GRIFFO</to>
		</support>
	</tribe>
	<tribe name="SHELLIZARDMOM" base="MONSTER">
		<support>
			<to>SHELLIZARDBABY</to>
		</support>
	</tribe>
	<tribe name="SHELLIZARDBABY" base="MONSTER">
		<support>
			<to>SHELLIZARDMOM</to>
		</support>
	</tribe>
	<tribe name="MANDURITWEAK" base="MONSTER">
		<support>
			<to>MANDURITWEAK</to>
		</support>
	</tribe>
	<tribe name="CHERUBIM2ND" base="MONSTER">
		<support>
			<to>CHERUBIM2ND</to>
		</support>
	</tribe>
	<tribe name="LEHPAR" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>LEHPAR</to>
			<to>LEHPAR_AGUARDDARK</to>
			<to>LEHPAR_APRETOR</to>
		</support>
	</tribe>
	<tribe name="GUARDDARK_ALEHPAR" base="GUARD_DARK">
		<aggro>
			<to>LEHPAR_AGUARDDARK</to>
		</aggro>
	</tribe>
	<tribe name="LEHPAR_AGUARDDARK" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>GUARDDARK_ALEHPAR</to>
		</aggro>
		<support>
			<to>LEHPAR</to>
			<to>LEHPAR_AGUARDDARK</to>
			<to>LEHPAR_APRETOR</to>
		</support>
	</tribe>
	<tribe name="AGGRESSIVE1_AAGGRESSIVE2" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>AGGRESSIVE2_AAGGRESSIVE1</to>
		</aggro>
	</tribe>
	<tribe name="AGGRESSIVE2_AAGGRESSIVE1" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>AGGRESSIVE1_AAGGRESSIVE2</to>
		</aggro>
	</tribe>
	<tribe name="LEHPAR_APRETOR" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>PRETOR_ALEHPAR</to>
		</aggro>
		<support>
			<to>LEHPAR</to>
			<to>LEHPAR_AGUARDDARK</to>
			<to>LEHPAR_APRETOR</to>
		</support>
	</tribe>
	<tribe name="PRETOR_ALEHPAR" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>LEHPAR_APRETOR</to>
		</aggro>
	</tribe>
	<tribe name="CYCLOPSBOSS" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>KRALLWIZARDCY</to>
		</aggro>
	</tribe>
	<tribe name="FIREELBOSS" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
	</tribe>
	<tribe name="FIREEL1" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>FIREEL1</to>
			<to>FIREELBOSS</to>
		</support>
	</tribe>
	<tribe name="FIREEL2" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>FIREEL2</to>
			<to>FIREELBOSS</to>
		</support>
	</tribe>
	<tribe name="FIREEL3" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>FIREEL3</to>
			<to>FIREELBOSS</to>
		</support>
	</tribe>
	<tribe name="AIRELBOSS" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
	</tribe>
	<tribe name="AIREL1" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>AIREL1</to>
			<to>AIRELBOSS</to>
		</support>
	</tribe>
	<tribe name="AIREL2" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>AIREL2</to>
			<to>AIRELBOSS</to>
		</support>
	</tribe>
	<tribe name="AIREL3" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>AIREL3</to>
			<to>AIRELBOSS</to>
		</support>
	</tribe>
	<tribe name="ETTIN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>ETTIN</to>
		</support>
	</tribe>
	<tribe name="L_DRGUARD_ADRAGGMOB1" base="GUARD">
		<aggro>
			<to>DRAGGMOB_ADRGUARD1</to>
		</aggro>
	</tribe>
	<tribe name="DRAGGMOB_ADRGUARD1" base="MONSTER">
		<aggro>
			<to>L_DRGUARD_ADRAGGMOB1</to>
		</aggro>
	</tribe>
	<tribe name="DARU_HZAIF" base="MONSTER">
		<hostile>
			<to>ZAIF_ADARU</to>
		</hostile>
	</tribe>
	<tribe name="ZAIF_ADARU" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>DARU_HZAIF</to>
		</aggro>
	</tribe>
	<tribe name="MUTA_HOCTASIDE" base="MONSTER">
		<hostile>
			<to>OCTASIDE_AMUTA</to>
		</hostile>
	</tribe>
	<tribe name="OCTASIDE_AMUTA" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>MUTA_HOCTASIDE</to>
		</aggro>
	</tribe>
	<tribe name="LIZARDMAN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>LIZARDMAN</to>
		</support>
	</tribe>
	<tribe name="ATAURIC" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>AHELLHOUND</to>
		</support>
	</tribe>
	<tribe name="AHELLHOUND" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>ATAURIC</to>
		</support>
	</tribe>
	<tribe name="APRETOR" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>ATAURIC</to>
		</support>
	</tribe>
	<tribe name="GHOSTLIGHT" base="GENERAL">
		<aggro>
			<to>PC_DARK</to>
		</aggro>
		<friend>
			<to>PC</to>
		</friend>
		<support>
			<to>GHOSTLIGHT</to>
		</support>
	</tribe>
	<tribe name="GHOSTDARK" base="GENERAL_DARK">
		<aggro>
			<to>PC</to>
		</aggro>
		<friend>
			<to>PC_DARK</to>
		</friend>
		<support>
			<to>GHOSTDARK</to>
		</support>
	</tribe>
	<tribe name="BMLGUARDIAN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>BMLGUARDIAN</to>
		</support>
	</tribe>
	<tribe name="BMDGUARDIAN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>BMDGUARDIAN</to>
		</support>
	</tribe>
	<tribe name="GMASTER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>GSLAVE</to>
		</support>
	</tribe>
	<tribe name="GSLAVE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>GMASTER</to>
		</support>
	</tribe>
	<tribe name="ELEMENTAL_FIRE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>ELEMENTAL_FIRE</to>
		</support>
	</tribe>
	<tribe name="ELEMENTAL_EARTH" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>ELEMENTAL_EARTH</to>
		</support>
	</tribe>
	<tribe name="ELEMENTAL_AIR" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>ELEMENTAL_AIR</to>
		</support>
	</tribe>
	<tribe name="ELEMENTAL_WATER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>ELEMENTAL_WATER</to>
		</support>
	</tribe>
	<tribe name="BAT_FAMILY_ELITE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>BAT_FAMILY_ELITE</to>
		</support>
	</tribe>
	<tribe name="SUCCUBUS_ELITE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>SUCCUBUS_ELITE</to>
		</support>
	</tribe>
	<tribe name="SAM_ELITE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>GARGOYLE_ELITE</to>
			<to>SAM_ELITE</to>
			<to>MAIDENGOLEM_ELITE</to>
		</support>
	</tribe>
	<tribe name="MAIDENGOLEM_ELITE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>GARGOYLE_ELITE</to>
			<to>SAM_ELITE</to>
			<to>MAIDENGOLEM_ELITE</to>
		</support>
	</tribe>
	<tribe name="GARGOYLE_ELITE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>GARGOYLE_ELITE</to>
			<to>SAM_ELITE</to>
			<to>MAIDENGOLEM_ELITE</to>
		</support>
	</tribe>
	<tribe name="AGGRESSIVE_LIGHT_HSPECTRE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
		</aggro>
		<hostile>
			<to>SPECTRE_AALIGHTDARK</to>
		</hostile>
	</tribe>
	<tribe name="AGGRESSIVE_DARK_HSPECTRE" base="MONSTER">
		<aggro>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<hostile>
			<to>SPECTRE_AALIGHTDARK</to>
		</hostile>
	</tribe>
	<tribe name="SPECTRE_AALIGHTDARK" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>AGGRESSIVE_LIGHT_HSPECTRE</to>
			<to>AGGRESSIVE_DARK_HSPECTRE</to>
		</aggro>
	</tribe>
	<tribe name="DRAMA_EVE_NONPC_A" base="GENERAL">
		<aggro>
			<to>DRAMA_EVE_NONPC_B</to>
		</aggro>
	</tribe>
	<tribe name="DRAMA_EVE_NONPC_B" base="GENERAL">
		<aggro>
			<to>DRAMA_EVE_NONPC_A</to>
		</aggro>
	</tribe>
	<tribe name="DRAMA_KIMEIA_NPC" base="GENERAL">
		<friend>
			<to>DRAMA_KIMEIA_MOB</to>
		</friend>
	</tribe>
	<tribe name="DRAMA_EVE_NONPC_DARKA" base="GENERAL_DARK">
		<aggro>
			<to>DRAMA_EVE_NONPC_B</to>
		</aggro>
	</tribe>
	<tribe name="DRAMA_EVE_NONPC_DARKB" base="GENERAL_DARK">
		<aggro>
			<to>DRAMA_EVE_NONPC_A</to>
		</aggro>
	</tribe>
	<tribe name="DRAMA_KIMEIA_DARKNPC" base="GENERAL_DARK">
		<friend>
			<to>DRAMA_KIMEIA_MOB</to>
		</friend>
	</tribe>
	<tribe name="DRAMA_KIMEIA_MOB" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>DRAMA_KIMEIA_NPC</to>
			<to>DRAMA_KIMEIA_DARKNPC</to>
		</aggro>
	</tribe>
	<tribe name="XIPETO" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
	</tribe>
	<tribe name="XIPETOBABY" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>XIPETO</to>
		</support>
	</tribe>
	<tribe name="TAURIC" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>TAURIC</to>
		</support>
	</tribe>
	<tribe name="CRESTLICH" base="MONSTER">
		<support>
			<to>CRESTLICH</to>
		</support>
	</tribe>
	<tribe name="NNAGA" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>NLIZARDMAN</to>
			<to>NLIZARDRAISER</to>
			<to>NLIZARDPET</to>
			<to>NLIZARDPRIEST</to>
			<to>NNAGA_PRIEST</to>
			<to>NNAGA</to>
			<to>NNAGA_ELEMENTALIST</to>
			<to>NNAGA_PRIESTBOSS</to>
			<to>XDRAKAN</to>
			<to>XDRAKAN_ELEMENTALIST</to>
			<to>XDRAKAN_PRIEST</to>
			<to>DRAGON</to>
		</support>
	</tribe>
	<tribe name="NNAGA_PRIEST" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>NLIZARDMAN</to>
			<to>NLIZARDRAISER</to>
			<to>NLIZARDPET</to>
			<to>NLIZARDPRIEST</to>
			<to>NNAGA_PRIEST</to>
			<to>NNAGA</to>
			<to>NNAGA_ELEMENTALIST</to>
			<to>NNAGA_PRIESTBOSS</to>
			<to>NNAGA_SERVANT</to>
			<to>XDRAKAN</to>
			<to>XDRAKAN_ELEMENTALIST</to>
			<to>XDRAKAN_PRIEST</to>
			<to>DRAGON</to>
		</support>
	</tribe>
	<tribe name="NNAGA_SERVANT" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>NNAGA_PRIEST</to>
		</support>
	</tribe>
	<tribe name="NNAGA_ELEMENTALIST" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>NLIZARDMAN</to>
			<to>NLIZARDRAISER</to>
			<to>NLIZARDPET</to>
			<to>NLIZARDPRIEST</to>
			<to>NNAGA_PRIEST</to>
			<to>NNAGA</to>
			<to>NNAGA_ELEMENTALIST</to>
			<to>NNAGA_PRIESTBOSS</to>
			<to>NNAGA_ELEMENTAL</to>
			<to>XDRAKAN</to>
			<to>XDRAKAN_ELEMENTALIST</to>
			<to>XDRAKAN_PRIEST</to>
			<to>DRAGON</to>
		</support>
	</tribe>
	<tribe name="NNAGA_ELEMENTAL" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>NNAGA_ELEMENTALIST</to>
		</support>
	</tribe>
	<tribe name="NLIZARDMAN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>NLIZARDMAN</to>
			<to>NLIZARDRAISER</to>
			<to>NLIZARDPET</to>
			<to>NLIZARDPRIEST</to>
			<to>NNAGA_PRIEST</to>
			<to>NNAGA</to>
			<to>NNAGA_ELEMENTALIST</to>
			<to>NNAGA_PRIESTBOSS</to>
			<to>XDRAKAN</to>
			<to>XDRAKAN_ELEMENTALIST</to>
			<to>XDRAKAN_PRIEST</to>
			<to>DRAGON</to>
			<to>NLIZARDMAN2</to>
		</support>
	</tribe>
	<tribe name="NLIZARDRAISER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>NLIZARDMAN</to>
			<to>NLIZARDRAISER</to>
			<to>NLIZARDPET</to>
			<to>NLIZARDPRIEST</to>
			<to>NNAGA_PRIEST</to>
			<to>NNAGA</to>
			<to>NNAGA_ELEMENTALIST</to>
			<to>NNAGA_PRIESTBOSS</to>
			<to>XDRAKAN</to>
			<to>XDRAKAN_ELEMENTALIST</to>
			<to>XDRAKAN_PRIEST</to>
			<to>DRAGON</to>
		</support>
	</tribe>
	<tribe name="NLIZARDPET" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>NLIZARDRAISER</to>
		</support>
	</tribe>
	<tribe name="NLIZARDPRIEST" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>NLIZARDMAN</to>
			<to>NLIZARDRAISER</to>
			<to>NLIZARDPET</to>
			<to>NLIZARDPRIEST</to>
			<to>NNAGA_PRIEST</to>
			<to>NNAGA</to>
			<to>NNAGA_ELEMENTALIST</to>
			<to>NNAGA_PRIESTBOSS</to>
			<to>XDRAKAN</to>
			<to>XDRAKAN_ELEMENTALIST</to>
			<to>XDRAKAN_PRIEST</to>
			<to>DRAGON</to>
		</support>
	</tribe>
	<tribe name="NNAGA_PRIESTBOSS" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>NLIZARDMAN</to>
			<to>NLIZARDRAISER</to>
			<to>NLIZARDPET</to>
			<to>NLIZARDPRIEST</to>
			<to>NNAGA_PRIEST</to>
			<to>NNAGA</to>
			<to>NNAGA_ELEMENTALIST</to>
			<to>XDRAKAN</to>
			<to>XDRAKAN_ELEMENTALIST</to>
			<to>XDRAKAN_PRIEST</to>
			<to>DRAGON</to>
		</support>
	</tribe>
	<tribe name="NNAGA_BOSS_SERVANT" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>NNAGA_PRIESTBOSS</to>
		</support>
	</tribe>
	<tribe name="SEIREN_MASTER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>SEIREN_MASTER</to>
			<to>SEIREN_SNAKE</to>
			<to>SEIREN</to>
		</support>
	</tribe>
	<tribe name="SEIREN_SNAKE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>SEIREN_MASTER</to>
		</support>
	</tribe>
	<tribe name="GENERAL_ADADR" base="GENERAL">
		<aggro>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
			<to>PC_DRAGON</to>
			<to>GENERAL_DRAGON</to>
			<to>GUARD_DRAGON</to>
		</aggro>
		<friend>
			<to>F4RAID</to>
		</friend>
		<support>
			<to>PC</to>
		</support>
	</tribe>
	<tribe name="GENERALDA_ALIDR" base="GENERAL_DARK">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DRAGON</to>
			<to>GENERAL_DRAGON</to>
			<to>GUARD_DRAGON</to>
		</aggro>
		<friend>
			<to>F4RAID</to>
		</friend>
		<support>
			<to>PC_DARK</to>
		</support>
	</tribe>
	<tribe name="GENERALDR_ALIDA" base="GENERAL_DRAGON">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<friend>
			<to>F4RAID</to>
		</friend>
	</tribe>
	<tribe name="LIZARDMAN_BOMB" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<friend>
			<to>BOMB_LIZARDMAN</to>
		</friend>
		<support>
			<to>LIZARDMAN_BOMB</to>
		</support>
	</tribe>
	<tribe name="BOMB_LIZARDMAN" base="GENERAL_DARK">
		<aggro>
			<to>LIZARDMAN_BOMB</to>
		</aggro>
		<friend>
			<to>NNAGA</to>
		</friend>
	</tribe>
	<tribe name="LIZARDMAN_KB" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
	</tribe>
	<tribe name="DRAKEPURPLE_MASTER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
	</tribe>
	<tribe name="DRAKEPURPLE_SLAVE" base="MONSTER">
		<support>
			<to>DRAKEPURPLE_MASTER</to>
		</support>
	</tribe>
	<tribe name="DRAKY_BOMB_MASTER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>NLIZARDMAN</to>
			<to>NLIZARDRAISER</to>
			<to>NLIZARDPET</to>
			<to>NLIZARDPRIEST</to>
			<to>NNAGA_PRIEST</to>
			<to>NNAGA</to>
			<to>NNAGA_ELEMENTALIST</to>
		</support>
	</tribe>
	<tribe name="DRAKY_BOMB_EX" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>DRAKY_BOMB_MASTER</to>
		</support>
	</tribe>
	<tribe name="FIREFUNGY" base="MONSTER">
		<support>
			<to>FIREFUNGY</to>
		</support>
	</tribe>
	<tribe name="NOFIGHT" base="MONSTER">
		<friend>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</friend>
	</tribe>
	<tribe name="FIELD_OBJECT_ALL" base="FIELD_OBJECT_LIGHT">
		<neutral>
			<to>PC_DARK</to>
		</neutral>
	</tribe>
	<tribe name="DRAKANDF3BOSS" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>DRAKANDF3SLAVE</to>
			<to>DRAKANDF3BOSS</to>
		</support>
	</tribe>
	<tribe name="DRAKANDF3SLAVE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>DRAKANDF3BOSS</to>
		</support>
	</tribe>
	<tribe name="CONSIADE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
		</aggro>
	</tribe>
	<tribe name="CONSIADE_SUM" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
		</aggro>
		<support>
			<to>CONSIADE</to>
		</support>
	</tribe>
	<tribe name="PARENTSMONSTER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<friend>
			<to>CHILDMONSTER</to>
		</friend>
	</tribe>
	<tribe name="CHILDMONSTER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<friend>
			<to>PARENTSMONSTER</to>
		</friend>
	</tribe>
	<tribe name="SEIREN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>SEIREN_MASTER</to>
			<to>SEIREN_SNAKE</to>
			<to>SEIREN</to>
		</support>
	</tribe>
	<tribe name="TEST_LIGHT_ADARK" base="GENERAL">
		<aggro>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
	</tribe>
	<tribe name="TEST_LIGHT_ADRAGON" base="GENERAL">
		<aggro>
			<to>PC_DRAGON</to>
			<to>GENERAL_DRAGON</to>
			<to>GUARD_DRAGON</to>
		</aggro>
	</tribe>
	<tribe name="TEST_LIGHT_AETC" base="GENERAL">
		<aggro>
			<to>MONSTER</to>
		</aggro>
	</tribe>
	<tribe name="TEST_DARK_ALIGHT" base="GENERAL_DARK">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
		</aggro>
	</tribe>
	<tribe name="TEST_DARK_ADRAGON" base="GENERAL_DARK">
		<aggro>
			<to>PC_DRAGON</to>
			<to>GENERAL_DRAGON</to>
			<to>GUARD_DRAGON</to>
		</aggro>
	</tribe>
	<tribe name="TEST_DARK_AETC" base="GENERAL_DARK">
		<aggro>
			<to>MONSTER</to>
		</aggro>
	</tribe>
	<tribe name="TEST_DRAGON_ALIGHT" base="GENERAL_DRAGON">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
		</aggro>
	</tribe>
	<tribe name="TEST_DRAGON_ADARK" base="GENERAL_DRAGON">
		<aggro>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
	</tribe>
	<tribe name="TEST_DRAGON_AETC" base="GENERAL_DRAGON">
		<aggro>
			<to>MONSTER</to>
		</aggro>
	</tribe>
	<tribe name="TEST_ETC_ALIGHT" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
		</aggro>
	</tribe>
	<tribe name="TEST_ETC_ADARK" base="MONSTER">
		<aggro>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
	</tribe>
	<tribe name="TEST_ETC_ADRAGON" base="MONSTER">
		<aggro>
			<to>PC_DRAGON</to>
			<to>GENERAL_DRAGON</to>
			<to>GUARD_DRAGON</to>
		</aggro>
	</tribe>
	<tribe name="ABYSSDRAKANGATE" base="MONSTER">
		<friend>
			<to>GUARD</to>
			<to>GUARD_DARK</to>
			<to>GUARD_DRAGON</to>
			<to>PC</to>
			<to>PC_DARK</to>
		</friend>
	</tribe>
	<tribe name="DARK_NPC" base="GENERAL_DARK">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
		</aggro>
		<friend>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
			<to>DARK_SUR_MOB</to>
			<to>DARK_LICH</to>
			<to>LICH_SOULEDSTONE</to>
			<to>DARK_MOB</to>
		</friend>
	</tribe>
	<tribe name="LIGHT_NPC" base="GENERAL">
		<aggro>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<friend>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>LIGHT_SUR_MOB</to>
			<to>LIGHT_LICH</to>
			<to>LICH_SOULEDSTONE</to>
			<to>LIGHT_MOB</to>
		</friend>
	</tribe>
	<tribe name="DARK_MOB" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
		</aggro>
		<friend>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</friend>
		<support>
			<to>DARK_NPC</to>
		</support>
	</tribe>
	<tribe name="LIGHT_MOB" base="MONSTER">
		<aggro>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<friend>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
		</friend>
		<support>
			<to>LIGHT_NPC</to>
		</support>
	</tribe>
	<tribe name="DARK_SUR_MOB" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
		</aggro>
		<friend>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</friend>
		<support>
			<to>DARK_NPC</to>
			<to>DARK_SUR_MOB</to>
		</support>
	</tribe>
	<tribe name="LIGHT_SUR_MOB" base="MONSTER">
		<aggro>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<friend>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
		</friend>
		<support>
			<to>LIGHT_NPC</to>
			<to>LIGHT_SUR_MOB</to>
		</support>
	</tribe>
	<tribe name="DARK_LICH" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
		</aggro>
		<friend>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</friend>
		<support>
			<to>DARK_NPC</to>
			<to>DARK_SUR_MOB</to>
		</support>
	</tribe>
	<tribe name="LIGHT_LICH" base="MONSTER">
		<aggro>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<friend>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
		</friend>
		<support>
			<to>LIGHT_NPC</to>
			<to>LIGHT_SUR_MOB</to>
		</support>
	</tribe>
	<tribe name="LICH_SOULEDSTONE" base="MONSTER">
		<friend>
			<to>DARK_LICH</to>
			<to>LIGHT_LICH</to>
		</friend>
	</tribe>
	<tribe name="LIGHT_DENLABIS" base="GENERAL">
		<aggro>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<friend>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>FIRETEMPLE_MOB</to>
		</friend>
	</tribe>
	<tribe name="FIRETEMPLE_MOB" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<friend>
			<to>LIGHT_DENLABIS</to>
		</friend>
		<support>
			<to>FIRETEMPLE_MOB</to>
		</support>
	</tribe>
	<tribe name="QUESTGUARD_DARK" base="GENERAL_DARK">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
		</aggro>
		<support>
			<to>PC_DARK</to>
		</support>
	</tribe>
	<tribe name="QUESTGUARD_LIGHT" base="GENERAL">
		<aggro>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>PC</to>
		</support>
	</tribe>
	<tribe name="BROHUM" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>BROHUM</to>
		</support>
	</tribe>
	<tribe name="CALYDON" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
		</aggro>
		<support>
			<to>CALYDON</to>
		</support>
	</tribe>
	<tribe name="CALYDON_POLYMORPH" base="MONSTER">
		<friend>
			<to>MONSTER</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
		</friend>
	</tribe>
	<tribe name="SWELLFISH" base="MONSTER">
		<support>
			<to>SWELLFISH</to>
		</support>
	</tribe>
	<tribe name="TRICON" base="MONSTER">
		<aggro>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
		</aggro>
		<support>
			<to>TRICON</to>
			<to>TRICO</to>
		</support>
	</tribe>
	<tribe name="TRICO" base="MONSTER">
	</tribe>
	<tribe name="PROTECTGUARD_LIGHT" base="GUARD">
	</tribe>
	<tribe name="PROTECTGUARD_DARK" base="GUARD_DARK">
	</tribe>
	<tribe name="DF5_GUARD_01_DARK" base="GUARD_DARK">
	</tribe>
	<tribe name="DF5_GUARD_02_DARK" base="GUARD_DARK">
	</tribe>
	<tribe name="DF5_GUARD_03_DARK" base="GUARD_DARK">
	</tribe>
	<tribe name="DF5_GUARD_04_DARK" base="GUARD_DARK">
	</tribe>
	<tribe name="TELEPORTER_DA" base="GUARD_DARK">
		<friend>
			<to>MONSTER</to>
		</friend>
	</tribe>
	<tribe name="FIELD_OBJECT_ALL_MONSTER" base="GENERAL">
		<friend>
			<to>MONSTER</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</friend>
	</tribe>
	<tribe name="HOLYSERVANT" base="MONSTER">
		<aggro>
			<to>HOLYSERVANT_DEBUFFER</to>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
	</tribe>
	<tribe name="HOLYSERVANT_DEBUFFER" base="USEALL">
		<aggro>
			<to>HOLYSERVANT</to>
		</aggro>
	</tribe>
	<tribe name="HOLYSERVANT_DESPAWN" base="MONSTER">
		<friend>
			<to>MONSTER</to>
		</friend>
		<support>
			<to>HOLYSERVANT</to>
		</support>
	</tribe>
	<tribe name="RANMARK" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>GENERAL</to>
			<to>GENERAL_DARK</to>
		</aggro>
		<support>
			<to>RANMARK</to>
		</support>
	</tribe>
	<tribe name="AGGRESSIVESUPPORTMONSTER2" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>AGGRESSIVESINGLEMONSTER</to>
			<to>AGGRESSIVESUPPORTMONSTER</to>
		</support>
	</tribe>
	<tribe name="LASBERG" base="GENERAL_DARK">
	</tribe>
	<tribe name="SHULACK" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>AGGRESSIVETOSHULACK2</to>
		</aggro>
		<friend>
			<to>SHULACK_ATTACKED</to>
		</friend>
		<support>
			<to>SHULACK</to>
			<to>SHULACK_DECK</to>
			<to>SHULACK_ATTACKING</to>
			<to>SHULACK_SUPPORT</to>
		</support>
	</tribe>
	<tribe name="ESCORT" base="USEALL">
		<hostile>
			<to>MONSTER</to>
			<to>AGGRESSIVEESCORT</to>
		</hostile>
		<friend>
			<to>PC</to>
			<to>PC_DARK</to>
		</friend>
	</tribe>
	<tribe name="AGGRESSIVEESCORT" base="MONSTER">
		<aggro>
			<to>ESCORT</to>
		</aggro>
	</tribe>
	<tribe name="XDRAKAN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>ESCORT</to>
		</aggro>
		<hostile>
			<to>IDCATACOMBS_TAROS</to>
		</hostile>
		<neutral>
			<to>DRAKAN_LGUARD</to>
			<to>DRAKAN_DGUARD</to>
		</neutral>
		<support>
			<to>XDRAKAN</to>
			<to>XDRAKAN_ELEMENTALIST</to>
			<to>XDRAKAN_PRIEST</to>
			<to>DRAGON</to>
		</support>
	</tribe>
	<tribe name="XDRAKAN_ELEMENTALIST" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>ESCORT</to>
		</aggro>
		<support>
			<to>XDRAKAN</to>
			<to>XDRAKAN_ELEMENTALIST</to>
			<to>XDRAKAN_PRIEST</to>
			<to>DRAGON</to>
		</support>
	</tribe>
	<tribe name="XDRAKAN_PET" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>ESCORT</to>
		</aggro>
		<support>
			<to>XDRAKAN_ELEMENTALIST</to>
		</support>
	</tribe>
	<tribe name="XDRAKAN_PRIEST" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>ESCORT</to>
		</aggro>
		<support>
			<to>XDRAKAN</to>
			<to>XDRAKAN_ELEMENTALIST</to>
			<to>XDRAKAN_PRIEST</to>
			<to>DRAGON</to>
		</support>
	</tribe>
	<tribe name="XDRAKAN_SERVANT" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>ESCORT</to>
		</aggro>
		<support>
			<to>XDRAKAN_PRIEST</to>
		</support>
	</tribe>
	<tribe name="AGGRESSIVETOSHULACK" base="MONSTER">
		<aggro>
			<to>SHULACK</to>
		</aggro>
		<support>
			<to>AGGRESSIVETOSHULACK</to>
		</support>
	</tribe>
	<tribe name="SHULACK_DECK" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>SHULACK</to>
			<to>SHULACK_DECK</to>
		</support>
	</tribe>
	<tribe name="SHULACK_DECK_KILLER" base="MONSTER">
		<aggro>
			<to>SHULACK_DECK</to>
		</aggro>
	</tribe>
	<tribe name="DRAGON" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
	</tribe>
	<tribe name="DRAGON_SLAVE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>DRAGON</to>
		</support>
	</tribe>
	<tribe name="DRAGON_CTRL" base="USEALL">
		<aggro>
			<to>DRAGON</to>
		</aggro>
	</tribe>
	<tribe name="AGGRESSIVETOSHULACK2" base="MONSTER">
		<aggro>
			<to>SHULACK</to>
		</aggro>
	</tribe>
	<tribe name="SPALLER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
	</tribe>
	<tribe name="SPALLERCTRL" base="USEALL">
		<hostile>
			<to>SPALLER</to>
		</hostile>
	</tribe>
	<tribe name="SHULACK_SLAVE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>SHULACK</to>
			<to>SHULACK_SLAVE</to>
		</support>
	</tribe>
	<tribe name="IDLF1_MONSTER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>ESCORT</to>
		</aggro>
		<support>
			<to>IDLF1_MONSTER</to>
		</support>
	</tribe>
	<tribe name="AGGRESSIVETOPCPET" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>GENERAL</to>
			<to>GENERAL_DARK</to>
		</hostile>
	</tribe>
	<tribe name="GENERALDR_ALIDA_SUPPORT" base="GENERAL_DRAGON">
		<support>
			<to>GENERALDR_ALIDA</to>
		</support>
	</tribe>
	<tribe name="GOLEM_SWITCH" base="MONSTER">
		<support>
			<to>AGGRESSIVESINGLEMONSTER</to>
		</support>
	</tribe>
	<tribe name="USEALLNONETOMONSTER" base="USEALL">
	</tribe>
	<tribe name="FANATIC" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>FANATIC</to>
			<to>XDRAKAN_ELEMENTALIST</to>
			<to>XDRAKAN_PRIEST</to>
			<to>DRAGON</to>
			<to>XDRAKAN</to>
		</support>
	</tribe>
	<tribe name="IDTEMPLE_BUGS" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>IDTEMPLE_BUGS</to>
		</support>
	</tribe>
	<tribe name="IDTEMPLE_STONE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>IDTEMPLE_STONE</to>
		</support>
	</tribe>
	<tribe name="IDELIM" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>GENERAL_DARK</to>
			<to>AGGRESSIVETOIDELIM</to>
		</aggro>
		<neutral>
			<to>IDELIM_FRIEND</to>
		</neutral>
		<support>
			<to>IDELIM</to>
		</support>
	</tribe>
	<tribe name="IDELIM_FRIEND" base="MONSTER">
		<hostile>
			<to>IDELIM</to>
		</hostile>
		<friend>
			<to>PC</to>
			<to>GUARD</to>
			<to>GENERAL</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>GENERAL_DARK</to>
			<to>USEALL</to>
		</friend>
	</tribe>
	<tribe name="FRIENDLYTOIDELIM" base="MONSTER">
		<aggro>
			<to>GENERAL</to>
		</aggro>
		<friend>
			<to>IDELIM</to>
		</friend>
	</tribe>
	<tribe name="AGGRESSIVETOIDELIM" base="USEALL">
		<aggro>
			<to>IDELIM</to>
		</aggro>
		<neutral>
			<to>IDELIM_FRIEND</to>
		</neutral>
		<support>
			<to>PC</to>
			<to>GUARD</to>
			<to>GENERAL</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>GENERAL_DARK</to>
		</support>
	</tribe>
	<tribe name="IDCATACOMBS_TAROS" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>XDRAKAN</to>
		</support>
	</tribe>
	<tribe name="IDCATACOMBS_DRAKE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<friend>
			<to>IDCATACOMBS_DRAKE_SUM</to>
		</friend>
	</tribe>
	<tribe name="IDCATACOMBS_DRAKE_SUM" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>IDCATACOMBS_DRAKE</to>
		</aggro>
	</tribe>
	<tribe name="DUMMY" base="MONSTER">
	</tribe>
	<tribe name="HOSTILE_ALL" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>PC_DRAGON</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
			<to>GENERAL_DRAGON</to>
			<to>GUARD_DRAGON</to>
			<to>USEALL</to>
			<to>MONSTER</to>
		</hostile>
	</tribe>
	<tribe name="AGGRESSIVE_ALL" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>PC_DRAGON</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
			<to>GENERAL_DRAGON</to>
			<to>GUARD_DRAGON</to>
			<to>USEALL</to>
			<to>MONSTER</to>
		</aggro>
	</tribe>
	<tribe name="F4GUARD_LIGHT" base="GUARD">
		<friend>
			<to>F4RAID</to>
		</friend>
	</tribe>
	<tribe name="F4GUARD_DARK" base="GUARD_DARK">
		<friend>
			<to>F4RAID</to>
		</friend>
	</tribe>
	<tribe name="F4GUARD_DRAGON" base="GUARD_DRAGON">
		<friend>
			<to>F4RAID</to>
		</friend>
	</tribe>
	<tribe name="F4RAID" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<friend>
			<to>F4GUARD_LIGHT</to>
			<to>F4GUARD_DARK</to>
			<to>GENERAL_ADADR</to>
			<to>GENERALDA_ALIDR</to>
			<to>F4GUARD_DRAGON</to>
			<to>GENERALDR_ALIDA</to>
			<to>GUARD</to>
			<to>GUARD_DARK</to>
		</friend>
	</tribe>
	<tribe name="DRAMATA" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>GENERAL</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>GENERAL_DARK</to>
		</aggro>
		<neutral>
			<to>AGGRESSIVESUPPORTMONSTER</to>
		</neutral>
		<support>
			<to>DRAMATA</to>
		</support>
	</tribe>
	<tribe name="DRAMATATIMERA" base="MONSTER">
	</tribe>
	<tribe name="DRAMATATIMERB" base="MONSTER">
		<aggro>
			<to>DRAMATATIMERA</to>
		</aggro>
	</tribe>
	<tribe name="DRAKANPOLYMORPH" base="MONSTER">
		<aggro>
			<to>DRAKANDOOR</to>
		</aggro>
		<friend>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD_DARK</to>
		</friend>
	</tribe>
	<tribe name="DRAKANDOOR" base="MONSTER">
	</tribe>
	<tribe name="GHTIMER" base="MONSTER">
		<aggro>
			<to>GUARD</to>
			<to>GUARD_DARK</to>
		</aggro>
	</tribe>
	<tribe name="SHULACK_ATTACKING" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>GENERAL</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>GENERAL_DARK</to>
			<to>SHULACK_ATTACKED</to>
		</aggro>
		<support>
			<to>SHULACK</to>
			<to>SHULACK_SUPPORT</to>
		</support>
	</tribe>
	<tribe name="SHULACK_ATTACKED" base="MONSTER">
		<aggro>
			<to>SHULACK_ATTACKING</to>
		</aggro>
	</tribe>
	<tribe name="SHULACK_SUPPORT" base="MONSTER">
		<support>
			<to>SHULACK</to>
			<to>SHULACK_ATTACKING</to>
		</support>
	</tribe>
	<tribe name="NLIZARDMAN2" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
	</tribe>
	<tribe name="DUMMY2" base="MONSTER">
	</tribe>
	<tribe name="DUMMY_DGUARD" base="GUARD_DARK">
		<aggro>
			<to>DUMMY</to>
		</aggro>
	</tribe>
	<tribe name="DUMMY2_DGUARD" base="GUARD_DARK">
		<aggro>
			<to>DUMMY2</to>
		</aggro>
	</tribe>
	<tribe name="CRYSTAL" base="MONSTER">
		<aggro>
			<to>ANTI_CRYSTAL</to>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<friend>
			<to>MONSTER</to>
			<to>CRYSTAL_NMDD</to>
		</friend>
		<support>
			<to>CRYSTAL</to>
		</support>
	</tribe>
	<tribe name="ANTI_CRYSTAL" base="MONSTER">
		<aggro>
			<to>CRYSTAL</to>
		</aggro>
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
		<support>
			<to>ANTI_CRYSTAL</to>
		</support>
	</tribe>
	<tribe name="DUMMY_LGUARD" base="GUARD">
		<aggro>
			<to>DUMMY</to>
		</aggro>
	</tribe>
	<tribe name="DUMMY2_LGUARD" base="GUARD">
		<aggro>
			<to>DUMMY2</to>
		</aggro>
	</tribe>
	<tribe name="HOSTILEONLYMONSTER" base="USEALL">
		<hostile>
			<to>MONSTER</to>
		</hostile>
	</tribe>
	<tribe name="ATKDRAKAN" base="USEALL">
		<aggro>
			<to>MONSTER</to>
			<to>PC_DRAGON</to>
			<to>GENERAL_DRAGON</to>
			<to>GUARD_DRAGON</to>
		</aggro>
	</tribe>
	<tribe name="CRYSTAL_NMDD" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>GUARD</to>
			<to>GUARD_DARK</to>
		</hostile>
		<friend>
			<to>CRYSTAL_SUM</to>
		</friend>
	</tribe>
	<tribe name="CRYSTAL_SUM" base="MONSTER">
		<aggro>
			<to>CRYSTAL_NMDD</to>
		</aggro>
	</tribe>
	<tribe name="GENERAL_KRALL" base="GENERAL">
		<friend>
			<to>KRALL</to>
			<to>KRALL_PC</to>
		</friend>
	</tribe>
	<tribe name="GENERAL_DARK_LYCAN" base="GENERAL_DARK">
		<friend>
			<to>LYCAN</to>
			<to>LYCAN_PC</to>
		</friend>
	</tribe>
	<tribe name="WAVE_TREE" base="USEALL">
		<aggro>
			<to>WAVE_SWARM1</to>
			<to>WAVE_SWARM2</to>
			<to>XDRAKAN</to>
		</aggro>
		<friend>
			<to>PC</to>
			<to>PC_DARK</to>
		</friend>
	</tribe>
	<tribe name="WAVE_SWARM1" base="MONSTER">
		<aggro>
			<to>WAVE_TREE</to>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<friend>
			<to>WAVE_SWARM2</to>
		</friend>
	</tribe>
	<tribe name="WAVE_SWARM2" base="MONSTER">
		<aggro>
			<to>WAVE_TREE</to>
		</aggro>
		<friend>
			<to>WAVE_SWARM1</to>
		</friend>
	</tribe>
	<tribe name="LYCAN_PC" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</hostile>
	</tribe>
	<tribe name="KRALL_PC" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</hostile>
	</tribe>
	<tribe name="IDELEMENTAL2HEALSUM" base="USEALL">
		<aggro>
			<to>AGGRESSIVEMONSTER</to>
		</aggro>
	</tribe>
	<tribe name="AGGRESSIVEMONSTER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
	</tribe>
	<tribe name="IDYUN_XDRAKAN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>IDYUN_ANCIENT</to>
			<to>IDYUN_POLYMORPH</to>
		</aggro>
		<support>
			<to>IDYUN_XDRAKAN</to>
		</support>
	</tribe>
	<tribe name="IDYUN_SIEGEWEAPON" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
	</tribe>
	<tribe name="IDYUN_ANCIENT" base="USEALL">
		<aggro>
			<to>IDYUN_XDRAKAN</to>
		</aggro>
	</tribe>
	<tribe name="IDYUN_HDRAKAN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>IDYUN_D1</to>
		</aggro>
	</tribe>
	<tribe name="IDYUN_ODRAKAN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>IDYUN_OBJECTS</to>
		</aggro>
	</tribe>
	<tribe name="IDYUN_RDRAKAN" base="MONSTER">
		<aggro>
			<to>IDYUN_TARGET</to>
		</aggro>
	</tribe>
	<tribe name="IDYUN_D1" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
	</tribe>
	<tribe name="IDYUN_OBJECTS" base="MONSTER">
	</tribe>
	<tribe name="IDYUN_TARGET" base="MONSTER">
	</tribe>
	<tribe name="IDRAKSHA_DRAKAN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<neutral>
			<to>IDRAKSHA_DRAGONTOOTH</to>
			<to>IDRAKSHA_NORMAL</to>
		</neutral>
		<support>
			<to>IDRAKSHA_DRAKAN</to>
			<to>IDRAKSHA_RAKSHA</to>
		</support>
	</tribe>
	<tribe name="IDRAKSHA_DRAGONTOOTH" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<neutral>
			<to>IDRAKSHA_NORMAL</to>
		</neutral>
		<support>
			<to>IDRAKSHA_DRAKAN</to>
			<to>IDRAKSHA_DRAGONTOOTH</to>
			<to>IDRAKSHA_RAKSHA</to>
		</support>
	</tribe>
	<tribe name="IDRAKSHA_NORMAL" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<neutral>
			<to>IDRAKSHA_DRAKAN</to>
			<to>IDRAKSHA_DRAGONTOOTH</to>
		</neutral>
		<support>
			<to>IDRAKSHA_NORMAL</to>
			<to>IDRAKSHA_RAKSHA</to>
		</support>
	</tribe>
	<tribe name="IDRAKSHA_RAKSHA" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</hostile>
	</tribe>
	<tribe name="DECOY" base="USEALL">
		<friend>
			<to>PC</to>
			<to>PC_DARK</to>
		</friend>
	</tribe>
	<tribe name="DECOY_HUNGER" base="MONSTER">
		<aggro>
			<to>DECOY</to>
		</aggro>
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
		<support>
			<to>DECOY_HUNGER</to>
		</support>
	</tribe>
	<tribe name="IDYUN_BOMBER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>IDYUN_ANCIENT</to>
		</support>
	</tribe>
	<tribe name="IDYUN_ANTIBOMBER" base="MONSTER">
		<hostile>
			<to>IDYUN_BOMBER</to>
		</hostile>
		<friend>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>IDYUN_ANCIENT</to>
		</friend>
	</tribe>
	<tribe name="PREDATOR" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>PREY</to>
		</aggro>
		<support>
			<to>PREDATOR</to>
		</support>
	</tribe>
	<tribe name="PREY" base="MONSTER">
		<hostile>
			<to>PREDATOR</to>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
		<support>
			<to>PREY</to>
		</support>
	</tribe>
	<tribe name="IDYUN_POLYMORPH" base="MONSTER">
		<aggro>
			<to>IDYUN_DOOR</to>
			<to>XDRAKAN</to>
		</aggro>
		<hostile>
			<to>IDYUN_XDRAKAN</to>
		</hostile>
		<friend>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>IDYUN_ANCIENT</to>
		</friend>
	</tribe>
	<tribe name="IDYUN_DOOR" base="MONSTER">
		<aggro>
			<to>IDYUN_VASARTI</to>
		</aggro>
		<hostile>
			<to>IDYUN_SIEGEWEAPON</to>
		</hostile>
		<friend>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>IDYUN_ANCIENT</to>
		</friend>
	</tribe>
	<tribe name="IDYUN_VASARTI" base="MONSTER">
		<aggro>
			<to>IDYUN_MEROPS</to>
		</aggro>
	</tribe>
	<tribe name="IDYUN_MEROPS" base="USEALL">
		<hostile>
			<to>IDYUN_VASARTI</to>
		</hostile>
		<friend>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>IDYUN_ANCIENT</to>
		</friend>
	</tribe>
	<tribe name="IDYUN_FIST" base="USEALL">
		<hostile>
			<to>IDYUN_VASARTI</to>
		</hostile>
		<friend>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>IDYUN_ANCIENT</to>
		</friend>
	</tribe>
	<tribe name="LDF4A_SANDWARM" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>LDF4A_SANDWARM</to>
		</support>
	</tribe>
	<tribe name="LDF4A_NEPILIM" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>LDF4A_NEPILIM</to>
		</support>
	</tribe>
	<tribe name="LDF4A_OWLLAU" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>LDF4A_OWLLAU</to>
		</support>
	</tribe>
	<tribe name="LDF4A_CALYDON" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>LDF4A_CALYDON</to>
		</support>
	</tribe>
	<tribe name="LDF4B_AGGRESSIVEYUNSOLDIER" base="USEALL">
		<aggro>
			<to>LDF4B_FANATIC</to>
		</aggro>
		<hostile>
			<to>AGGRESSIVESINGLEMONSTER</to>
		</hostile>
		<support>
			<to>PC</to>
			<to>PC_DARK</to>
		</support>
	</tribe>
	<tribe name="LDF4B_ATTACKWAGON" base="MONSTER">
		<aggro>
			<to>LDF4B_WAGON</to>
		</aggro>
		<hostile>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</hostile>
	</tribe>
	<tribe name="LDF4B_FANATIC" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<hostile>
			<to>LDF4B_AGGRESSIVEYUNSOLDIER</to>
		</hostile>
		<support>
			<to>LDF4B_FANATIC</to>
		</support>
	</tribe>
	<tribe name="IDLDF4A_DECOY" base="MONSTER">
		<aggro>
			<to>DECOY</to>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>DECOY_HUNGER</to>
		</support>
	</tribe>
	<tribe name="GENERAL_IDLDF4A_INTRO" base="GENERAL">
		<aggro>
			<to>GENERAL_DARK_IDLDF4A_INTRO</to>
		</aggro>
		<hostile>
			<to>PC_DARK</to>
		</hostile>
	</tribe>
	<tribe name="GENERAL_DARK_IDLDF4A_INTRO" base="GENERAL_DARK">
		<aggro>
			<to>GENERAL_IDLDF4A_INTRO</to>
		</aggro>
		<hostile>
			<to>PC</to>
		</hostile>
	</tribe>
	<tribe name="AGRESSIVETOMONSTER" base="GENERAL">
		<aggro>
			<to>MONSTER</to>
		</aggro>
		<friend>
			<to>PC</to>
			<to>GUARD</to>
		</friend>
	</tribe>
	<tribe name="LDF4A_PUBLIC_MONSTER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>GENERAL</to>
			<to>GENERAL_DARK</to>
			<to>GUARD</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>LDF4A_PUBLIC_MONSTER</to>
		</support>
	</tribe>
	<tribe name="GENERAL_LDF4A_PUBLIC_YUN" base="USEALL">
		<aggro>
			<to>MONSTER_LDF4A_PUBLIC_LIZARDMAN</to>
		</aggro>
		<hostile>
			<to>MONSTER</to>
		</hostile>
		<friend>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</friend>
	</tribe>
	<tribe name="MONSTER_LDF4A_PUBLIC_LIZARDMAN" base="MONSTER">
		<aggro>
			<to>GENERAL_LDF4A_PUBLIC_YUN</to>
		</aggro>
		<hostile>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</hostile>
		<friend>
			<to>LDF4A_PUBLIC_MONSTER</to>
		</friend>
	</tribe>
	<tribe name="YDUMMY" base="MONSTER">
	</tribe>
	<tribe name="YDUMMY2" base="MONSTER">
	</tribe>
	<tribe name="YDUMMY_GUARD" base="USEALL">
		<aggro>
			<to>YDUMMY</to>
		</aggro>
		<neutral>
			<to>YDUMMY2</to>
		</neutral>
	</tribe>
	<tribe name="YDUMMY2_GUARD" base="USEALL">
		<aggro>
			<to>YDUMMY2</to>
		</aggro>
		<neutral>
			<to>YDUMMY</to>
		</neutral>
	</tribe>
	<tribe name="YUN_GUARD" base="USEALL">
		<aggro>
			<to>IDYUN_XDRAKAN</to>
		</aggro>
		<hostile>
			<to>MONSTER</to>
		</hostile>
		<friend>
			<to>YDUMMY</to>
			<to>YDUMMY2</to>
		</friend>
		<support>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>YUN_GUARD</to>
			<to>NEUTRAL_LGUARD</to>
			<to>NEUTRAL_DGUARD</to>
		</support>
	</tribe>
	<tribe name="LDF4B_WAGON" base="USEALL">
	</tribe>
	<tribe name="LDF4B_SPARRING_GUARD" base="GENERAL">
		<aggro>
			<to>LDF4B_SPARRING_Y</to>
		</aggro>
	</tribe>
	<tribe name="LDF4B_SPARRING_Y" base="USEALL">
		<aggro>
			<to>LDF4B_SPARRING_GUARD</to>
			<to>LDF4B_SPARRING_DGUARD</to>
		</aggro>
	</tribe>
	<tribe name="GURURU_D1" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
	</tribe>
	<tribe name="GURURU_DECO" base="MONSTER">
		<aggro>
			<to>GURURU_D1</to>
		</aggro>
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
	</tribe>
	<tribe name="LDF4B_SPARRING_GUARD2" base="GENERAL">
		<aggro>
			<to>LDF4B_SPARRING_Y2</to>
		</aggro>
	</tribe>
	<tribe name="LDF4B_SPARRING_Y2" base="USEALL">
		<aggro>
			<to>LDF4B_SPARRING_GUARD2</to>
			<to>LDF4B_SPARRING_DGUARD2</to>
		</aggro>
	</tribe>
	<tribe name="YDUMMY_LGUARD" base="GENERAL">
		<aggro>
			<to>YDUMMY</to>
		</aggro>
		<neutral>
			<to>YDUMMY2</to>
		</neutral>
	</tribe>
	<tribe name="YDUMMY2_LGUARD" base="GENERAL">
		<aggro>
			<to>YDUMMY2</to>
		</aggro>
		<neutral>
			<to>YDUMMY</to>
		</neutral>
	</tribe>
	<tribe name="YDUMMY_DGUARD" base="GENERAL_DARK">
		<aggro>
			<to>YDUMMY</to>
		</aggro>
		<neutral>
			<to>YDUMMY2</to>
		</neutral>
	</tribe>
	<tribe name="YDUMMY2_DGUARD" base="GENERAL_DARK">
		<aggro>
			<to>YDUMMY2</to>
		</aggro>
		<neutral>
			<to>YDUMMY</to>
		</neutral>
	</tribe>
	<tribe name="LDF4B_SPARRING_DGUARD" base="GENERAL_DARK">
		<aggro>
			<to>LDF4B_SPARRING_Y</to>
		</aggro>
	</tribe>
	<tribe name="LDF4B_SPARRING_DGUARD2" base="GENERAL_DARK">
		<aggro>
			<to>LDF4B_SPARRING_Y2</to>
		</aggro>
	</tribe>
	<tribe name="TDOWN_DRAKAN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>TDOWN_DRAKAN</to>
		</support>
	</tribe>
	<tribe name="NEUTRAL_LGUARD" base="GUARD">
		<aggro>
			<to>IDYUN_XDRAKAN</to>
		</aggro>
		<neutral>
			<to>YDUMMY_LGUARD</to>
			<to>YDUMMY2_LGUARD</to>
			<to>YDUMMY</to>
			<to>YDUMMY2</to>
			<to>GENERAL_DARK</to>
		</neutral>
		<support>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>YUN_GUARD</to>
			<to>NEUTRAL_LGUARD</to>
			<to>NEUTRAL_DGUARD</to>
		</support>
	</tribe>
	<tribe name="NEUTRAL_DGUARD" base="GUARD_DARK">
		<aggro>
			<to>IDYUN_XDRAKAN</to>
		</aggro>
		<neutral>
			<to>YDUMMY_DGUARD</to>
			<to>YDUMMY2_DGUARD</to>
			<to>YDUMMY</to>
			<to>YDUMMY2</to>
			<to>GENERAL</to>
		</neutral>
		<support>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>YUN_GUARD</to>
			<to>NEUTRAL_LGUARD</to>
			<to>NEUTRAL_DGUARD</to>
		</support>
	</tribe>
	<tribe name="LDF4A_NEPILIM_SUMMON" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>LDF4A_NEPILIM</to>
			<to>LDF4A_NEPILIM_SUMMON</to>
		</support>
	</tribe>
	<tribe name="DRAKAN_LGUARD" base="GUARD">
		<neutral>
			<to>XDRAKAN</to>
		</neutral>
	</tribe>
	<tribe name="DRAKAN_DGUARD" base="GUARD_DARK">
		<neutral>
			<to>XDRAKAN</to>
		</neutral>
	</tribe>
	<tribe name="LDF4B_MINE" base="MONSTER">
		<aggro>
			<to>IDYUN_XDRAKAN</to>
		</aggro>
		<friend>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>IDYUN_ANCIENT</to>
		</friend>
	</tribe>
	<tribe name="LDF4A_YUN_GUARD" base="USEALL">
		<hostile>
			<to>MONSTER</to>
		</hostile>
		<friend>
			<to>LDF4A_SANDWARM</to>
			<to>LDF4A_NEPILIM</to>
			<to>LDF4A_OWLLAU</to>
			<to>LDF4A_CALYDON</to>
			<to>LDF4A_PUBLIC_MONSTER</to>
			<to>LDF4A_POLY_SHULACK</to>
		</friend>
		<support>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>LDF4A_YUN_GUARD</to>
		</support>
	</tribe>
	<tribe name="XDRAKAN_LGUARD" base="GUARD">
		<aggro>
			<to>XDRAKAN</to>
		</aggro>
		<support>
			<to>XDRAKAN_LGUARD</to>
			<to>PC</to>
		</support>
	</tribe>
	<tribe name="XDRAKAN_DGUARD" base="GUARD_DARK">
		<aggro>
			<to>XDRAKAN</to>
		</aggro>
		<support>
			<to>XDRAKAN_DGUARD</to>
			<to>PC_DARK</to>
		</support>
	</tribe>
	<tribe name="LDF4A_LG_SKILL" base="MONSTER">
		<aggro>
			<to>LDF4A_LG_SKILL_RECEIVE</to>
		</aggro>
		<friend>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>LDF4A_NEPILIM</to>
		</friend>
	</tribe>
	<tribe name="LDF4A_LG_SKILL_RECEIVE" base="MONSTER">
		<aggro>
			<to>LDF4A_LG_SKILL</to>
		</aggro>
		<friend>
			<to>PC</to>
			<to>PC_DARK</to>
		</friend>
	</tribe>
	<tribe name="LDF4A_POLY_SHULACK" base="MONSTER">
		<friend>
			<to>SHULACK</to>
			<to>LDF4A_YUN_GUARD</to>
		</friend>
	</tribe>
	<tribe name="TESTBATTLE_NPC" base="PC">
		<aggro>
			<to>MONSTER</to>
		</aggro>
		<friend>
			<to>USEALL</to>
		</friend>
		<support>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>TESTBATTLE_NPC</to>
		</support>
	</tribe>
	<tribe name="TIAMATREMNANT_DRAKAN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<friend>
			<to>TIAMATREMNANT_LIZARD</to>
		</friend>
		<support>
			<to>VRITRA</to>
			<to>TIAMATREMNANT_DRAKAN</to>
			<to>TIAMATREMNANT_LIZARD_INJURY</to>
		</support>
	</tribe>
	<tribe name="TIAMATREMNANT_LIZARD" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<support>
			<to>VRITRA</to>
			<to>TIAMATREMNANT_DRAKAN</to>
			<to>TIAMATREMNANT_LIZARD</to>
			<to>TIAMATREMNANT_LIZARD_INJURY</to>
		</support>
	</tribe>
	<tribe name="TIAMATREMNANT_LIZARD_INJURY" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</hostile>
		<friend>
			<to>VRITRA</to>
			<to>TIAMATREMNANT_DRAKAN</to>
			<to>TIAMATREMNANT_LIZARD</to>
			<to>TIAMATREMNANT_LIZARD_INJURY</to>
		</friend>
	</tribe>
	<tribe name="NEUTRAL_GUARD" base="USEALL">
		<neutral>
			<to>PC</to>
			<to>PC_DARK</to>
		</neutral>
		<support>
			<to>NEUTRAL_GUARD</to>
			<to>NEUTRAL_GUARD_ON_ATTACK</to>
			<to>NEUTRAL_GUARD_ON_ATTACK01</to>
		</support>
	</tribe>
	<tribe name="VRITRA" base="GUARD_DRAGON">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>ESCORT</to>
			<to>IDLDF5RE_SOLO_Q</to>
		</aggro>
		<friend>
			<to>VRITRA</to>
			<to>VRITRASUPPORT</to>
			<to>NONAGRRESSIVEFRIENDLYVRITRA</to>
			<to>AGRRESSIVEFRIENDLYVRITRA</to>
			<to>AGRRESSIVEFRIENDLYVRITRA2</to>
			<to>IDVRITRA_BASE_REBIRTH</to>
			<to>IDF5_TD_ASSULT</to>
			<to>IDF5_TD_SIEGE</to>
			<to>IDRUNEWP_VRITRA</to>
			<to>IDRUNEWP_ANCIENTARM</to>
			<to>TIAMATREMNANT_LIZARD</to>
			<to>TIAMATREMNANT_LIZARD_INJURY</to>
		</friend>
		<neutral>
			<to>XDRAKAN</to>
		</neutral>
	</tribe>
	<tribe name="DF5_VRITRA" base="VRITRA">
	</tribe>
	<tribe name="LF5_ITEM" base="VRITRA">
	</tribe>
	<tribe name="IDEVENT01_SUMMON" base="MONSTER">
		<aggro>
			<to>IDEVENT01_TOWER</to>
		</aggro>
		<friend>
			<to>PC</to>
			<to>PC_DARK</to>
		</friend>
		<support>
			<to>IDEVENT01_SUMMON</to>
		</support>
	</tribe>
	<tribe name="LIGHT_SUR_MOB_DF2ADIRECTPORTAL" base="MONSTER">
		<aggro>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<hostile>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
		</hostile>
		<support>
			<to>LIGHT_NPC</to>
			<to>LIGHT_SUR_MOB</to>
			<to>LIGHT_SUR_MOB_DF2ADIRECTPORTAL</to>
			<to>LIGHT_LICH_DF2ADIRECTPORTAL</to>
		</support>
	</tribe>
	<tribe name="LIGHT_LICH_DF2ADIRECTPORTAL" base="MONSTER">
		<aggro>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<hostile>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
		</hostile>
		<support>
			<to>LIGHT_NPC</to>
			<to>LIGHT_SUR_MOB</to>
			<to>LIGHT_SUR_MOB_DF2ADIRECTPORTAL</to>
			<to>LIGHT_LICH_DF2ADIRECTPORTAL</to>
		</support>
	</tribe>
	<tribe name="LDF5_CALYDON" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>LDF5_CALYDON</to>
		</support>
	</tribe>
	<tribe name="LDF5_OWLLAU" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>LDF5_OWLLAU</to>
		</support>
	</tribe>
	<tribe name="LDF5_GURURU" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>LDF5_GURURU</to>
		</support>
	</tribe>
	<tribe name="ELEMENTAL_LIGHT" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>ELEMENTAL_LIGHT</to>
		</support>
	</tribe>
	<tribe name="NEUTRAL_GUARD_ON_ATTACK" base="GUARD_DRAGON">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
		<support>
			<to>NEUTRAL_GUARD</to>
			<to>NEUTRAL_GUARD_ON_ATTACK</to>
			<to>NEUTRAL_GUARD_ON_ATTACK01</to>
		</support>
	</tribe>
	<tribe name="NEUTRAL_GUARD_ON_ATTACK01" base="GUARD_DRAGON">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
		<support>
			<to>NEUTRAL_GUARD</to>
			<to>NEUTRAL_GUARD_ON_ATTACK</to>
			<to>NEUTRAL_GUARD_ON_ATTACK01</to>
		</support>
	</tribe>
	<tribe name="XDRAKAN_UNATTACK" base="MONSTER">
		<friend>
			<to>XDRAKAN</to>
		</friend>
	</tribe>
	<tribe name="GOD_KAISINEL" base="GENERAL">
		<hostile>
			<to>TIAMAT</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</hostile>
	</tribe>
	<tribe name="GOD_MARCHUTAN" base="GENERAL_DARK">
		<hostile>
			<to>TIAMAT</to>
			<to>PC</to>
			<to>GUARD</to>
		</hostile>
	</tribe>
	<tribe name="TIAMAT" base="MONSTER">
		<aggro>
			<to>GOD_KAISINEL</to>
			<to>GOD_MARCHUTAN</to>
		</aggro>
		<hostile>
			<to>KAHRUN</to>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
	</tribe>
	<tribe name="LDF5_VESPA" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
		<support>
			<to>LDF5_VESPA</to>
		</support>
	</tribe>
	<tribe name="LDF5_SPAKLE" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
		<support>
			<to>LDF5_SPAKLE</to>
		</support>
	</tribe>
	<tribe name="LDF5_LUPYLLINI" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
		<support>
			<to>LDF5_LUPYLLINI</to>
		</support>
	</tribe>
	<tribe name="LDF5_BRAX" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<friend>
			<to>LDF5_BRAX</to>
		</friend>
		<support>
			<to>BRAX</to>
		</support>
	</tribe>
	<tribe name="LDF5_SHULACK_KEEPER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>SHULACK</to>
			<to>LDF5_SHULACK_KEEPER</to>
			<to>LDF5_WORKER</to>
		</support>
	</tribe>
	<tribe name="LDF5_WORKER" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
		<friend>
			<to>LDF5_WORKER</to>
		</friend>
		<support>
			<to>SHULACK</to>
			<to>LDF5_SHULACK_KEEPER</to>
		</support>
	</tribe>
	<tribe name="KAHRUN" base="USEALL">
		<hostile>
			<to>TIAMAT</to>
			<to>XDRAKAN</to>
		</hostile>
		<friend>
			<to>PC</to>
			<to>PC_DARK</to>
		</friend>
	</tribe>
	<tribe name="IDTIAMAT_XDRAKAN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>IDTIAMAT_ANCIENT</to>
		</aggro>
		<support>
			<to>IDTIAMAT_XDRAKAN</to>
		</support>
	</tribe>
	<tribe name="IDTIAMAT_ANCIENT" base="USEALL">
		<aggro>
			<to>IDTIAMAT_XDRAKAN</to>
		</aggro>
	</tribe>
	<tribe name="LDF5_DARU" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<friend>
			<to>LDF5_DARU</to>
		</friend>
		<support>
			<to>DARU</to>
		</support>
	</tribe>
	<tribe name="IDEVENT01_TOWER" base="USEALL">
		<aggro>
			<to>IDEVENT01_SUMMON</to>
		</aggro>
		<friend>
			<to>PC</to>
			<to>PC_DARK</to>
		</friend>
	</tribe>
	<tribe name="IDTIAMAT_SPAWNHEAL" base="MONSTER">
		<aggro>
			<to>XDRAKAN</to>
		</aggro>
		<friend>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>TIAMAT</to>
			<to>GOD_KAISINEL</to>
			<to>GOD_MARCHUTAN</to>
		</friend>
	</tribe>
	<tribe name="IDTIAMAT_AREAHIDE" base="MONSTER">
		<hostile>
			<to>XDRAKAN</to>
		</hostile>
	</tribe>
	<tribe name="IDEVENT01_POLYMORPHL" base="PC">
		<hostile>
			<to>IDEVENT01_SUMMON</to>
		</hostile>
		<friend>
			<to>PC_DARK</to>
			<to>IDEVENT01_MC</to>
		</friend>
	</tribe>
	<tribe name="IDEVENT01_POLYMORPHD" base="PC_DARK">
		<hostile>
			<to>IDEVENT01_SUMMON</to>
		</hostile>
		<friend>
			<to>PC</to>
			<to>IDEVENT01_MC</to>
		</friend>
	</tribe>
	<tribe name="IDF5_SIEGEWEAPON" base="PC">
		<friend>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</friend>
	</tribe>
	<tribe name="IDF5_SIEGEWEAPON_ATTACK" base="MONSTER">
		<aggro>
			<to>IDF5_SIEGEWEAPON</to>
		</aggro>
	</tribe>
	<tribe name="IDEVENT01_MC" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
		<friend>
			<to>IDEVENT01_POLYMORPHL</to>
			<to>IDEVENT01_POLYMORPHD</to>
			<to>DRAGON</to>
		</friend>
	</tribe>
	<tribe name="IDF5_R2_SYNC1" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
	</tribe>
	<tribe name="IDF5_R2_SYNC1_ATTACK" base="MONSTER">
		<aggro>
			<to>IDF5_R2_SYNC1</to>
		</aggro>
	</tribe>
	<tribe name="IDF5_R2_SYNC2" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
	</tribe>
	<tribe name="IDF5_R2_SYNC2_ATTACK" base="MONSTER">
		<aggro>
			<to>IDF5_R2_SYNC2</to>
		</aggro>
	</tribe>
	<tribe name="IDF5_R2_SYNC3" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
	</tribe>
	<tribe name="IDF5_R2_SYNC3_ATTACK" base="MONSTER">
		<aggro>
			<to>IDF5_R2_SYNC3</to>
		</aggro>
	</tribe>
	<tribe name="LDF5_FUNGY" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
		<support>
			<to>LDF5_FUNGY</to>
		</support>
	</tribe>
	<tribe name="LDF5_V_CHIEF_L" base="GUARD">
		<aggro>
			<to>LDF5_V_KILLER_D</to>
			<to>LDF5_V_KILLER_DR</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>PC_DRAGON</to>
			<to>GUARD_DRAGON</to>
			<to>GENERAL_DRAGON</to>
		</aggro>
		<friend>
			<to>PC</to>
			<to>GUARD</to>
			<to>GENERAL</to>
		</friend>
	</tribe>
	<tribe name="LDF5_V_CHIEF_D" base="GUARD_DARK">
		<aggro>
			<to>LDF5_V_KILLER_L</to>
			<to>LDF5_V_KILLER_DR</to>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DRAGON</to>
			<to>GUARD_DRAGON</to>
			<to>GENERAL_DRAGON</to>
		</aggro>
		<friend>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>GENERAL_DARK</to>
		</friend>
	</tribe>
	<tribe name="LDF5_V_CHIEF_DR" base="GUARD_DRAGON">
		<aggro>
			<to>LDF5_V_KILLER_L</to>
			<to>LDF5_V_KILLER_D</to>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>GUARD</to>
			<to>GUARD_DARK</to>
			<to>GENERAL</to>
			<to>GENERAL_DARK</to>
		</aggro>
		<friend>
			<to>PC_DRAGON</to>
			<to>GUARD_DRAGON</to>
			<to>GENERAL_DRAGON</to>
		</friend>
	</tribe>
	<tribe name="LDF5_V_KILLER_L" base="GUARD">
		<aggro>
			<to>LDF5_V_CHIEF_D</to>
			<to>LDF5_V_CHIEF_DR</to>
		</aggro>
	</tribe>
	<tribe name="LDF5_V_KILLER_D" base="GUARD_DARK">
		<aggro>
			<to>LDF5_V_CHIEF_L</to>
			<to>LDF5_V_CHIEF_DR</to>
		</aggro>
	</tribe>
	<tribe name="LDF5_V_KILLER_DR" base="GUARD_DRAGON">
		<aggro>
			<to>LDF5_V_CHIEF_L</to>
			<to>LDF5_V_CHIEF_D</to>
		</aggro>
	</tribe>
	<tribe name="LDF5_BABARIAN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>LDF5_BABARIAN</to>
		</support>
	</tribe>
	<tribe name="IDF5_TD_WEAPON_PC" base="PC">
		<hostile>
			<to>MONSTER</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
			<to>IDF5_TD_WEAPON_PC_DARK</to>
		</hostile>
		<friend>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>IDF5_TD_WEAPON_PC</to>
		</friend>
	</tribe>
	<tribe name="IDF5_TD_DOOR" base="USEALL">
		<hostile>
			<to>MONSTER</to>
		</hostile>
		<friend>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</friend>
	</tribe>
	<tribe name="IDF5_TD_ASSULT" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
			<to>IDF5_TD_WEAPON_PC</to>
			<to>IDF5_TD_WEAPON_PC_DARK</to>
		</aggro>
		<friend>
			<to>VRITRA</to>
			<to>IDF5_TD_SIEGE</to>
			<to>VRITRASUPPORT</to>
		</friend>
		<support>
			<to>IDF5_TD_ASSULT</to>
		</support>
	</tribe>
	<tribe name="IDF5_TD_SIEGE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
			<to>IDF5_TD_DOOR</to>
		</aggro>
		<friend>
			<to>VRITRA</to>
			<to>IDF5_TD_SIEGE</to>
			<to>IDF5_TD_ASSULT</to>
			<to>VRITRASUPPORT</to>
		</friend>
	</tribe>
	<tribe name="TEST_ATTACKTOPC" base="GUARD_DARK">
		<aggro>
			<to>PC</to>
		</aggro>
		<support>
			<to>PC_DARK</to>
		</support>
	</tribe>
	<tribe name="TEST_ATTACKTOPC_DARK" base="GUARD">
		<aggro>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>PC</to>
		</support>
	</tribe>
	<tribe name="TEST_ATTACKTONPC" base="MONSTER">
		<aggro>
			<to>TEST_ATTACKTOPC</to>
			<to>TEST_ATTACKTOPC_DARK</to>
		</aggro>
	</tribe>
	<tribe name="TEST_SUPPORTNPC" base="USEALL">
		<hostile>
			<to>TEST_DRAKAN</to>
		</hostile>
		<support>
			<to>PC</to>
			<to>PC_DARK</to>
		</support>
	</tribe>
	<tribe name="TEST_DRAKAN" base="MONSTER">
		<aggro>
			<to>TEST_SUPPORTNPC</to>
		</aggro>
		<support>
			<to>XDRAKAN</to>
		</support>
	</tribe>
	<tribe name="LDF_V_CHIEF_L" base="GUARD">
		<aggro>
			<to>LDF_V_KILLER_LEHPAR</to>
			<to>LDF_V_KILLER_KRALL</to>
			<to>LDF_V_KILLER_LYCAN</to>
		</aggro>
	</tribe>
	<tribe name="LDF_V_CHIEF_D" base="GUARD_DARK">
		<aggro>
			<to>LDF_V_KILLER_LEHPAR</to>
			<to>LDF_V_KILLER_KRALL</to>
			<to>LDF_V_KILLER_LYCAN</to>
		</aggro>
	</tribe>
	<tribe name="LDF_V_KILLER_LEHPAR" base="MONSTER">
		<aggro>
			<to>LDF_V_CHIEF_L</to>
			<to>LDF_V_CHIEF_D</to>
		</aggro>
	</tribe>
	<tribe name="LDF_V_KILLER_KRALL" base="MONSTER">
		<aggro>
			<to>LDF_V_CHIEF_L</to>
			<to>LDF_V_CHIEF_D</to>
		</aggro>
	</tribe>
	<tribe name="LDF_V_KILLER_LYCAN" base="MONSTER">
		<aggro>
			<to>LDF_V_CHIEF_L</to>
			<to>LDF_V_CHIEF_D</to>
		</aggro>
	</tribe>
	<tribe name="LDF_V_GUARD_LIGHT" base="GUARD">
		<aggro>
			<to>LYCAN</to>
			<to>LEHPAR</to>
			<to>KRALL</to>
		</aggro>
		<friend>
			<to>MONSTER</to>
		</friend>
	</tribe>
	<tribe name="LDF_V_GUARD_DARK" base="GUARD_DARK">
		<aggro>
			<to>LYCAN</to>
			<to>LEHPAR</to>
			<to>KRALL</to>
		</aggro>
		<friend>
			<to>MONSTER</to>
		</friend>
	</tribe>
	<tribe name="IDF5_TD_COMMANDER_LI" base="GUARD">
		<aggro>
			<to>IDF5_TD_ASSULT</to>
			<to>IDF5_TD_SIEGE</to>
			<to>VRITRASUPPORT</to>
		</aggro>
		<hostile>
			<to>MONSTER</to>
		</hostile>
	</tribe>
	<tribe name="IDF5_TD_COMMANDER_DA" base="GUARD_DARK">
		<aggro>
			<to>IDF5_TD_ASSULT</to>
			<to>IDF5_TD_SIEGE</to>
			<to>VRITRASUPPORT</to>
		</aggro>
		<hostile>
			<to>MONSTER</to>
		</hostile>
	</tribe>
	<tribe name="IDLDF5_UNDER_RUNE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>ESCORT</to>
		</aggro>
		<support>
			<to>IDLDF5_UNDER_RUNE</to>
		</support>
	</tribe>
	<tribe name="LDF5B_KILLER" base="USEALL">
		<aggro>
			<to>LDF5B_DOOR_LI</to>
			<to>LDF5B_DOOR_DA</to>
			<to>LDF5B_DOOR_DR</to>
		</aggro>
	</tribe>
	<tribe name="LDF5B_DOOR_LI" base="GENERAL">
		<hostile>
			<to>LDF5B_KILLER</to>
		</hostile>
	</tribe>
	<tribe name="LDF5B_DOOR_DA" base="GENERAL_DARK">
		<hostile>
			<to>LDF5B_KILLER</to>
		</hostile>
	</tribe>
	<tribe name="LDF5B_DOOR_DR" base="GENERAL_DRAGON">
		<hostile>
			<to>LDF5B_KILLER</to>
		</hostile>
	</tribe>
	<tribe name="FIELD_OBJECT_ALL_HOSTILEMONSTER" base="FIELD_OBJECT_LIGHT">
		<hostile>
			<to>MONSTER</to>
		</hostile>
		<neutral>
			<to>PC_DARK</to>
		</neutral>
	</tribe>
	<tribe name="LDF5B_FOBJ_HOSTILEPC" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
		<neutral>
			<to>GENERAL</to>
			<to>GENERAL_DARK</to>
			<to>GENERAL_DRAGON</to>
		</neutral>
	</tribe>
	<tribe name="IDF5_TD_GUARD_LIGHT" base="GUARD">
		<aggro>
			<to>IDF5_TD_ASSULT</to>
			<to>IDF5_TD_SIEGE</to>
			<to>VRITRA</to>
			<to>VRITRASUPPORT</to>
		</aggro>
		<hostile>
			<to>MONSTER</to>
		</hostile>
		<friend>
			<to>PC</to>
			<to>GENERAL</to>
		</friend>
		<support>
			<to>GUARD</to>
			<to>IDF5_TD_GUARD_LIGHT</to>
		</support>
	</tribe>
	<tribe name="IDF5_TD_GUARD_DARK" base="GUARD_DARK">
		<aggro>
			<to>IDF5_TD_ASSULT</to>
			<to>IDF5_TD_SIEGE</to>
			<to>VRITRA</to>
			<to>VRITRASUPPORT</to>
		</aggro>
		<hostile>
			<to>MONSTER</to>
		</hostile>
		<friend>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
		</friend>
		<support>
			<to>GUARD_DARK</to>
			<to>IDF5_TD_GUARD_DARK</to>
		</support>
	</tribe>
	<tribe name="IDRUNEWP_VRITRA" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>ESCORT</to>
		</aggro>
		<neutral>
			<to>XDRAKAN</to>
		</neutral>
		<support>
			<to>VRITRA</to>
			<to>IDRUNEWP_ESCORT</to>
			<to>IDRUNEWP_VRITRA</to>
		</support>
	</tribe>
	<tribe name="IDRUNEWP_ESCORT" base="MONSTER">
		<neutral>
			<to>VRITRA</to>
			<to>IDRUNEWP_VRITRA</to>
		</neutral>
	</tribe>
	<tribe name="IDRUNEWP_ANCIENTARM" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>ESCORT</to>
		</aggro>
		<neutral>
			<to>IDRUNEWP_ESCORT</to>
		</neutral>
	</tribe>
	<tribe name="LDF5_DEBRIE" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
		<support>
			<to>LDF5_DEBRIE</to>
		</support>
	</tribe>
	<tribe name="LDF5_NEUT" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
		<support>
			<to>LDF5_NEUT</to>
			<to>NEUT</to>
			<to>NEUTQUEEN</to>
		</support>
	</tribe>
	<tribe name="IDLDF5RE_SOLO_Q" base="USEALL">
		<aggro>
			<to>VRITRA</to>
		</aggro>
	</tribe>
	<tribe name="LDF5_SHULACK_DIRECT" base="MONSTER">
		<aggro>
			<to>LDF5_NATIVE_DIRECT</to>
		</aggro>
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
	</tribe>
	<tribe name="LDF5_NATIVE_DIRECT" base="MONSTER">
		<aggro>
			<to>LDF5_SHULACK_DIRECT</to>
		</aggro>
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
	</tribe>
	<tribe name="SHULACK_SLAVE_NOTAGGRESSIVE" base="MONSTER">
		<support>
			<to>SHULACK</to>
		</support>
	</tribe>
	<tribe name="LDF5B_OUT_DOOR_KILLER_LI" base="GENERAL">
		<aggro>
			<to>LDF5B_DOOR_DA</to>
			<to>LDF5B_DOOR_DR</to>
		</aggro>
	</tribe>
	<tribe name="LDF5B_OUT_DOOR_KILLER_DA" base="GENERAL_DARK">
		<aggro>
			<to>LDF5B_DOOR_LI</to>
			<to>LDF5B_DOOR_DR</to>
		</aggro>
	</tribe>
	<tribe name="LDF5_MUTA" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
		</hostile>
		<support>
			<to>LDF5_MUTA</to>
		</support>
	</tribe>
	<tribe name="USEALL_LDF5_TOWER_LI" base="GENERAL">
		<aggro>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>GENERAL_DARK</to>
		</aggro>
	</tribe>
	<tribe name="USEALL_LDF5_TOWER_DA" base="GENERAL_DARK">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>GENERAL</to>
		</aggro>
	</tribe>
	<tribe name="MONSTER_FRIENDLY_LDFCHIEF" base="MONSTER">
		<neutral>
			<to>LDF_V_CHIEF_L</to>
			<to>LDF_V_CHIEF_D</to>
		</neutral>
	</tribe>
	<tribe name="USEALL_TELEPORTER_LI" base="GENERAL">
	</tribe>
	<tribe name="USEALL_TELEPORTER_DA" base="GENERAL_DARK">
	</tribe>
	<tribe name="IDASTERIA_IU_MONSTER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>IDASTERIA_IU_POLYMORPHL</to>
			<to>IDASTERIA_IU_POLYMORPHD</to>
		</aggro>
		<support>
			<to>IDASTERIA_IU_MONSTER</to>
			<to>IDASTERIA_IU_MONSTER2</to>
		</support>
	</tribe>
	<tribe name="IDASTERIA_IU_NPC" base="USEALL">
		<hostile>
			<to>IDASTERIA_IU_MONSTER</to>
			<to>IDASTERIA_IU_MONSTER2</to>
		</hostile>
		<friend>
			<to>IDASTERIA_IU_POLYMORPHL</to>
			<to>IDASTERIA_IU_POLYMORPHD</to>
			<to>PC</to>
			<to>PC_DARK</to>
		</friend>
	</tribe>
	<tribe name="IDASTERIA_IU_POLYMORPHL" base="PC">
		<hostile>
			<to>IDASTERIA_IU_MONSTER</to>
			<to>IDASTERIA_IU_MONSTER2</to>
		</hostile>
		<friend>
			<to>PC_DARK</to>
			<to>IDASTERIA_IU_ATK</to>
		</friend>
	</tribe>
	<tribe name="IDASTERIA_IU_POLYMORPHD" base="PC_DARK">
		<hostile>
			<to>IDASTERIA_IU_MONSTER</to>
			<to>IDASTERIA_IU_MONSTER2</to>
		</hostile>
		<friend>
			<to>PC</to>
			<to>IDASTERIA_IU_ATK</to>
		</friend>
	</tribe>
	<tribe name="IDF5_R2_CANNON" base="USEALL">
		<hostile>
			<to>IDF5_R2_CANNON_ATTACK</to>
		</hostile>
		<friend>
			<to>MONSTER</to>
		</friend>
	</tribe>
	<tribe name="IDF5_R2_CANNON_ATTACK" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<friend>
			<to>IDF5_R2_CANNON</to>
		</friend>
	</tribe>
	<tribe name="IDASTERIA_IU_ATK" base="MONSTER">
		<friend>
			<to>IDASTERIA_IU_POLYMORPHL</to>
			<to>IDASTERIA_IU_POLYMORPHD</to>
			<to>PC</to>
			<to>PC_DARK</to>
		</friend>
	</tribe>
	<tribe name="USEALL_HOSTILEPC" base="USEALL">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>PET</to>
			<to>PET_DARK</to>
		</hostile>
	</tribe>
	<tribe name="IDRUNEWP_RUNEDEVICE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>ESCORT</to>
		</aggro>
		<support>
			<to>IDRUNEWP_ANCIENTARM</to>
		</support>
	</tribe>
	<tribe name="IDASTERIA_IU_MONSTER2" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>IDASTERIA_IU_POLYMORPHL</to>
			<to>IDASTERIA_IU_POLYMORPHD</to>
		</hostile>
		<support>
			<to>IDASTERIA_IU_MONSTER</to>
			<to>IDASTERIA_IU_MONSTER2</to>
		</support>
	</tribe>
	<tribe name="NONAGRRESSIVEFRIENDLYVRITRA" base="MONSTER">
		<hostile>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>GENERAL_DARK</to>
		</hostile>
		<friend>
			<to>VRITRA</to>
			<to>VRITRASUPPORT</to>
			<to>IDVRITRA_BASE_REBIRTH</to>
			<to>IDF5_TD_ASSULT</to>
			<to>IDF5_TD_SIEGE</to>
			<to>AGRRESSIVEFRIENDLYVRITRA</to>
			<to>AGRRESSIVEFRIENDLYVRITRA2</to>
			<to>VRITRATANK</to>
		</friend>
	</tribe>
	<tribe name="IDVRITRA_BASE_REBIRTH" base="MONSTER">
		<aggro>
			<to>VRITRA</to>
			<to>VRITRASUPPORT</to>
		</aggro>
	</tribe>
	<tribe name="IDKAMAR_VRITRA" base="GUARD_DRAGON">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<hostile>
			<to>IDKAMAR_SIEGEWEAPON_LIGHT</to>
			<to>IDKAMAR_SIEGEWEAPON_DARK</to>
		</hostile>
		<friend>
			<to>IDKAMAR_PROTECTGUARD_LIGHT</to>
			<to>IDKAMAR_PROTECTGUARD_DARK</to>
		</friend>
		<support>
			<to>IDKAMAR_VRITRA</to>
		</support>
	</tribe>
	<tribe name="IDRUNEWP_VRITRADEVICE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>ESCORT</to>
		</aggro>
		<neutral>
			<to>XDRAKAN</to>
		</neutral>
		<support>
			<to>VRITRA</to>
			<to>IDRUNEWP_ESCORT</to>
			<to>IDRUNEWP_VRITRA</to>
		</support>
	</tribe>
	<tribe name="IDKAMAR_SIEGEWEAPON_LIGHT" base="GUARD">
		<aggro>
			<to>IDKAMAR_CANNON</to>
		</aggro>
		<hostile>
			<to>IDKAMAR_VRITRA</to>
		</hostile>
	</tribe>
	<tribe name="IDKAMAR_SIEGEWEAPON_DARK" base="GUARD_DARK">
		<aggro>
			<to>IDKAMAR_CANNON</to>
		</aggro>
		<hostile>
			<to>IDKAMAR_VRITRA</to>
		</hostile>
	</tribe>
	<tribe name="IDF5_TD_WEAPON_PC_DARK" base="PC_DARK">
		<hostile>
			<to>MONSTER</to>
			<to>PC</to>
			<to>GENERAL</to>
			<to>GUARD</to>
			<to>IDF5_TD_WEAPON_PC</to>
		</hostile>
		<friend>
			<to>PC_DARK</to>
			<to>GENERAL_DARK</to>
			<to>GUARD_DARK</to>
			<to>IDF5_TD_WEAPON_PC_DARK</to>
		</friend>
	</tribe>
	<tribe name="IDRUNEWP_AGGRESSIVEANCIENTARM" base="USEALL">
		<aggro>
			<to>IDRUNEWP_ANCIENTARM</to>
		</aggro>
	</tribe>
	<tribe name="VRITRASUPPORT" base="GUARD_DRAGON">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<friend>
			<to>VRITRA</to>
			<to>NONAGRRESSIVEFRIENDLYVRITRA</to>
			<to>IDVRITRA_BASE_REBIRTH</to>
			<to>IDF5_TD_ASSULT</to>
			<to>IDF5_TD_SIEGE</to>
			<to>AGRRESSIVEFRIENDLYVRITRA</to>
			<to>AGRRESSIVEFRIENDLYVRITRA2</to>
			<to>VRITRATANK</to>
		</friend>
		<neutral>
			<to>XDRAKAN</to>
		</neutral>
		<support>
			<to>VRITRASUPPORT</to>
		</support>
	</tribe>
	<tribe name="AGRRESSIVEFRIENDLYVRITRA" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<friend>
			<to>VRITRA</to>
			<to>NONAGRRESSIVEFRIENDLYVRITRA</to>
			<to>IDVRITRA_BASE_REBIRTH</to>
			<to>IDF5_TD_ASSULT</to>
			<to>IDF5_TD_SIEGE</to>
			<to>VRITRASUPPORT</to>
			<to>AGRRESSIVEFRIENDLYVRITRA2</to>
			<to>VRITRATANK</to>
		</friend>
		<neutral>
			<to>XDRAKAN</to>
		</neutral>
		<support>
			<to>AGRRESSIVEFRIENDLYVRITRA</to>
		</support>
	</tribe>
	<tribe name="AGRRESSIVEFRIENDLYVRITRA2" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<friend>
			<to>VRITRA</to>
			<to>NONAGRRESSIVEFRIENDLYVRITRA</to>
			<to>IDVRITRA_BASE_REBIRTH</to>
			<to>IDF5_TD_ASSULT</to>
			<to>IDF5_TD_SIEGE</to>
			<to>AGRRESSIVEFRIENDLYVRITRA</to>
			<to>VRITRASUPPORT</to>
			<to>AGRRESSIVEFRIENDLYVRITRA</to>
			<to>VRITRATANK</to>
		</friend>
		<neutral>
			<to>XDRAKAN</to>
		</neutral>
		<support>
			<to>AGRRESSIVEFRIENDLYVRITRA2</to>
		</support>
	</tribe>
	<tribe name="VRITRATANK" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
		</aggro>
		<friend>
			<to>VRITRA</to>
			<to>NONAGRRESSIVEFRIENDLYVRITRA</to>
			<to>IDVRITRA_BASE_REBIRTH</to>
			<to>IDF5_TD_ASSULT</to>
			<to>IDF5_TD_SIEGE</to>
			<to>AGRRESSIVEFRIENDLYVRITRA</to>
			<to>AGRRESSIVEFRIENDLYVRITRA2</to>
		</friend>
		<neutral>
			<to>XDRAKAN</to>
		</neutral>
		<support>
			<to>VRITRASUPPORT</to>
		</support>
	</tribe>
	<tribe name="AGRRESSIVEVRITRAANDPC" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>VRITRA</to>
			<to>NONAGRRESSIVEFRIENDLYVRITRA</to>
			<to>IDVRITRA_BASE_REBIRTH</to>
			<to>IDF5_TD_ASSULT</to>
			<to>IDF5_TD_SIEGE</to>
			<to>AGRRESSIVEFRIENDLYVRITRA</to>
			<to>AGRRESSIVEFRIENDLYVRITRA2</to>
			<to>VRITRASUPPORT</to>
		</aggro>
	</tribe>
	<tribe name="TIGRAN" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<support>
			<to>TIGRAN</to>
		</support>
	</tribe>
	<tribe name="IDF5U1_VRITRA" base="GUARD_DRAGON">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>ESCORT</to>
			<to>IDF5U1_TANK</to>
			<to>IDF5U1_PCFLAG</to>
		</aggro>
		<neutral>
			<to>XDRAKAN</to>
		</neutral>
		<support>
			<to>IDF5U1_VRITRA</to>
			<to>IDF5U1_VRITRAFLAG</to>
			<to>IDF5U1_VRITRAWEAPON</to>
		</support>
	</tribe>
	<tribe name="IDF5U1_VRITRAWEAPON" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>ESCORT</to>
			<to>IDF5U1_TANK</to>
			<to>IDF5U1_PCFLAG</to>
		</hostile>
		<friend>
			<to>IDF5U1_VRITRA</to>
		</friend>
		<neutral>
			<to>XDRAKAN</to>
		</neutral>
	</tribe>
	<tribe name="IDF5U1_VRITRATRAP" base="GUARD_DRAGON">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>ESCORT</to>
			<to>IDF5U1_TANK</to>
			<to>IDF5U1_PCFLAG</to>
		</aggro>
		<friend>
			<to>IDF5U1_VRITRA</to>
		</friend>
		<neutral>
			<to>XDRAKAN</to>
		</neutral>
		<support>
			<to>IDF5U1_VRITRAFLAG</to>
			<to>IDF5U1_VRITRAWEAPON</to>
		</support>
	</tribe>
	<tribe name="IDF5U1_TANK" base="USEALL">
		<hostile>
			<to>IDF5U1_VRITRAWEAPON</to>
		</hostile>
		<neutral>
			<to>IDF5U1_AGGRESSIVETANK</to>
			<to>IDF5U1_VRITRAFLAG</to>
			<to>IDF5U1_VRITRA</to>
			<to>IDF5U1_VRITRATRAP</to>
			<to>MONSTER</to>
		</neutral>
	</tribe>
	<tribe name="IDF5U1_AGGRESSIVETANK" base="MONSTER">
		<aggro>
			<to>IDF5U1_TANK</to>
		</aggro>
	</tribe>
	<tribe name="IDKAMAR_SIEGEWEAPON_ATTACK" base="MONSTER">
		<aggro>
			<to>PROTECTGUARD_LIGHT_SIEGEWEAPON</to>
			<to>PROTECTGUARD_DARK_SIEGEWEAPON</to>
		</aggro>
	</tribe>
	<tribe name="PROTECTGUARD_LIGHT_SIEGEWEAPON" base="GUARD">
		<aggro>
			<to>IDKAMAR_CANNON</to>
		</aggro>
		<friend>
			<to>MONSTER</to>
		</friend>
	</tribe>
	<tribe name="PROTECTGUARD_DARK_SIEGEWEAPON" base="GUARD_DARK">
		<aggro>
			<to>IDKAMAR_CANNON</to>
		</aggro>
		<friend>
			<to>MONSTER</to>
		</friend>
	</tribe>
	<tribe name="IDF5_TD_BOMBER" base="MONSTER">
		<aggro>
			<to>IDF5_TD_DOOR</to>
		</aggro>
		<friend>
			<to>VRITRA</to>
			<to>IDF5_TD_SIEGE</to>
			<to>IDF5_TD_ASSULT</to>
			<to>VRITRASUPPORT</to>
			<to>PC</to>
			<to>PC_DARK</to>
		</friend>
	</tribe>
	<tribe name="IDF5U2_VRITRA" base="GUARD_DRAGON">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>ESCORT</to>
		</aggro>
		<friend>
			<to>IDF5U2_ARROWTRAP</to>
		</friend>
		<neutral>
			<to>MONSTER</to>
			<to>IDF5U2_GUARD</to>
			<to>IDF5U2_GUARD_DARK</to>
			<to>IDF5U2_SHULACK</to>
			<to>IDF5U2_SHULACK_SLAVE</to>
		</neutral>
		<support>
			<to>IDF5U2_VRITRA</to>
		</support>
	</tribe>
	<tribe name="IDF5U2_VRITRATRAP" base="GUARD_DRAGON">
		<aggro>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>ESCORT</to>
		</aggro>
		<friend>
			<to>IDF5U2_VRITRA</to>
			<to>IDF5U2_ARROWTRAP</to>
		</friend>
		<neutral>
			<to>MONSTER</to>
			<to>IDF5U2_GUARD</to>
			<to>IDF5U2_GUARD_DARK</to>
			<to>IDF5U2_SHULACK</to>
			<to>IDF5U2_SHULACK_SLAVE</to>
		</neutral>
	</tribe>
	<tribe name="IDF5U2_GUARD" base="GUARD">
		<friend>
			<to>IDF5U2_ARROWTRAP</to>
		</friend>
		<neutral>
			<to>MONSTER</to>
			<to>IDF5U2_VRITRA</to>
			<to>IDF5U2_VRITRATRAP</to>
			<to>IDF5U2_SHULACK</to>
			<to>IDF5U2_SHULACK_SLAVE</to>
		</neutral>
		<support>
			<to>IDF5U2_GUARD</to>
		</support>
	</tribe>
	<tribe name="IDF5U2_GUARD_DARK" base="GUARD_DARK">
		<friend>
			<to>IDF5U2_ARROWTRAP</to>
		</friend>
		<neutral>
			<to>MONSTER</to>
			<to>IDF5U2_VRITRA</to>
			<to>IDF5U2_VRITRATRAP</to>
			<to>IDF5U2_SHULACK</to>
			<to>IDF5U2_SHULACK_SLAVE</to>
		</neutral>
		<support>
			<to>IDF5U2_GUARD_DARK</to>
		</support>
	</tribe>
	<tribe name="IDF5U2_SHULACK" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<friend>
			<to>IDF5U2_ARROWTRAP</to>
		</friend>
		<neutral>
			<to>MONSTER</to>
			<to>IDF5U2_GUARD</to>
			<to>IDF5U2_GUARD_DARK</to>
			<to>IDF5U2_VRITRA</to>
			<to>IDF5U2_VRITRATRAP</to>
		</neutral>
		<support>
			<to>IDF5U2_SHULACK</to>
			<to>IDF5U2_SHULACK_ESCORT</to>
		</support>
	</tribe>
	<tribe name="IDF5U2_SHULACK_SLAVE" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
		</aggro>
		<friend>
			<to>IDF5U2_ARROWTRAP</to>
		</friend>
		<neutral>
			<to>MONSTER</to>
			<to>IDF5U2_GUARD</to>
			<to>IDF5U2_GUARD_DARK</to>
			<to>IDF5U2_VRITRA</to>
			<to>IDF5U2_VRITRATRAP</to>
		</neutral>
		<support>
			<to>IDF5U2_SHULACK</to>
			<to>IDF5U2_SHULACK_SLAVE</to>
			<to>IDF5U2_SHULACK_ESCORT</to>
		</support>
	</tribe>
	<tribe name="IDF5U2_SHULACK_ESCORT" base="MONSTER">
		<friend>
			<to>IDF5U2_ARROWTRAP</to>
		</friend>
	</tribe>
	<tribe name="IDF5U1_VRITRAFLAG" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>GUARD</to>
			<to>PC_DARK</to>
			<to>GUARD_DARK</to>
			<to>ESCORT</to>
			<to>IDF5U1_TANK</to>
			<to>IDF5U1_PCFLAG</to>
		</hostile>
		<friend>
			<to>IDF5U1_VRITRA</to>
		</friend>
		<neutral>
			<to>XDRAKAN</to>
		</neutral>
	</tribe>
	<tribe name="IDF5U1_PCFLAG" base="NONE">
		<hostile>
			<to>IDF5U1_VRITRA</to>
			<to>IDF5U1_VRITRAWEAPON</to>
			<to>IDF5U1_VRITRATRAP</to>
			<to>IDF5U1_AGGRESSIVETANK</to>
		</hostile>
		<neutral>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>IDF5U1_TANK</to>
		</neutral>
	</tribe>
	<tribe name="IDKAMAR_PROTECTGUARD_LIGHT" base="GUARD">
		<friend>
			<to>MONSTER</to>
			<to>IDKAMAR_VRITRA</to>
		</friend>
	</tribe>
	<tribe name="IDKAMAR_PROTECTGUARD_DARK" base="GUARD_DARK">
		<friend>
			<to>MONSTER</to>
			<to>IDKAMAR_VRITRA</to>
		</friend>
	</tribe>
	<tribe name="IDF5U2_ARROWTRAP" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>PET</to>
			<to>PET_DARK</to>
		</hostile>
	</tribe>
	<tribe name="IDKAMAR_CANNON" base="USEALL">
		<aggro>
			<to>IDKAMAR_SIEGEWEAPON_DARK</to>
			<to>IDKAMAR_SIEGEWEAPON_LIGHT</to>
			<to>PROTECTGUARD_LIGHT_SIEGEWEAPON</to>
			<to>PROTECTGUARD_DARK_SIEGEWEAPON</to>
		</aggro>
	</tribe>
	<tribe name="IDF5U2_FOBJ" base="MONSTER">
		<friend>
			<to>IDF5U2_FOBJ</to>
		</friend>
		<neutral>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>PET</to>
			<to>PET_DARK</to>
		</neutral>
	</tribe>
	<tribe name="IDASTERIA_IU_WORLD_MONSTER" base="MONSTER">
		<aggro>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>IDASTERIA_IU_WORLD_POLYMORPHL</to>
			<to>IDASTERIA_IU_WORLD_POLYMORPHD</to>
		</aggro>
		<support>
			<to>IDASTERIA_IU_WORLD_MONSTER</to>
			<to>IDASTERIA_IU_WORLD_MONSTER2</to>
		</support>
	</tribe>
	<tribe name="IDASTERIA_IU_WORLD_NPC" base="USEALL">
		<hostile>
			<to>IDASTERIA_IU_WORLD_MONSTER</to>
			<to>IDASTERIA_IU_WORLD_MONSTER2</to>
		</hostile>
		<friend>
			<to>IDASTERIA_IU_WORLD_POLYMORPHL</to>
			<to>IDASTERIA_IU_WORLD_POLYMORPHD</to>
			<to>PC</to>
			<to>PC_DARK</to>
		</friend>
	</tribe>
	<tribe name="IDASTERIA_IU_WORLD_POLYMORPHL" base="PC">
		<hostile>
			<to>IDASTERIA_IU_WORLD_MONSTER</to>
			<to>IDASTERIA_IU_WORLD_MONSTER2</to>
		</hostile>
		<friend>
			<to>PC_DARK</to>
		</friend>
	</tribe>
	<tribe name="IDASTERIA_IU_WORLD_POLYMORPHD" base="PC_DARK">
		<hostile>
			<to>IDASTERIA_IU_WORLD_MONSTER</to>
			<to>IDASTERIA_IU_WORLD_MONSTER2</to>
		</hostile>
		<friend>
			<to>PC</to>
		</friend>
	</tribe>
	<tribe name="IDASTERIA_IU_WORLD_MONSTER2" base="MONSTER">
		<hostile>
			<to>PC</to>
			<to>PC_DARK</to>
			<to>IDASTERIA_IU_WORLD_POLYMORPHL</to>
			<to>IDASTERIA_IU_WORLD_POLYMORPHD</to>
		</hostile>
		<support>
			<to>IDASTERIA_IU_WORLD_MONSTER</to>
			<to>IDASTERIA_IU_WORLD_MONSTER2</to>
		</support>
	</tribe>
</tribe_relations>