<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">

	<xs:include schemaLocation="../modifiers.xsd" />
	<xs:include schemaLocation="../skill_tree/skill_tree.xsd" />
	<xs:include schemaLocation="../bonuses/bonuses.xsd" />

	<xs:element name="item_templates">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="item_template" type="itemTemplate"
					minOccurs="1" maxOccurs="unbounded" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:complexType name="Godstone">
		<xs:attribute name="skillid" type="xs:int"/>
		<xs:attribute name="skilllvl" type="xs:int"/>
		<xs:attribute name="probability" type="xs:int"/>
		<xs:attribute name="probabilityleft" type="xs:int"/>
	</xs:complexType>

	<xs:complexType name="Stigma">
		<xs:sequence>
			<xs:element name="require_skill" type="RequireSkill" minOccurs="0"
					maxOccurs="unbounded" />
			</xs:sequence>
		<xs:attribute name="skillid" type="xs:int"/>
		<xs:attribute name="skilllvl" type="xs:int"/>
		<xs:attribute name="skillid2" type="xs:int" default="0"/>
		<xs:attribute name="skilllvl2" type="xs:int" default="0"/>
		<xs:attribute name="shard" type="xs:int"/>
		<xs:attribute name="type" type="xs:int" default="0"/>
		<xs:attribute name="enchant" type="xs:int" default="0"/>
	</xs:complexType>

	<xs:complexType name="RequireSkill">
		<xs:sequence>
			<xs:element name="skillId" type="xs:int" minOccurs="0"
				maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="skilllvl" type="xs:int"/>
	</xs:complexType>

	<xs:complexType name="itemTemplate">
		<xs:sequence>
			<xs:element name="modifiers" type="Modifiers" minOccurs="0"
				maxOccurs="unbounded" />
			<xs:element name="actions" type="ItemActions" minOccurs="0"
				maxOccurs="unbounded" />
			<xs:element name="godstone" type="Godstone" minOccurs="0"
				maxOccurs="1" />
			<xs:element name="stigma" type="Stigma" minOccurs="0"
				maxOccurs="1" />
			<xs:element name="bonus" type="ItemBonus" minOccurs="0"
				maxOccurs="unbounded" />
			<xs:element name="feed" type="ItemFeed" minOccurs="0"
				maxOccurs="unbounded" />
			<xs:element name="conditioning" type="ItemConditioning" minOccurs="0"
				maxOccurs="unbounded" />
			<xs:element name="robot" type="Robot" minOccurs="0"
				maxOccurs="1" />
			<xs:element name="idian" type="ItemIdian" minOccurs="0"
				maxOccurs="1" />
			<xs:element name="enhance" type="ItemEnhance" minOccurs="0"
				maxOccurs="1" />
		</xs:sequence>
		<xs:attribute name="return_alias" type="xs:string" />
		<xs:attribute name="return_world" type="xs:int" default="0" />
		<xs:attribute name="equipment_type" type="equipType" />
		<xs:attribute name="slot" type="xs:int" default="0" />
		<xs:attribute name="usedelayid" type="xs:int" default="0" />
		<xs:attribute name="usedelay" type="xs:int" default="0" />
		<xs:attribute name="cash_item" type="xs:int" default="0" />
		<xs:attribute name="weapon_boost" type="xs:int" default="0" />
		<xs:attribute name="attack_type" type="EAttackType" />
		<xs:attribute name="attack_gap" type="xs:float" default="0" />
		<xs:attribute name="desc" type="xs:int" default="0" />
		<xs:attribute name="gender" type="Gender" />
		<xs:attribute name="restrict" type="xs:string" use="optional" />
		<xs:attribute name="restrict_max" type="xs:string" use="optional" />
		<xs:attribute name="option_slot_bonus" type="xs:int" default="0" />
		<xs:attribute name="no_enchant" type="xs:boolean" />
		<xs:attribute name="can_proc_enchant" type="xs:boolean" />
		<xs:attribute name="can_fuse" type="xs:boolean" />
		<xs:attribute name="drop" type="xs:boolean" />
		<xs:attribute name="dye" type="xs:boolean" />
		<xs:attribute name="race" type="itemRace" />
		<xs:attribute name="aic" type="xs:int" default="0" />
		<xs:attribute name="ai" type="xs:int" default="0" />
		<xs:attribute name="eic" type="xs:int" default="0" />
		<xs:attribute name="ei" type="xs:int" default="0" />
		<xs:attribute name="cic" type="xs:int" default="0" />
		<xs:attribute name="ci" type="xs:int" default="0" />
		<xs:attribute name="ap" type="xs:int" default="0" />
		<xs:attribute name="price" type="xs:int" default="0" />
		<xs:attribute name="quality" type="itemQuality" />
		<xs:attribute name="item_category" type="xs:string" />
		<xs:attribute name="item_type" type="itemType" />
		<xs:attribute name="max_stack_count" type="xs:int" default="1" />
		<xs:attribute name="armor_type" type="armorType" />
		<xs:attribute name="weapon_type" type="weaponType" />
		<xs:attribute name="mask" type="xs:int" default="0" />
		<xs:attribute name="level" type="xs:int" default="0" />
		<xs:attribute name="id" type="itemId" use="required" />
		<xs:attribute name="name" type="xs:string" />
		<xs:attribute name="quest" type="xs:int" default="0" />
		<xs:attribute name="expire_mins" type="xs:int" />
		<xs:attribute name="cash_minute" type="xs:int" />
		<xs:attribute name="exchange_mins" type="xs:int" />
		<xs:attribute name="world_drop" type="xs:boolean" default="false" />
		<xs:attribute name="ap_rank_min" type="xs:int" default="0" />
		<xs:attribute name="special" type="xs:int" default="0" />
		<xs:attribute name="ancient" type="xs:int" default="0" />
	</xs:complexType>

	<xs:simpleType name="itemSlot">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MAIN_HAND" />
			<xs:enumeration value="SUB_HAND" />
			<xs:enumeration value="HAND_MAIN_OR_SUB" />
			<xs:enumeration value="HELMET" />
			<xs:enumeration value="TORSO" />
			<xs:enumeration value="GLOVES" />
			<xs:enumeration value="BOOTS" />
			<xs:enumeration value="EARRINGS_LEFT" />
			<xs:enumeration value="EARRINGS_RIGHT" />
			<xs:enumeration value="EARRING_RIGHT_OR_LEFT" />
			<xs:enumeration value="RING_LEFT" />
			<xs:enumeration value="RING_RIGHT" />
			<xs:enumeration value="RING_RIGHT_OR_LEFT" />
			<xs:enumeration value="NECKLACE" />
			<xs:enumeration value="SHOULDER" />
			<xs:enumeration value="PANTS" />
			<xs:enumeration value="POWER_SHARD_RIGHT" />
			<xs:enumeration value="POWER_SHARD_LEFT" />
			<xs:enumeration value="SHARD_RIGHT_OR_LEFT" />
			<xs:enumeration value="WINGS" />
			<xs:enumeration value="WAIST" />
			<xs:enumeration value="TORSO_GLOVE_FOOT_SHOULDER_LEG" />
		</xs:restriction>
	</xs:simpleType>

	<xs:complexType name="AbstractItemAction" abstract="true" />

	<xs:complexType name="ItemActions">
		<xs:sequence minOccurs="0" maxOccurs="unbounded">
			<xs:element name="skilllearn" type="SkillLearnAction"
				minOccurs="0" maxOccurs="1" />
			<xs:element name="skilluse" type="SkillUseAction"
				minOccurs="0" maxOccurs="1" />
			<xs:element name="enchant" type="EnchantItemAction"
				minOccurs="0" maxOccurs="1" />
			<xs:element name="queststart" type="QuestStartAction"
				minOccurs="0" maxOccurs="1" />
			<xs:element name="dye" type="DyeAction"
				minOccurs="0" maxOccurs="1" />
			<xs:element name="craftlearn" type="CraftLearnAction"
				minOccurs="0" maxOccurs="1" />
			<xs:element name="extract" type="ExtractAction"
				minOccurs="0" maxOccurs="1" />
			<xs:element name="toypetspawn" type="ToyPetSpawnAction"
				minOccurs="0" maxOccurs="1" />
			<xs:element name="split" type="SplitAction"
				minOccurs="0" maxOccurs="1" />
			<xs:element name="read" type="ReadAction"
				minOccurs="0" maxOccurs="1" />
			<xs:element name="ticket" type="TicketAction"
				minOccurs="0" maxOccurs="1" />
			<xs:element name="title" type="TitleAction"
				minOccurs="0" maxOccurs="1" />
			<xs:element name="emotion" type="EmotionAction"
				minOccurs="0" maxOccurs="1" />
			<xs:element name="cosmetic" type="CosmeticAction"
				minOccurs="0" maxOccurs="1" />
			<xs:element name="motion" type="MotionAction"
				minOccurs="0" maxOccurs="1" />
			<xs:element name="condition" type="ConditionAction"
				minOccurs="0" maxOccurs="1" />
			<xs:element name="identify" type="IdentifyAction"
				minOccurs="0" maxOccurs="1" />
			<xs:element name="mount" type="MountAction"
				minOccurs="0" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="SkillLearnAction">
		<xs:complexContent>
			<xs:extension base="AbstractItemAction">
				<xs:attribute name="race" type="skillRace" />
				<xs:attribute name="level" type="xs:int" />
				<xs:attribute name="class" type="skillPlayerClass" />
				<xs:attribute name="skillid" type="xs:int" />
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>

	<xs:complexType name="SkillUseAction">
		<xs:complexContent>
			<xs:extension base="AbstractItemAction">
				<xs:attribute name="skillid" type="xs:int" />
				<xs:attribute name="level" type="xs:int" />
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>

	<xs:complexType name="EnchantItemAction">
		<xs:complexContent>
			<xs:extension base="AbstractItemAction">
				<xs:attribute name="count" type="xs:int" />
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>

	<xs:complexType name="ExtractAction">
		<xs:complexContent>
			<xs:extension base="AbstractItemAction" />
		</xs:complexContent>
	</xs:complexType>

	<xs:complexType name="DyeAction">
		<xs:complexContent>
			<xs:extension base="AbstractItemAction">
				<xs:attribute name="color" type="xs:string" />
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>

	<xs:complexType name="QuestStartAction">
		<xs:complexContent>
			<xs:extension base="AbstractItemAction" />
		</xs:complexContent>
	</xs:complexType>

	<xs:complexType name="CraftLearnAction">
		<xs:complexContent>
			<xs:extension base="AbstractItemAction">
				<xs:attribute name="recipeid" type="xs:int" />
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>

	<xs:complexType name="ToyPetSpawnAction">
		<xs:complexContent>
			<xs:extension base="AbstractItemAction">
				<xs:attribute name="npcid" type="xs:int" />
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>

	<xs:complexType name="SplitAction">
		<xs:complexContent>
			<xs:extension base="AbstractItemAction" />
		</xs:complexContent>
	</xs:complexType>

	<xs:complexType name="ReadAction">
		<xs:complexContent>
			<xs:extension base="AbstractItemAction" />
		</xs:complexContent>
	</xs:complexType>

	<xs:complexType name="TicketAction">
		<xs:complexContent>
			<xs:extension base="AbstractItemAction">
				<xs:attribute name="function" type="xs:string" />
				<xs:attribute name="param" type="xs:int" />
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>

	<xs:complexType name="TitleAction">
		<xs:complexContent>
			<xs:extension base="AbstractItemAction">
				<xs:attribute name="titleid" type="xs:int" />
				<xs:attribute name="expire" type="xs:int" />
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>

	<xs:complexType name="EmotionAction">
		<xs:complexContent>
			<xs:extension base="AbstractItemAction">
				<xs:attribute name="emotionid" type="xs:int" />
				<xs:attribute name="expire" type="xs:int" />
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>

	<xs:complexType name="MotionAction">
		<xs:complexContent>
			<xs:extension base="AbstractItemAction">
				<xs:attribute name="motionName" type="xs:string" />
				<xs:attribute name="expire" type="xs:int" />
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>

	<xs:complexType name="ConditionAction">
		<xs:complexContent>
			<xs:extension base="AbstractItemAction">
				<xs:attribute name="level" type="xs:int" />
				<xs:attribute name="deep" type="xs:boolean" />
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>

	<xs:complexType name="IdentifyAction">
		<xs:complexContent>
			<xs:extension base="AbstractItemAction">
				<xs:attribute name="target" type="xs:string" />
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>

	<xs:complexType name="MountAction">
		<xs:complexContent>
			<xs:extension base="AbstractItemAction">
				<xs:attribute name="mountId" type="xs:int" />
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>

	<xs:complexType name="CosmeticAction">
		<xs:complexContent>
			<xs:extension base="AbstractItemAction">
				<xs:attribute name="lips" type="xs:string" />
				<xs:attribute name="eyes" type="xs:string" />
				<xs:attribute name="face" type="xs:string" />
				<xs:attribute name="hair" type="xs:string" />
				<xs:attribute name="hairType" type="xs:int" default="0" />
				<xs:attribute name="faceType" type="xs:int" default="0" />
				<xs:attribute name="tattooType" type="xs:int" default="0" />
				<xs:attribute name="makeupType" type="xs:int" default="0" />
				<xs:attribute name="voiceType" type="xs:int" default="0" />
				<xs:attribute name="preset" type="xs:string" />
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>

	<xs:simpleType name="equipType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ARMOR" />
			<xs:enumeration value="WEAPON" />
			<xs:enumeration value="NONE" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="itemId">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="100000000" />
			<xs:maxInclusive value="200000000" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="expireMins">
		<xs:restriction base="xs:int">
			<xs:enumeration value="-1" />
			<xs:enumeration value="0" />
			<xs:enumeration value="1" />
			<xs:enumeration value="3" />
			<xs:enumeration value="5" />
			<xs:enumeration value="10" />
			<xs:enumeration value="21" />
			<xs:enumeration value="30" />
			<xs:enumeration value="31" />
			<xs:enumeration value="60" />
			<xs:enumeration value="61" />
			<xs:enumeration value="81" />
			<xs:enumeration value="90" />
			<xs:enumeration value="91" />
			<xs:enumeration value="120" />
			<xs:enumeration value="121" />
			<xs:enumeration value="180" />
			<xs:enumeration value="181" />
			<xs:enumeration value="300" />
			<xs:enumeration value="301" />
			<xs:enumeration value="360" />
			<xs:enumeration value="361" />
			<xs:enumeration value="600" />
			<xs:enumeration value="601" />
			<xs:enumeration value="720" />
			<xs:enumeration value="721" />
			<xs:enumeration value="1140" />
			<xs:enumeration value="1141" />
			<xs:enumeration value="1440" />
			<xs:enumeration value="1441" />
			<xs:enumeration value="4320" />
			<xs:enumeration value="4321" />
			<xs:enumeration value="10080" />
			<xs:enumeration value="10081" />
			<xs:enumeration value="14400" />
			<xs:enumeration value="14401" />
			<xs:enumeration value="20161" />
			<xs:enumeration value="21600" />
			<xs:enumeration value="21601" />
			<xs:enumeration value="43200" />
			<xs:enumeration value="43201" />
			<xs:enumeration value="86400" />
			<xs:enumeration value="86401" />
			<xs:enumeration value="86402" />
			<xs:enumeration value="129601" />
			<xs:enumeration value="259200" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="armorType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CHAIN" />
			<xs:enumeration value="CLOTHES" />
			<xs:enumeration value="LEATHER" />
			<xs:enumeration value="PLATE" />
			<xs:enumeration value="ROBE" />
			<xs:enumeration value="SHARD" /> <!-- todo to category -->
			<xs:enumeration value="ARROW" />
			<xs:enumeration value="SHIELD" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="weaponType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="DAGGER_1H" />
			<xs:enumeration value="MACE_1H" />
			<xs:enumeration value="SWORD_1H" />
			<xs:enumeration value="TOOLHOE_1H" />
			<xs:enumeration value="BOOK_2H" />
			<xs:enumeration value="ORB_2H" />
			<xs:enumeration value="POLEARM_2H" />
			<xs:enumeration value="STAFF_2H" />
			<xs:enumeration value="SWORD_2H" />
			<xs:enumeration value="TOOLPICK_2H" />
			<xs:enumeration value="TOOLROD_2H" />
			<xs:enumeration value="BOW" />
			<xs:enumeration value="GUN_1H" />
			<xs:enumeration value="CANNON_2H" />
			<xs:enumeration value="HARP_2H" />
			<xs:enumeration value="KEYBLADE_2H" />
			<xs:enumeration value="KEYHAMMER" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="itemQuality">
		<xs:restriction base="xs:string">
			<xs:enumeration value="COMMON" />
			<xs:enumeration value="RARE" />
			<xs:enumeration value="LEGEND" />
			<xs:enumeration value="UNIQUE" />
			<xs:enumeration value="EPIC" />
			<xs:enumeration value="MYTHIC" />
			<xs:enumeration value="JUNK" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="itemCategory">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SWORD" />
			<xs:enumeration value="MACE" />
			<xs:enumeration value="CASH_SWORD" />
			<xs:enumeration value="CASH_NINJA_SWORD" />
			<xs:enumeration value="CASH_SF_SWORD" />
			<xs:enumeration value="CASH_GOLD_SWORD" />
			<xs:enumeration value="CASH_PIRATE_SWORD" />
			<xs:enumeration value="CASH_LOVELYHERO_SWORD" />
			<xs:enumeration value="ITEM_VALEN_SWORD" />
			<xs:enumeration value="ITEM_WHITE_SWORD" />
			<xs:enumeration value="CASH_BAT" />
			<xs:enumeration value="STAR_SWORD" />
			<xs:enumeration value="AXE" />
			<xs:enumeration value="CASH_MACE" />
			<xs:enumeration value="CASH_NINJA_MACE" />
			<xs:enumeration value="CASH_SF_MACE" />
			<xs:enumeration value="CASH_GOLD_MACE" />
			<xs:enumeration value="CASH_PIRATE_MACE" />
			<xs:enumeration value="CASH_LOVELYHERO_MACE" />
			<xs:enumeration value="ITEM_VALEN_MACE" />
			<xs:enumeration value="ITEM_WHITE_MACE" />
			<xs:enumeration value="STAR_MACE" />
			<xs:enumeration value="DAGGER" />
			<xs:enumeration value="CASH_DAGGER" />
			<xs:enumeration value="CASH_NINJA_DAGGER" />
			<xs:enumeration value="CASH_SF_DAGGER" />
			<xs:enumeration value="CASH_GOLD_DAGGER" />
			<xs:enumeration value="CASH_PIRATE_DAGGER" />
			<xs:enumeration value="CASH_LOVELYHERO_DAGGER" />
			<xs:enumeration value="ITEM_VALEN_DAGGER" />
			<xs:enumeration value="ITEM_WHITE_DAGGER" />
			<xs:enumeration value="STAR_DAGGER" />
			<xs:enumeration value="ORB" />
			<xs:enumeration value="CASH_ORB" />
			<xs:enumeration value="CASH_NINJA_ORB" />
			<xs:enumeration value="CASH_SF_ORB" />
			<xs:enumeration value="CASH_GOLD_ORB" />
			<xs:enumeration value="CASH_PIRATE_ORB" />
			<xs:enumeration value="CASH_LOVELYHERO_ORB" />
			<xs:enumeration value="ITEM_VALEN_ORB" />
			<xs:enumeration value="ITEM_WHITE_ORB" />
			<xs:enumeration value="STAR_ORB" />
			<xs:enumeration value="BOOK" />
			<xs:enumeration value="CASH_BOOK" />
			<xs:enumeration value="CASH_NINJA_BOOK" />
			<xs:enumeration value="CASH_SF_BOOK" />
			<xs:enumeration value="CASH_GOLD_BOOK" />
			<xs:enumeration value="CASH_PIRATE_BOOK" />
			<xs:enumeration value="CASH_LOVELYHERO_BOOK" />
			<xs:enumeration value="ITEM_VALEN_BOOK" />
			<xs:enumeration value="ITEM_WHITE_BOOK" />
			<xs:enumeration value="STAR_BOOK" />
			<xs:enumeration value="H2SWORD" />
			<xs:enumeration value="CASH_H2SWORD" />
			<xs:enumeration value="CASH_NINJA" />
			<xs:enumeration value="CASH_SF" />
			<xs:enumeration value="CASH_GOLD" />
			<xs:enumeration value="CASH_PIRATE" />
			<xs:enumeration value="CASH_LOVELYHERO" />
			<xs:enumeration value="ITEM_VALEN" />
			<xs:enumeration value="ITEM_WHITE" />
			<xs:enumeration value="STAR" />
			<xs:enumeration value="POLEARM" />
			<xs:enumeration value="CASH_POLEARM" />
			<xs:enumeration value="CASH_NINJA_POLEARM" />
			<xs:enumeration value="CASH_SF_POLEARM" />
			<xs:enumeration value="CASH_GOLD_POLEARM" />
			<xs:enumeration value="CASH_PIRATE_POLEARM" />
			<xs:enumeration value="CASH_LOVELYHERO_POLEARM" />
			<xs:enumeration value="ITEM_VALEN_POLEARM" />
			<xs:enumeration value="ITEM_WHITE_POLEARM" />
			<xs:enumeration value="STAR_POLEARM" />
			<xs:enumeration value="STAFF" />
			<xs:enumeration value="ROD" />
			<xs:enumeration value="CASH_STAFF" />
			<xs:enumeration value="CASH_NINJA_STAFF" />
			<xs:enumeration value="CASH_SF_STAFF" />
			<xs:enumeration value="CASH_GOLD_STAFF" />
			<xs:enumeration value="CASH_PIRATE_STAFF" />
			<xs:enumeration value="CASH_LOVELYHERO_STAFF" />
			<xs:enumeration value="ITEM_VALEN_STAFF" />
			<xs:enumeration value="ITEM_WHITE_STAFF" />
			<xs:enumeration value="STAR_STAFF" />
			<xs:enumeration value="BOW" />
			<xs:enumeration value="CASH_BOW" />
			<xs:enumeration value="CASH_NINJA_BOW" />
			<xs:enumeration value="CASH_SF_BOW" />
			<xs:enumeration value="CASH_GOLD_BOW" />
			<xs:enumeration value="CASH_PIRATE_BOW" />
			<xs:enumeration value="CASH_LOVELYHERO_BOW" />
			<xs:enumeration value="ITEM_VALEN_BOW" />
			<xs:enumeration value="ITEM_WHITE_BOW" />
			<xs:enumeration value="STAR_BOW" />
			<xs:enumeration value="GUN" />
			<xs:enumeration value="ITEM_NCT_GUN" />
			<xs:enumeration value="ITEM_GF_GUN" />
			<xs:enumeration value="ITEM_INN_GUN" />
			<xs:enumeration value="ITEM_NCW_GUN" />
			<xs:enumeration value="ITEM_NCJ_GUN" />
			<xs:enumeration value="ITEM_CHN_GUN" />
			<xs:enumeration value="MAKEAION" />
			<xs:enumeration value="CASH_NINJA_GUN" />
			<xs:enumeration value="CASH_PIRATE_GUN" />
			<xs:enumeration value="CASH_SF_GUN" />
			<xs:enumeration value="CASH_LOVELYHERO_GUN" />
			<xs:enumeration value="ITEM_VALEN_GUN" />
			<xs:enumeration value="ITEM_WHITE_GUN" />
			<xs:enumeration value="STAR_GUN" />
			<xs:enumeration value="CANNON" />
			<xs:enumeration value="CASH_NINJA_CANNON" />
			<xs:enumeration value="CASH_PIRATE_CANNON" />
			<xs:enumeration value="CASH_SF_CANNON" />
			<xs:enumeration value="CASH_LOVELYHERO_CANNON" />
			<xs:enumeration value="ITEM_VALEN_CANNON" />
			<xs:enumeration value="ITEM_WHITE_CANNON" />
			<xs:enumeration value="STAR_CANNON" />
			<xs:enumeration value="HARP" />
			<xs:enumeration value="ITEM_NCT_HARP" />
			<xs:enumeration value="ITEM_GF_HARP" />
			<xs:enumeration value="ITEM_NCW_HARP" />
			<xs:enumeration value="ITEM_INN_HARP" />
			<xs:enumeration value="ITEM_NCJ_HARP" />
			<xs:enumeration value="ITEM_CHN_HARP" />
			<xs:enumeration value="HA" />
			<xs:enumeration value="CASH_NINJA_HARP" />
			<xs:enumeration value="CASH_PIRATE_HARP" />
			<xs:enumeration value="CASH_SF_HARP" />
			<xs:enumeration value="CASH_LOVELYHERO_HARP" />
			<xs:enumeration value="ITEM_VALEN_HARP" />
			<xs:enumeration value="ITEM_WHITE_HARP" />
			<xs:enumeration value="STAR_HARP" />
			<xs:enumeration value="KEYBLADE" />
			<xs:enumeration value="CASH_NINJA_KEYBLADE" />
			<xs:enumeration value="CASH_SF_KEYBLADE" />
			<xs:enumeration value="ITEM_VALEN_KEYBLADE" />
			<xs:enumeration value="ITEM_WHITE_KEYBLADE" />
			<xs:enumeration value="SHOP_CAT_KEYBLADE" />
			<xs:enumeration value="STAR_KEYBLADE" />
			<xs:enumeration value="HOE" />
			<xs:enumeration value="PICK" />
			<xs:enumeration value="CL_TORSO" />
			<xs:enumeration value="RB_TORSO" />
			<xs:enumeration value="CL_CASH__BODY" />
			<xs:enumeration value="SHOP_PARTY_BODY" />
			<xs:enumeration value="SHOP_SEXY_BODY" />
			<xs:enumeration value="SHOP_CAT_BODY" />
			<xs:enumeration value="SHOP_SEETHROUGH_BODY" />
			<xs:enumeration value="LT_TORSO" />
			<xs:enumeration value="CH_TORSO" />
			<xs:enumeration value="PL_TORSO" />
			<xs:enumeration value="CASH_SANTA_BODY_W" />
			<xs:enumeration value="CASH_SANTA_BODY_M" />
			<xs:enumeration value="CASH_KOREANDRESS_BODY_M" />
			<xs:enumeration value="CASH_KOREANDRESS_BODY_W" />
			<xs:enumeration value="JAPANESEDRESS" />
			<xs:enumeration value="TAIWANESEDRESS" />
			<xs:enumeration value="CHINESEDRESS" />
			<xs:enumeration value="CASH_LINEAGE2_BODY" />
			<xs:enumeration value="CASH_WEDDINGDRESS_BODY" />
			<xs:enumeration value="CASH_PORGUSS_BODY" />
			<xs:enumeration value="CASH_DEBRIE_BODY" />
			<xs:enumeration value="HIGHDRESSL" />
			<xs:enumeration value="HIGHDRESSS" />
			<xs:enumeration value="HIGHDRESSLO" />
			<xs:enumeration value="MANUFACTURE" />
			<xs:enumeration value="CASH_AMERICAN_DRESS_BODY" />
			<xs:enumeration value="CASH_RUSSIAN_DRESS_BODY" />
			<xs:enumeration value="CASH_EUROPEAN_DRESS_BODY" />
			<xs:enumeration value="CASH_WGCOS" />
			<xs:enumeration value="CASH_SWIMSUIT" />
			<xs:enumeration value="CASH_VALENTINEDAY_BODY" />
			<xs:enumeration value="CASH_WHITEDAY_BODY" />
			<xs:enumeration value="CASH_FOOLSDAY_BODY" />
			<xs:enumeration value="HIGHDRESSGT_LI" />
			<xs:enumeration value="HIGHDRESSGT_DA" />
			<xs:enumeration value="HIGHDRESSMD_LI" />
			<xs:enumeration value="HIGHDRESSMD__DA" />
			<xs:enumeration value="CASH_MISSA_BODY" />
			<xs:enumeration value="CASH_WIZARD_BODY" />
			<xs:enumeration value="CASH_DEVA_BODY" />
			<xs:enumeration value="ARENA_REWARD" />
			<xs:enumeration value="CASH_CHILDREN_BODY" />
			<xs:enumeration value="CASH__BODY" />
			<xs:enumeration value="CASH_KOREANDRESS_BODY" />
			<xs:enumeration value="CASH_SHIRT_BODY" />
			<xs:enumeration value="CASH_SCHOOLLOOK_BODY" />
			<xs:enumeration value="CASH_HOODT_BODY" />
			<xs:enumeration value="CASH_BOHEMIAN_BODY" />
			<xs:enumeration value="CASH_ALICE_BODY" />
			<xs:enumeration value="VALENTINE_EVENT_TORSO" />
			<xs:enumeration value="WHITE_EVENT_TORSO" />
			<xs:enumeration value="CASH_GENIE_BODY" />
			<xs:enumeration value="CASH_MAID_BODY" />
			<xs:enumeration value="CASH_DIVING_BODY" />
			<xs:enumeration value="CASH_HUSSAR_BODY" />
			<xs:enumeration value="CASH_NINJA_BODY" />
			<xs:enumeration value="CASH_ANIMAL_BODY" />
			<xs:enumeration value="CASH_SF_BODY" />
			<xs:enumeration value="CASH__MAJE_BODY" />
			<xs:enumeration value="CASH_GOLD_BODY" />
			<xs:enumeration value="CASH_LEOPARD_BODY" />
			<xs:enumeration value="CASH_NURSE_BODY" />
			<xs:enumeration value="CASH_DOCTOR_BODY" />
			<xs:enumeration value="CASH_COUPLET_BODY" />
			<xs:enumeration value="CASH_COUPLEHOOD_BODY" />
			<xs:enumeration value="CASH_PRINTINGT_BODY" />
			<xs:enumeration value="CASH_SHIRTOFF_BODY" />
			<xs:enumeration value="CASH_SNAKE_BODY" />
			<xs:enumeration value="CASH_LEOPARDBIKINI_BODY" />
			<xs:enumeration value="CASH_SEETHROUGH_BODY" />
			<xs:enumeration value="CASH_INNERWARE_BODY" />
			<xs:enumeration value="DINOS_BASEBALLUNIGORM_TORSO" />
			<xs:enumeration value="CASH_SEXYCLUB_BODY" />
			<xs:enumeration value="CASH_PIRATEC_BODY" />
			<xs:enumeration value="CASH_PIRATES_BODY" />
			<xs:enumeration value="CASH_LEOPARDSUIT_BODY" />
			<xs:enumeration value="MS_TORSO_KOREANDRESS" />
			<xs:enumeration value="CASH_HERLOCK_BODY" />
			<xs:enumeration value="CASH_KEI_BODY" />
			<xs:enumeration value="CASH_COP_BODY" />
			<xs:enumeration value="CASH_SNOWBOARD_BODY" />
			<xs:enumeration value="CASH_SNOWBOARD_COUPLE_BODY" />
			<xs:enumeration value="CASH_CHILDRENUNIFORM_BODY" />
			<xs:enumeration value="CASH_TRENCH_BODY" />
			<xs:enumeration value="CASH_BRUCELEE_BODY" />
			<xs:enumeration value="CASH_SNOWOUTFIT_BODY" />
			<xs:enumeration value="CASH_LUXURYSUIT_BODY" />
			<xs:enumeration value="CASH_SNOWMAN" />
			<xs:enumeration value="CASH_CASUALRIDER_BODY" />
			<xs:enumeration value="CASH_CASUALRETRO_BODY" />
			<xs:enumeration value="CASH_LOVELYHERO_BODY" />
			<xs:enumeration value="MS_TORSO_VALEN" />
			<xs:enumeration value="CASH_ALLURING_BODY" />
			<xs:enumeration value="CASH_MARINESLIMFIT_BODY" />
			<xs:enumeration value="CASH_SCARYSPANDEX_BODY" />
			<xs:enumeration value="TORSO_LUXURY" />
			<xs:enumeration value="CASH_BLACKDRESS_BODY" />
			<xs:enumeration value="CASH_TUNINGSTYLE_BODY" />
			<xs:enumeration value="CASH_WORLDBASEBALL_BODY" />
			<xs:enumeration value="CASH_BLACKSWAN_BODY" />
			<xs:enumeration value="CASH_SWIMSUITTATOO_BODY" />
			<xs:enumeration value="CASH_SPACE_BODY" />
			<xs:enumeration value="CASH_BATHWEAR_BODY" />
			<xs:enumeration value="CASH_METALSPIKY_BODY" />
			<xs:enumeration value="CASH_WHITEDRESS_BODY" />
			<xs:enumeration value="CL_GLOVE" />
			<xs:enumeration value="CASH_PORGUSS_GLOVE" />
			<xs:enumeration value="CASH_DEBRIE_GLOVE" />
			<xs:enumeration value="CASH_FOOLSDAY_GLOVE" />
			<xs:enumeration value="CASH__GLOVE" />
			<xs:enumeration value="CASH_BOHEMIAN_GLOVE" />
			<xs:enumeration value="CASH_ALICE_GLOVE" />
			<xs:enumeration value="CASH__HAND" />
			<xs:enumeration value="PL_GLOVE" />
			<xs:enumeration value="CH_GLOVE" />
			<xs:enumeration value="LT_GLOVE" />
			<xs:enumeration value="RB_GLOVE" />
			<xs:enumeration value="CL_CASH__HAND" />
			<xs:enumeration value="SHOP_CAT_HAND" />
			<xs:enumeration value="CASH_BOHEMIAN_SHOULDER" />
			<xs:enumeration value="CASH_HUSSAR_SHOULDER" />
			<xs:enumeration value="PL_SHOULDER" />
			<xs:enumeration value="CH_SHOULDER" />
			<xs:enumeration value="LT_SHOULDER" />
			<xs:enumeration value="RB_SHOULDER" />
			<xs:enumeration value="CL_CASH__SHOULDER" />
			<xs:enumeration value="SHOP_CAT_SHOULDER" />
			<xs:enumeration value="CL_PANTS" />
			<xs:enumeration value="CASH__PANTS" />
			<xs:enumeration value="CASH_BOHEMIAN_PANTS" />
			<xs:enumeration value="CASH_HUSSAR_LEG" />
			<xs:enumeration value="PL_PANTS" />
			<xs:enumeration value="CH_PANTS" />
			<xs:enumeration value="LT_PANTS" />
			<xs:enumeration value="RB_PANTS" />
			<xs:enumeration value="CL_CASH__LEG" />
			<xs:enumeration value="SHOP_CAT_LEG" />
			<xs:enumeration value="CL_SHOES" />
			<xs:enumeration value="CASH_PORGUSS_SHOES" />
			<xs:enumeration value="CASH_DEBRIE_SHOES" />
			<xs:enumeration value="CASH_FOOLSDAY_SHOES" />
			<xs:enumeration value="CASH_DEVA_SHOES" />
			<xs:enumeration value="CASH__SHOES" />
			<xs:enumeration value="CASH_BOHEMIAN_SHOES" />
			<xs:enumeration value="CASH_ALICE_SHOES" />
			<xs:enumeration value="CASH__FOOT" />
			<xs:enumeration value="CASH_HUSSAR_FOOT" />
			<xs:enumeration value="PL_SHOES" />
			<xs:enumeration value="CH_SHOES" />
			<xs:enumeration value="LT_SHOES" />
			<xs:enumeration value="RB_SHOES" />
			<xs:enumeration value="CL_CASH__FOOT" />
			<xs:enumeration value="SHOP_CAT_FOOT" />
			<xs:enumeration value="SHIELD" />
			<xs:enumeration value="CASH_SHIELD" />
			<xs:enumeration value="CASH_NINJA_SHIELD" />
			<xs:enumeration value="CASH_SF_SHIELD" />
			<xs:enumeration value="CASH_GOLD_SHIELD" />
			<xs:enumeration value="CASH_BUCKLER" />
			<xs:enumeration value="CASH_PIRATE_SHIELD" />
			<xs:enumeration value="CASH_LOVELYHERO_SHIELD" />
			<xs:enumeration value="ITEM_VALEN_SHIELD" />
			<xs:enumeration value="ITEM_WHITE_SHIELD" />
			<xs:enumeration value="STAR_SHIELD" />
			<xs:enumeration value="EARRING" />
			<xs:enumeration value="CASH_EARRING" />
			<xs:enumeration value="NECKLACE" />
			<xs:enumeration value="RING" />
			<xs:enumeration value="BELT" />
			<xs:enumeration value="AC_HEAD" />
			<xs:enumeration value="RB_HEAD" />
			<xs:enumeration value="CH_HEAD" />
			<xs:enumeration value="LT_HEAD" />
			<xs:enumeration value="PL_HEAD" />
			<xs:enumeration value="GLASSES" />
			<xs:enumeration value="HALLOWEEN_HEAD" />
			<xs:enumeration value="CASH_GLASSES" />
			<xs:enumeration value="CASH_HEAD" />
			<xs:enumeration value="CASH_DEVA_HEAD" />
			<xs:enumeration value="CASH_PUMPKIN_HEAD" />
			<xs:enumeration value="CONTEST_BOWLER" />
			<xs:enumeration value="CONTEST_COCK" />
			<xs:enumeration value="CONTEST_CROW" />
			<xs:enumeration value="CONTEST_GHOST" />
			<xs:enumeration value="CL_CASH__HEAD" />
			<xs:enumeration value="EVENT_MUNIN_MAGIC_FEDORA" />
			<xs:enumeration value="SHOP_PARTY_HEAD" />
			<xs:enumeration value="SHOP_CAT_HEAD" />
			<xs:enumeration value="SHOP_SEETHROUGH_HEAD" />
			<xs:enumeration value="BEAR_HEAD" />
			<xs:enumeration value="RABBIT_HEAD" />
			<xs:enumeration value="SUNFLOWER_HEAD" />
			<xs:enumeration value="TULIP_HEAD" />
			<xs:enumeration value="CHERUBIM_HEAD" />
			<xs:enumeration value="SPRIGG_HEAD" />
			<xs:enumeration value="HEART_HEAD" />
			<xs:enumeration value="CASH_CHERUBIM_HEAD" />
			<xs:enumeration value="CASH_SPRIGG_HEAD" />
			<xs:enumeration value="CASH_GRAPE_HEAD" />
			<xs:enumeration value="CASH_SANTA_HEAD" />
			<xs:enumeration value="CASH_PORGUSS_HEAD" />
			<xs:enumeration value="CASH_DEBRIE_HEAD" />
			<xs:enumeration value="CASH_AC_HEAD" />
			<xs:enumeration value="CASH_SHEEP_HEAD" />
			<xs:enumeration value="CASH_CHESTNUTS_HEAD" />
			<xs:enumeration value="HELM" />
			<xs:enumeration value="CASH_MANDURI_HEAD" />
			<xs:enumeration value="CASH_DEVIL_HEAD" />
			<xs:enumeration value="CASH_VALENTINEDAY_HEAD" />
			<xs:enumeration value="CASH_WHITEDAY_HEAD" />
			<xs:enumeration value="CASH_FOOLSDAY_HEAD" />
			<xs:enumeration value="CASH_BINYEO_HEAD" />
			<xs:enumeration value="CASH_DAENGGI_HEAD" />
			<xs:enumeration value="CASH_GACHAE_HEAD" />
			<xs:enumeration value="CASH_NAMBAWI_HEAD" />
			<xs:enumeration value="CASH_ZAIF_HEAD" />
			<xs:enumeration value="CASH_VIKING_HEAD" />
			<xs:enumeration value="CASH_HIIV_HEAD" />
			<xs:enumeration value="CASH_FETHLOT_HEAD" />
			<xs:enumeration value="CASH_DARU_HEAD" />
			<xs:enumeration value="CASH_MUTA_HEAD" />
			<xs:enumeration value="CASH_FRILLFAIMAM_HEAD" />
			<xs:enumeration value="CASH_FUNGY_HEAD" />
			<xs:enumeration value="CASH_SPAKY_HEAD" />
			<xs:enumeration value="CASH_MINX_HEAD" />
			<xs:enumeration value="CASH_MISSA_HEAD" />
			<xs:enumeration value="CASH_WIZARD_HEAD" />
			<xs:enumeration value="CASH_CHILDREN_HEAD" />
			<xs:enumeration value="CASH_VICTORYDAY_HEAD" />
			<xs:enumeration value="CASH__HEAD" />
			<xs:enumeration value="CASH_CAP" />
			<xs:enumeration value="CASH_BOHEMIAN_HEAD" />
			<xs:enumeration value="CASH_FRUITHAT_HEAD" />
			<xs:enumeration value="CASH_GENIE_HEAD" />
			<xs:enumeration value="CASH_WEDDINGDRESS_HEAD" />
			<xs:enumeration value="CASH_MAID_HEAD" />
			<xs:enumeration value="CASH_DIVING_HEAD" />
			<xs:enumeration value="CASH_HUSSAR_HEAD" />
			<xs:enumeration value="CASH_ORIENTALMASK_HEAD" />
			<xs:enumeration value="CASH_SENSATION_HEAD" />
			<xs:enumeration value="CASH_HORSEMASK_HEAD" />
			<xs:enumeration value="CASH_BIGRIBBON_HEAD" />
			<xs:enumeration value="CASH_NINJA_HEAD" />
			<xs:enumeration value="CASH_ANIMAL_HEAD" />
			<xs:enumeration value="CASH_SF_HEAD" />
			<xs:enumeration value="CASH_GOLD_HEAD" />
			<xs:enumeration value="CASH_DOCTOR_HEAD" />
			<xs:enumeration value="CASH_NURSE_HEAD" />
			<xs:enumeration value="CASH_BEACHHAT_HEAD" />
			<xs:enumeration value="DINOS_BASEBALLCAP_HEAD" />
			<xs:enumeration value="CASH_PCAPTAIN_HEAD" />
			<xs:enumeration value="CASH_PBANDANA_HEAD" />
			<xs:enumeration value="CASH_EYEPATCH_HEAD" />
			<xs:enumeration value="CASH_LEOPARDSUIT_HEAD" />
			<xs:enumeration value="HEAD_KOREAN_DRESS" />
			<xs:enumeration value="CASH_ANIMALHAT_HEAD" />
			<xs:enumeration value="CASH_COP_HEAD" />
			<xs:enumeration value="CASH_SNOWBOARD_HEAD" />
			<xs:enumeration value="CASH_CHILDRENUNIFORM_HEAD" />
			<xs:enumeration value="CASH_TRENCH_HEAD" />
			<xs:enumeration value="CASH_RUDOLPH" />
			<xs:enumeration value="CASH_LUXURYSUIT_HEAD" />
			<xs:enumeration value="CASH_SHUGOCAP_HEAD" />
			<xs:enumeration value="CASH_CASUALRIDER_HEAD" />
			<xs:enumeration value="CASH_STEAMPUNK_HEAD" />
			<xs:enumeration value="HEAD_LUXURY" />
			<xs:enumeration value="CASH_BLACKDRESS_HEAD" />
			<xs:enumeration value="CASH_WORLDBASEBALL_HEAD" />
			<xs:enumeration value="CASH_BLACKSWAN_HEAD" />
			<xs:enumeration value="CASH_SPACE_HEAD" />
			<xs:enumeration value="CASH_CATCHER_HEAD" />
			<xs:enumeration value="CASH_BATHWEAR_HEAD" />
			<xs:enumeration value="CASH_WHITEDRESS_HEAD" />
			<xs:enumeration value="VALENTINE_EVENT_HEAD" />
			<xs:enumeration value="WHITE_EVENT_HEAD" />
			<xs:enumeration value="STIGMA" />
			<xs:enumeration value="_P_EQUIP_DUAL" />
			<xs:enumeration value="_FI_POTENTIALHEALTH" />
			<xs:enumeration value="_FI_CRIPPLINGCUT" />
			<xs:enumeration value="_FI_ANKLEGRAB" />
			<xs:enumeration value="_FI_LOCKDOWNIMPACT" />
			<xs:enumeration value="_FI_HOWLING" />
			<xs:enumeration value="_WA_STEADINESS" />
			<xs:enumeration value="_WA_GATECRUSH" />
			<xs:enumeration value="_FI_HEAVYIMPACT" />
			<xs:enumeration value="_FI_BLADEMODE" />
			<xs:enumeration value="_KN_HOLYWRATH" />
			<xs:enumeration value="_KN_DIVINEPOWER" />
			<xs:enumeration value="_KN_STUNNINGSNACHER" />
			<xs:enumeration value="_KN_MASSIVEPROVOKE" />
			<xs:enumeration value="_KN_DIVINESLASH" />
			<xs:enumeration value="_KN_BREAKPOWER" />
			<xs:enumeration value="_KN_GRANDPROTECTION" />
			<xs:enumeration value="_KN_CONQUESTMIND" />
			<xs:enumeration value="_KN_ABSOLUTESCARE" />
			<xs:enumeration value="_RA_SPOUTARROW" />
			<xs:enumeration value="_RA_PARALYZEARROW" />
			<xs:enumeration value="_RA_BACKDASHSTAB" />
			<xs:enumeration value="_RA_SILENTARROW" />
			<xs:enumeration value="_RA_LIGHT_HEAVENSTRAP" />
			<xs:enumeration value="_RA_DARK_HEAVENSTRAP" />
			<xs:enumeration value="_RA_LIGHT_FAIRYFLARE" />
			<xs:enumeration value="_RA_DARK_FAIRYFLARE" />
			<xs:enumeration value="_RA_BREATHOFNATURE" />
			<xs:enumeration value="_RA_MARKSMANMODE" />
			<xs:enumeration value="_SC_TRUESHOTMIND" />
			<xs:enumeration value="_RA_RESISTMIND" />
			<xs:enumeration value="_AS_CARVESIGNETCHAIN" />
			<xs:enumeration value="_AS_QUICKMOVE" />
			<xs:enumeration value="_AS_WHISPEROFGALE" />
			<xs:enumeration value="_AS_BLINDSIDE" />
			<xs:enumeration value="_AS_STUNINGINTENTION" />
			<xs:enumeration value="_AS_SIGNETWAVE" />
			<xs:enumeration value="_AS_VISIOUSEYE" />
			<xs:enumeration value="_AS_BLINDINGBURST" />
			<xs:enumeration value="_AS_MIRRORATTACK" />
			<xs:enumeration value="_PR_GRACEOFGOD" />
			<xs:enumeration value="_PR_BLINDINGLIGHT" />
			<xs:enumeration value="_PR_FIRSTAID" />
			<xs:enumeration value="_PR_COURSEWOUND" />
			<xs:enumeration value="_PR_REVERSECONDITION" />
			<xs:enumeration value="_PR_SAGESWISDOM" />
			<xs:enumeration value="_PR_MASSEMERGENTHEAL" />
			<xs:enumeration value="_PR_MASSDISPEL" />
			<xs:enumeration value="_PR_BINDINGHEAL" />
			<xs:enumeration value="_CH_NIGHTWISH" />
			<xs:enumeration value="_CH_HEALINGTANK" />
			<xs:enumeration value="_CL_IMBUEMANA" />
			<xs:enumeration value="_CH_IMPROVEDALLDEFEND" />
			<xs:enumeration value="_CH_SWORDBIND" />
			<xs:enumeration value="_CH_IMPROVEDLIFESTREAM" />
			<xs:enumeration value="_CH_IMPROVEDALLATTACK" />
			<xs:enumeration value="_CH_CHANT_IMPROVEDRESISTSTUN" />
			<xs:enumeration value="_CH_HEROICSTRIKE" />
			<xs:enumeration value="_WI_CURSEDTREE" />
			<xs:enumeration value="_WI_ARCANEWISDOM" />
			<xs:enumeration value="_WI_LIGHT_FROSTPILLAR" />
			<xs:enumeration value="_WI_DARK_FROSTPILLAR" />
			<xs:enumeration value="_WI_COUNTERMAGIC" />
			<xs:enumeration value="_WI_ARCANEPOWER" />
			<xs:enumeration value="_WI_MASSEXPLOSION" />
			<xs:enumeration value="_WI_ARCANEBOOST" />
			<xs:enumeration value="_WI_LIGHT_ICYVEINS" />
			<xs:enumeration value="_WI_DARK_ICYVEINS" />
			<xs:enumeration value="_WI_ICEBARRIER" />
			<xs:enumeration value="_EL_DRAINENERGY" />
			<xs:enumeration value="_EL_BIND" />
			<xs:enumeration value="_EL_SYMPATHETICSWITCH" />
			<xs:enumeration value="_EL_ENCHANTMENTBURST" />
			<xs:enumeration value="_EL_SILENCE" />
			<xs:enumeration value="_EL_DIMISSPOLYMORPH" />
			<xs:enumeration value="_EL_ORDER_ELEMENTALFIELD" />
			<xs:enumeration value="_EL_TERRORSPIRIT" />
			<xs:enumeration value="_EL_ETHERCHARGE" />
			<xs:enumeration value="_EL_ORDER_SACRIFICE" />
			<xs:enumeration value="_FI_REVENGESLASH" />
			<xs:enumeration value="_FI_FORCEBLAST" />
			<xs:enumeration value="_FI_SHOCKIMPULSE" />
			<xs:enumeration value="_KN_GODJUDGEMENT" />
			<xs:enumeration value="_KN_DRAINSWORD" />
			<xs:enumeration value="_RA_PHOENIXARROW" />
			<xs:enumeration value="_RA_DESTRUCTARROW" />
			<xs:enumeration value="_AS_NOVABURST" />
			<xs:enumeration value="_AS_DEMONICBURST" />
			<xs:enumeration value="_PR_DIVINECIRCLE" />
			<xs:enumeration value="_PR_ABYSALCIRCLE" />
			<xs:enumeration value="_CH_ANGELICWISH" />
			<xs:enumeration value="_CH_DEMONICWISH" />
			<xs:enumeration value="_WI_LIGHT_ATOMICENERGY" />
			<xs:enumeration value="_WI_DARK_ATOMICENERGY" />
			<xs:enumeration value="_EL_ORDER_ETHEREMISSION" />
			<xs:enumeration value="_EL_ORDER_ETHERIMPACT" />
			<xs:enumeration value="_FI_DRAINCUT" />
			<xs:enumeration value="_FI_FORCEBURST" />
			<xs:enumeration value="_FI_SHOCKBURST" />
			<xs:enumeration value="_KN_RESISTARMOR" />
			<xs:enumeration value="_KN_GODPUNISHMENT" />
			<xs:enumeration value="_KN_DEMONPUNISHMENT" />
			<xs:enumeration value="_RA_TIGEREYE" />
			<xs:enumeration value="_RA_ANCIENTARROW" />
			<xs:enumeration value="_RA_MYSTICARROW" />
			<xs:enumeration value="_AS_SHADOWWALK" />
			<xs:enumeration value="_AS_SUNBURST" />
			<xs:enumeration value="_AS_MOONBURST" />
			<xs:enumeration value="_PR_SUGGESTION" />
			<xs:enumeration value="_PR_DIVINESANCTUARY" />
			<xs:enumeration value="_PR_ABYSALSANCTUARY" />
			<xs:enumeration value="_CH_RECOVERWORD" />
			<xs:enumeration value="_CH_ANGELICWALL" />
			<xs:enumeration value="_CH_DEMONICWALL" />
			<xs:enumeration value="_WI_ICELANCE" />
			<xs:enumeration value="_WI_LIGHT_VOLCANICFLAME" />
			<xs:enumeration value="_WI_DARK_LAVAWAVE" />
			<xs:enumeration value="_EL_STORMBLADE" />
			<xs:enumeration value="_EL_ORDER_ETHERERUPTION" />
			<xs:enumeration value="_EL_ORDER_SHADOWERUPTION" />
			<xs:enumeration value="_AS_STABSTANCE" />
			<xs:enumeration value="_EL_ESCAPE" />
			<xs:enumeration value="_FI_ENFEEBLEHIT" />
			<xs:enumeration value="_FI_RAGESPIRIT" />
			<xs:enumeration value="ENHANCED__FI_CHARGINGHIT" />
			<xs:enumeration value="ENHANCED__FI_BERSERKSTANCE" />
			<xs:enumeration value="ENHANCED__FI_TECHNICALCOUNTER" />
			<xs:enumeration value="ENHANCED__FI_SHARPNESSHIT" />
			<xs:enumeration value="ENHANCED__FI_CHARGINGSHOCK" />
			<xs:enumeration value="ENHANCED__FI_VORPALHIT" />
			<xs:enumeration value="_KN_HIGHPROVOKE" />
			<xs:enumeration value="ENHANCED__KN_RECOVER" />
			<xs:enumeration value="ENHANCED__KN_SENTINEL" />
			<xs:enumeration value="ENHANCED__KN_BRAINSTORM" />
			<xs:enumeration value="ENHANCED__KN_POWERSINK" />
			<xs:enumeration value="ENHANCED__KN_INVINSIBLESHIELD" />
			<xs:enumeration value="ENHANCED__KN_DESTRUCTWISH" />
			<xs:enumeration value="_RA_ENCHANTBOW" />
			<xs:enumeration value="_RA_LIGHT_THROWINGTRAP" />
			<xs:enumeration value="_RA_DARK_THROWINGTRAP" />
			<xs:enumeration value="ENHANCED__RA_MOVINGSHOT" />
			<xs:enumeration value="ENHANCED__RA_PANTHERMOVE" />
			<xs:enumeration value="ENHANCED__RA_EXPLOSIONARROW" />
			<xs:enumeration value="ENHANCED__RA_HUNTERMIND" />
			<xs:enumeration value="ENHANCED__RA_SHADOWARROW" />
			<xs:enumeration value="ENHANCED__RA_MASSEXPLOSIONARROW" />
			<xs:enumeration value="_AS_SIGNETFLARE" />
			<xs:enumeration value="_AS_HITMANMIND" />
			<xs:enumeration value="ENHANCED__AS_TIGERASSAULT" />
			<xs:enumeration value="ENHANCED__AS_WINDSLASH" />
			<xs:enumeration value="ENHANCED__AS_EXPLOSIONPOISON" />
			<xs:enumeration value="ENHANCED__AS_FLASHSLASH" />
			<xs:enumeration value="ENHANCED__AS_CHAINSIGNETBURST" />
			<xs:enumeration value="ENHANCED__AS_ASSAULTSLASH" />
			<xs:enumeration value="_PR_PURGATORY" />
			<xs:enumeration value="ENHANCED__PR_REGENERAITIONSHINE" />
			<xs:enumeration value="ENHANCED__PR_MEMORYBLUR" />
			<xs:enumeration value="ENHANCED__PR_PAINLINKS" />
			<xs:enumeration value="ENHANCED__PR_REVIVEHAND" />
			<xs:enumeration value="ENHANCED__PR_TRANQUILITY" />
			<xs:enumeration value="ENHANCED__PR_LIGHT_ETERNALSERVENT" />
			<xs:enumeration value="ENHANCED__PR_DARK_ETERNALSERVENT" />
			<xs:enumeration value="_CH_PROTECTSELF" />
			<xs:enumeration value="_CH_SPLASHSWING" />
			<xs:enumeration value="ENHANCED__CH_CHAKRA" />
			<xs:enumeration value="ENHANCED__CH_IMBUEPOWER" />
			<xs:enumeration value="ENHANCED__CH_MPHEAL" />
			<xs:enumeration value="ENHANCED__CH_BLESSPROTECT" />
			<xs:enumeration value="ENHANCED__CH_MOUNTAINCRASH" />
			<xs:enumeration value="ENHANCED__CH_CHANT_INVINCIBLE" />
			<xs:enumeration value="_WI_WINDCUTTER" />
			<xs:enumeration value="ENHANCED__WI_ILLUSIONSTORM" />
			<xs:enumeration value="ENHANCED__WI_ILLUSIONDANCE" />
			<xs:enumeration value="ENHANCED__WI_ROCKFALL" />
			<xs:enumeration value="ENHANCED__WI_SOULGAIN" />
			<xs:enumeration value="ENHANCED__WI_SLEEPINGSTORM" />
			<xs:enumeration value="ENHANCED__WI_FLAMESTRIKE" />
			<xs:enumeration value="_EL_FIREAID" />
			<xs:enumeration value="ENHANCED__EL_LIGHT_SLAVE_STORMSERVENT" />
			<xs:enumeration value="ENHANCED__EL_DARK_SLAVE_STORMSERVENT" />
			<xs:enumeration value="ENHANCED__EL_ENCHANTARMOR" />
			<xs:enumeration value="ENHANCED__EL_ENFEEBLEMENT" />
			<xs:enumeration value="ENHANCED__EL_MANAREVERSE" />
			<xs:enumeration value="ENHANCED__EL_ORDER_DESTRUCTIMPACT" />
			<xs:enumeration value="ENHANCED__EL_HELLPAIN" />
			<xs:enumeration value="ENHANCED__FI_KNEECRASH" />
			<xs:enumeration value="ENHANCED__FI_JUMPATTACK" />
			<xs:enumeration value="ENHANCED__FI_BURSERKLANCE" />
			<xs:enumeration value="ENHANCED__FI_DRAINSWORD" />
			<xs:enumeration value="_PR_POWERSMITE" />
			<xs:enumeration value="ENHANCED__KN_INTIMIDATION" />
			<xs:enumeration value="ENHANCED__KN_FORTITUDEWAVE" />
			<xs:enumeration value="ENHANCED__KN_INVINSIBLEPROTECT" />
			<xs:enumeration value="_AS_VENOMSTAB" />
			<xs:enumeration value="ENHANCED__KN_DESTRUCTSHIELD" />
			<xs:enumeration value="ENHANCED__RA_LIGHT_BLAZINGTRAP" />
			<xs:enumeration value="_KN_REFLECTSHIELD" />
			<xs:enumeration value="ENHANCED__RA_DARK_BLAZINGTRAP" />
			<xs:enumeration value="ENHANCED__RA_TRACKERMIND" />
			<xs:enumeration value="ENHANCED__RA_LIGHTNINGSHOT" />
			<xs:enumeration value="ENHANCED__RA_PAINARROW" />
			<xs:enumeration value="_WI_HPMPEXCHANGE" />
			<xs:enumeration value="_FI_SURVIVORSTANCE" />
			<xs:enumeration value="ENHANCED__AS_STUNBURST" />
			<xs:enumeration value="ENHANCED__AS_SENSEBOOST" />
			<xs:enumeration value="ENHANCED__AS_SILENTBURST" />
			<xs:enumeration value="ENHANCED__AS_VENOMSLASH" />
			<xs:enumeration value="_RA_LIGHT_CALLCOMPANION" />
			<xs:enumeration value="_RA_DARK_CALLCOMPANION" />
			<xs:enumeration value="ENHANCED__PR_LIGHT_HEALINGSERVENT" />
			<xs:enumeration value="_EL_ENERVATIONCURSE" />
			<xs:enumeration value="ENHANCED__PR_DARK_HEALINGSERVENT" />
			<xs:enumeration value="ENHANCED__PR_SUFFERMEMORY" />
			<xs:enumeration value="ENHANCED__PR_HEALERSHAND" />
			<xs:enumeration value="ENHANCED__PR_CALLLIGHTNING" />
			<xs:enumeration value="ENHANCED__CH_SHOCKWAVE" />
			<xs:enumeration value="ENHANCED__CH_SURPERIORHEAL" />
			<xs:enumeration value="ENHANCED__CH_IMPROVEDBODY" />
			<xs:enumeration value="ENHANCED__WI_ELEMENTALSEAL" />
			<xs:enumeration value="ENHANCED__WI_STORMSHOCK" />
			<xs:enumeration value="ENHANCED__WI_ICYSHIELD" />
			<xs:enumeration value="ENHANCED__WI_ZEROPOINT" />
			<xs:enumeration value="_CH_CHANT_IMPROVEDCRITICAL" />
			<xs:enumeration value="ENHANCED__EL_ELEMENTALCHARGE" />
			<xs:enumeration value="ENHANCED__EL_SLEEPINGSPIRIT" />
			<xs:enumeration value="ENHANCED__EL_ORDER_OMEN" />
			<xs:enumeration value="ENHANCED__EL_HELLCURSE" />
			<xs:enumeration value="ENHANCED__CH_SLOWCRASH" />
			<xs:enumeration value="_FI_SEISMICDRAIN" />
			<xs:enumeration value="_KN_THUNDERBLADE" />
			<xs:enumeration value="_RA_DRAINEYE" />
			<xs:enumeration value="_AS_SHADOWDODGE" />
			<xs:enumeration value="_PR_HEALINGGRACE" />
			<xs:enumeration value="_CH_SOAREDROCK" />
			<xs:enumeration value="_WI_SUMMONTORNADO" />
			<xs:enumeration value="_EL_ELEMENTTRANSFER" />
			<xs:enumeration value="_FI_SPINNINGPARRY" />
			<xs:enumeration value="_FI_WHIRLDRAIN" />
			<xs:enumeration value="_KN_MOVINGSTANCE" />
			<xs:enumeration value="_KN_TRIPLESNACHER" />
			<xs:enumeration value="_RA_SNAKEBITETRAP" />
			<xs:enumeration value="_RA_RAPIDSHOT" />
			<xs:enumeration value="_AS_SHADOWDROP" />
			<xs:enumeration value="_AS_LIGHT_DEFENDROAR" />
			<xs:enumeration value="_AS_DARK_DEFENDROAR" />
			<xs:enumeration value="_PR_RESTORELIFE" />
			<xs:enumeration value="_PR_OFFENSIVEMODE" />
			<xs:enumeration value="_CH_PHOENIXSTEP" />
			<xs:enumeration value="_CH_TRIPLETHRUST" />
			<xs:enumeration value="_WI_HIBERNATION" />
			<xs:enumeration value="_WI_CYCLONESTRIKE" />
			<xs:enumeration value="_EL_SOULSTEAL" />
			<xs:enumeration value="_EL_ELEMENTALBOOST" />
			<xs:enumeration value="_FI_ANKLESNATCHER" />
			<xs:enumeration value="_WI_DEEPSLEEP" />
			<xs:enumeration value="_KN_EXECUTIONALSTRIKE" />
			<xs:enumeration value="_RA_SEALINGARROW" />
			<xs:enumeration value="_AS_RUNNERSSTANCE" />
			<xs:enumeration value="_AS_BACKBLOW" />
			<xs:enumeration value="_PR_THUNDEROFPUNISHMENT" />
			<xs:enumeration value="_CH_HARSHTHRUST" />
			<xs:enumeration value="_EL_ORDER_PROTECT" />
			<xs:enumeration value="_RA_INSTANTSPRINT" />
			<xs:enumeration value="_EL_EARTHGUARDIANCE" />
			<xs:enumeration value="_PR_SPRINT" />
			<xs:enumeration value="_FI_MAGICALBARRIER" />
			<xs:enumeration value="_KN_REVIVALTOUCH" />
			<xs:enumeration value="_CH_ENCOURAGERUN" />
			<xs:enumeration value="_WI_WAKEUP" />
			<xs:enumeration value="ENHANCED__BA_DELAYPARALYZE" />
			<xs:enumeration value="_BA_FOCUSDEFENCE" />
			<xs:enumeration value="_BA_LULLABY" />
			<xs:enumeration value="ENHANCED__BA_PLAYINGSTYLESCHANGEA" />
			<xs:enumeration value="ENHANCED__BA_PLAYINGSTYLESCHANGEB" />
			<xs:enumeration value="_BA_SANCTUARY" />
			<xs:enumeration value="_BA_SONGOFBIND" />
			<xs:enumeration value="ENHANCED__BA_SONGOFBLESS" />
			<xs:enumeration value="ENHANCED__BA_SONGOFBRAVE" />
			<xs:enumeration value="_BA_SONGOFEARTH" />
			<xs:enumeration value="ENHANCED__BA_SONGOFFIRM" />
			<xs:enumeration value="ENHANCED__BA_SONGOFGALE" />
			<xs:enumeration value="_BA_SONGOFMANAREVERSE" />
			<xs:enumeration value="ENHANCED__BA_SONGOFMASSHEAL" />
			<xs:enumeration value="ENHANCED__BA_SONGOFPAIN" />
			<xs:enumeration value="_BA_SONGOFREGENERATION" />
			<xs:enumeration value="_BA_SONGOFRESURRECTION" />
			<xs:enumeration value="ENHANCED__BA_SONGOFSERENITY" />
			<xs:enumeration value="_BA_SONGOGPEACE" />
			<xs:enumeration value="ENHANCED__GU_ANTIGRAVITYCANNON" />
			<xs:enumeration value="ENHANCED__GU_ARMORBREAK" />
			<xs:enumeration value="_GU_BULLSEYE" />
			<xs:enumeration value="ENHANCED__GU_EMPOWERAMMO" />
			<xs:enumeration value="_GU_EMPOWERMAGIC" />
			<xs:enumeration value="ENHANCED__GU_FIRECHAINREFRESH" />
			<xs:enumeration value="_GU_FLAMECANNON" />
			<xs:enumeration value="_GU_ICECANNON" />
			<xs:enumeration value="_GU_MAGICALBREATHING" />
			<xs:enumeration value="ENHANCED__GU_MENTALICCANNON" />
			<xs:enumeration value="_GU_MENTALSNATCHER" />
			<xs:enumeration value="ENHANCED__GU_ONEPOINTRAPIDFIRE" />
			<xs:enumeration value="_GU_RAPIDSTANCE" />
			<xs:enumeration value="ENHANCED__GU_SHOCKCANNON" />
			<xs:enumeration value="_GU_SILENCESHOT" />
			<xs:enumeration value="_GU_SOULBREAKCANNON" />
			<xs:enumeration value="ENHANCED__GU_SUNNINGSHOT" />
			<xs:enumeration value="_GU_SURVIVALINSTINCT" />
			<xs:enumeration value="ENHANCED__GU_VASSBLASTER" />
			<xs:enumeration value="ENHANCED__GU_VEINSNIPE" />
			<xs:enumeration value="_RI_EATHIUMCURTAIN" />
			<xs:enumeration value="_RI_EATHIUMCOMPRESS" />
			<xs:enumeration value="_RI_SHIELDDESTROYER" />
			<xs:enumeration value="_RI_FORWARDSTRIKE" />
			<xs:enumeration value="_RI_RAPIDCHARGE" />
			<xs:enumeration value="_RI_EATHIUMBLAST" />
			<xs:enumeration value="_RI_TUNESENSOR" />
			<xs:enumeration value="_RI_CHAINFIRE" />
			<xs:enumeration value="_RI_ITHEUMREFLECTOR" />
			<xs:enumeration value="_RI_EATHIUMREACTIVE" />
			<xs:enumeration value="ENHANCED__RI_FLAMETHROW" />
			<xs:enumeration value="ENHANCED__RI_POWEROVERDRIVE" />
			<xs:enumeration value="ENHANCED__RI_RAPIDATTACK" />
			<xs:enumeration value="ENHANCED__RI_OVERCHARGE" />
			<xs:enumeration value="ENHANCED__RI_POWERSLAM" />
			<xs:enumeration value="ENHANCED__RI_WEAPONSMASH" />
			<xs:enumeration value="ENHANCED__RI_SPEEDOVERDRIVE" />
			<xs:enumeration value="ENHANCED__RI_BINDINGSLAM" />
			<xs:enumeration value="ENHANCED__RI_MAGNETICFIELD" />
			<xs:enumeration value="ENHANCED__RI_EATHIUMWALL" />
			<xs:enumeration value="CRYSTAL" />
			<xs:enumeration value="TOOL" />
			<xs:enumeration value="VEGETABLE" />
			<xs:enumeration value="VEGETABLE_C" />
			<xs:enumeration value="VEGETABLE_R" />
			<xs:enumeration value="ROCK" />
			<xs:enumeration value="GAS" />
			<xs:enumeration value="NOBLEMETAL_C" />
			<xs:enumeration value="NOBLEMETAL_R" />
			<xs:enumeration value="NOBLEMETAL_L" />
			<xs:enumeration value="GEM" />
			<xs:enumeration value="METAL_C" />
			<xs:enumeration value="METAL_R" />
			<xs:enumeration value="METAL_L" />
			<xs:enumeration value="GEMSTONE" />
			<xs:enumeration value="JEWELRY_C" />
			<xs:enumeration value="JEWELRY_R" />
			<xs:enumeration value="JEWELRY_L" />
			<xs:enumeration value="HERB" />
			<xs:enumeration value="HERB_C" />
			<xs:enumeration value="LOG" />
			<xs:enumeration value="TREE_C" />
			<xs:enumeration value="TREE_R" />
			<xs:enumeration value="TREE_L" />
			<xs:enumeration value="JUTE" />
			<xs:enumeration value="PLANT_C" />
			<xs:enumeration value="PLANT_R" />
			<xs:enumeration value="PLANT_L" />
			<xs:enumeration value="BOUQUET" />
			<xs:enumeration value="FRUIT" />
			<xs:enumeration value="BERRY_C" />
			<xs:enumeration value="BERRY_R" />
			<xs:enumeration value="LEAF" />
			<xs:enumeration value="FISH" />
			<xs:enumeration value="FISH_C" />
			<xs:enumeration value="FISH_R" />
			<xs:enumeration value="OD" />
			<xs:enumeration value="OD_ALL_C" />
			<xs:enumeration value="OD_ALL_R" />
			<xs:enumeration value="OD_ALL_L" />
			<xs:enumeration value="SEEFOOD" />
			<xs:enumeration value="SHELL_C" />
			<xs:enumeration value="SHELL_R" />
			<xs:enumeration value="CO_COMBINESKILL_MATERIAL_R" />
			<xs:enumeration value="SLATE" />
			<xs:enumeration value="CRUST" />
			<xs:enumeration value="DUST" />
			<xs:enumeration value="ID_ALL_C" />
			<xs:enumeration value="ID_ALL_R" />
			<xs:enumeration value="ID_ALL_L" />
			<xs:enumeration value="RAWHIDE" />
			<xs:enumeration value="ELEMENTALSTONE" />
			<xs:enumeration value="CRYSTALBALL" />
			<xs:enumeration value="STONE" />
			<xs:enumeration value="SCALE" />
			<xs:enumeration value="MANE" />
			<xs:enumeration value="LEATHER" />
			<xs:enumeration value="CARAPACE" />
			<xs:enumeration value="BONE" />
			<xs:enumeration value="BLOOD" />
			<xs:enumeration value="FEATHER" />
			<xs:enumeration value="HORN" />
			<xs:enumeration value="BRANCH" />
			<xs:enumeration value="TUSK" />
			<xs:enumeration value="CLAW" />
			<xs:enumeration value="ELEMENTALWATER" />
			<xs:enumeration value="MUCUS" />
			<xs:enumeration value="HEART" />
			<xs:enumeration value="TOOTH" />
			<xs:enumeration value="FERN" />
			<xs:enumeration value="WATERDROP" />
			<xs:enumeration value="SPORE" />
			<xs:enumeration value="LEG" />
			<xs:enumeration value="EYE" />
			<xs:enumeration value="SHINE" />
			<xs:enumeration value="DRAGONHIDE" />
			<xs:enumeration value="_B_MATERIAL__R" />
			<xs:enumeration value="_DF_MATERIAL__R" />
			<xs:enumeration value="_E_MATERIAL__R" />
			<xs:enumeration value="_A_MATERIAL__R" />
			<xs:enumeration value="_C_MATERIAL__R" />
			<xs:enumeration value="_D_MATERIAL__R" />
			<xs:enumeration value="_F_MATERIAL__R" />
			<xs:enumeration value="_G_MATERIAL__R" />
			<xs:enumeration value="_H_MATERIAL__R" />
			<xs:enumeration value="_R_MATERIAL__R" />
			<xs:enumeration value="_W_MATERIAL__R" />
			<xs:enumeration value="_T_MATERIAL__R" />
			<xs:enumeration value="_ALL_MATERIAL_U" />
			<xs:enumeration value="WE_MATERIAL_ID_S_DR_E" />
			<xs:enumeration value="WE_MATERIAL_ID_S_PVP_E" />
			<xs:enumeration value="AR_MATERIAL_ID_S_DR_E" />
			<xs:enumeration value="AR_MATERIAL_ID_S_PVP_E" />
			<xs:enumeration value="AC_MATERIAL_ID_S_DR_E" />
			<xs:enumeration value="WE_MATERIAL_ID_S_N_E" />
			<xs:enumeration value="WE_MATERIAL_ID_S_PVE_E" />
			<xs:enumeration value="WE_MATERIAL_ID_S_PVE_M" />
			<xs:enumeration value="AR_MATERIAL_ID_S_N_E" />
			<xs:enumeration value="AR_MATERIAL_ID_S_PVE_E" />
			<xs:enumeration value="AR_MATERIAL_ID_S_PVE_M" />
			<xs:enumeration value="AC_MATERIAL_ID_S_N_E" />
			<xs:enumeration value="POTION_MATERIAL_ID_S_N_U" />
			<xs:enumeration value="QUEST_MATERIAL_ID_S_PVE_M" />
			<xs:enumeration value="PVP_MATERIAL_ATOM" />
			<xs:enumeration value="PVP_MATERIAL_CIRCLE" />
			<xs:enumeration value="PVP_MATERIAL_MINERAL" />
			<xs:enumeration value="PVP_MATERIAL_ORE" />
			<xs:enumeration value="PVE_MATERIAL_ATOM" />
			<xs:enumeration value="PVE_MATERIAL_CIRCLE" />
			<xs:enumeration value="JUNK" />
			<xs:enumeration value="PVE_BREAK_GEM" />
			<xs:enumeration value="PVE_BREAK_ORE" />
			<xs:enumeration value="PVE_BREAK_JEWEL" />
			<xs:enumeration value="POLISH_MATERIAL_CRYSTAL" />
			<xs:enumeration value="MATTER_MATERIAL_PIECE" />
			<xs:enumeration value="MATTER_KEY_CRYSTAL" />
			<xs:enumeration value="MATTER_CRYSTAL_PIECE" />
			<xs:enumeration value="MATTER_MIX_STONE" />
			<xs:enumeration value="TIAMAT_INFINITIVE_TORSO" />
			<xs:enumeration value="TIAMAT_INFINITIVE_PANTS" />
			<xs:enumeration value="TIAMAT_INFINITIVE_SHOES" />
			<xs:enumeration value="TIAMAT_INFINITIVE_GLOVE" />
			<xs:enumeration value="TIAMAT_INFINITIVE_SHOULDER" />
			<xs:enumeration value="TIAMAT_INFINITIVE_HEAD" />
			<xs:enumeration value="SURKANA" />
			<xs:enumeration value="DRAZMA" />
			<xs:enumeration value="DRANA" />
			<xs:enumeration value="DRAGONHORN" />
			<xs:enumeration value="DRAGONSCALE" />
			<xs:enumeration value="MEAT" />
			<xs:enumeration value="DR_BLOOD_MATERIAL" />
			<xs:enumeration value="MUSHROOM" />
			<xs:enumeration value="ROOT" />
			<xs:enumeration value="INGOT" />
			<xs:enumeration value="STUD" />
			<xs:enumeration value="BAR" />
			<xs:enumeration value="WIRE" />
			<xs:enumeration value="CHAIN" />
			<xs:enumeration value="PLATE" />
			<xs:enumeration value="STRAP" />
			<xs:enumeration value="YARN" />
			<xs:enumeration value="TEXTILE" />
			<xs:enumeration value="BOARD" />
			<xs:enumeration value="SQUAREWOOD" />
			<xs:enumeration value="GEMPODER" />
			<xs:enumeration value="METAL" />
			<xs:enumeration value="GEAR" />
			<xs:enumeration value="WHIP" />
			<xs:enumeration value="BOTTLE" />
			<xs:enumeration value="POTION" />
			<xs:enumeration value="CLAMP" />
			<xs:enumeration value="ASSEMBLEBOARD" />
			<xs:enumeration value="COTTON" />
			<xs:enumeration value="MAGICSWORD" />
			<xs:enumeration value="HOOD" />
			<xs:enumeration value="FLAG" />
			<xs:enumeration value="BEAD" />
			<xs:enumeration value="FLASK" />
			<xs:enumeration value="WS_COMBINESKILL_AR_R" />
			<xs:enumeration value="AS_COMBINESKILL_HA_R" />
			<xs:enumeration value="TA_COMBINESKILL_WS_R" />
			<xs:enumeration value="HA_COMBINESKILL_TA_R" />
			<xs:enumeration value="AL_COMBINESKILL_AS_R" />
			<xs:enumeration value="CO_COMBINESKILL_ALL_R" />
			<xs:enumeration value="CO_CANNONPARTS_WS_R" />
			<xs:enumeration value="TA_CANNONPARTS_WS_R" />
			<xs:enumeration value="AL_CANNONPARTS_WS_R" />
			<xs:enumeration value="HA_CANNONPARTS_WS_R" />
			<xs:enumeration value="AS_CANNONPARTS_WS_R" />
			<xs:enumeration value="WS_WE_PARTS_DR_R" />
			<xs:enumeration value="AS_PL_PARTS_DR_R" />
			<xs:enumeration value="AS_CH_PARTS_DR_R" />
			<xs:enumeration value="TA_RT_PARTS_DR_R" />
			<xs:enumeration value="TA_RB_PARTS_DR_R" />
			<xs:enumeration value="HA_WE_PARTS_DR_R" />
			<xs:enumeration value="HA_AC_PARTS_DR_R" />
			<xs:enumeration value="AL_WE_PARTS_DR_R" />
			<xs:enumeration value="WS_WE_PARTS_N_R" />
			<xs:enumeration value="AS_PL_PARTS_N_R" />
			<xs:enumeration value="AS_CH_PARTS_N_R" />
			<xs:enumeration value="TA_RT_PARTS_N_R" />
			<xs:enumeration value="TA_RB_PARTS_N_R" />
			<xs:enumeration value="HA_WE_PARTS_N_R" />
			<xs:enumeration value="HA_AC_PARTS_N_R" />
			<xs:enumeration value="AL_WE_PARTS_N_R" />
			<xs:enumeration value="AL_PO_PARTS_N_R" />
			<xs:enumeration value="AL_SCR_PARTS_N_R" />
			<xs:enumeration value="PVE_MATERIAL_STAR" />
			<xs:enumeration value="PVE_MATERIAL_SUN" />
			<xs:enumeration value="MATTER_ANCIENT_CRYSTAL" />
			<xs:enumeration value="SCROLL" />
			<xs:enumeration value="DRINK" />
			<xs:enumeration value="TEA" />
			<xs:enumeration value="DISH" />
			<xs:enumeration value="SACK" />
			<xs:enumeration value="COOKIE" />
			<xs:enumeration value="BARK" />
			<xs:enumeration value="JELLY" />
			<xs:enumeration value="CANDY" />
			<xs:enumeration value="CHOCOLATE" />
			<xs:enumeration value="CASH_ZERO" />
			<xs:enumeration value="BOLUS" />
			<xs:enumeration value="OMELETTE" />
			<xs:enumeration value="CASH_SONGPYEON" />
			<xs:enumeration value="CASH_POLYMORPH_CANDY" />
			<xs:enumeration value="MAGICSTONE" />
			<xs:enumeration value="SHELL" />
			<xs:enumeration value="QINA" />
			<xs:enumeration value="_BEER" />
			<xs:enumeration value="SIGN" />
			<xs:enumeration value="COCKTAIL" />
			<xs:enumeration value="WDAY_CASTINGCHOCO" />
			<xs:enumeration value="WDAY_ATKCHOCO" />
			<xs:enumeration value="WDAY_CASTINGFLOWER" />
			<xs:enumeration value="WDAY_ATKFLOWER" />
			<xs:enumeration value="WDAY_EFFORTCHOCO" />
			<xs:enumeration value="WDAY_LOVECHOCO" />
			<xs:enumeration value="WDAY_EFFORTFLOWER" />
			<xs:enumeration value="WDAY_LOVEFLOWER" />
			<xs:enumeration value="WDAY_FLOWERBOUQUET" />
			<xs:enumeration value="TRUMP_HEART" />
			<xs:enumeration value="TRUMP_CLUB" />
			<xs:enumeration value="TRUMP_ROYALSTRAIGHT" />
			<xs:enumeration value="TRUMP" />
			<xs:enumeration value="POTION_AREALIMIT" />
			<xs:enumeration value="GOLDAPPLE_ABSOLUTEEXP" />
			<xs:enumeration value="FANTA_ORANGE" />
			<xs:enumeration value="FANTA_GRAPE" />
			<xs:enumeration value="ARROW" />
			<xs:enumeration value="QUEST_SCROLL" />
			<xs:enumeration value="LAMP" />
			<xs:enumeration value="TAIL_SCORPION" />
			<xs:enumeration value="EMBLEM" />
			<xs:enumeration value="SCROLL_REGIST_FIRE" />
			<xs:enumeration value="SCROLL_REGIST_EARTH" />
			<xs:enumeration value="SCROLL_REGIST_WATER" />
			<xs:enumeration value="SCROLL_REGIST_WIND" />
			<xs:enumeration value="SCROLL_CRITICAL_PHY" />
			<xs:enumeration value="SCROLL_SHIELD_ALL" />
			<xs:enumeration value="SCROLL_SPEED_ATK" />
			<xs:enumeration value="SCROLL_SPEED_RUN" />
			<xs:enumeration value="SCROLL_SPEED_FLY" />
			<xs:enumeration value="SEED" />
			<xs:enumeration value="BOMB" />
			<xs:enumeration value="MAGICBALL" />
			<xs:enumeration value="SCROLL_CRITICAL_MAG" />
			<xs:enumeration value="SCROLL_SPEED_CASTING" />
			<xs:enumeration value="CASH_FIRECRACKER" />
			<xs:enumeration value="MARBLE" />
			<xs:enumeration value="CONTROLLER" />
			<xs:enumeration value="BUCKET" />
			<xs:enumeration value="BOX" />
			<xs:enumeration value="VP_RECOVERY" />
			<xs:enumeration value="HOUSING_CHRISTMAS_BM" />
			<xs:enumeration value="__KEYDOOR_RED" />
			<xs:enumeration value="__KEYDOOR_BLUE" />
			<xs:enumeration value="__KEYDOOR_YELLOW" />
			<xs:enumeration value="__KEYDOOR_WHITE" />
			<xs:enumeration value="INK" />
			<xs:enumeration value="ODEQUIPMENT" />
			<xs:enumeration value="KEY_BLUE" />
			<xs:enumeration value="_CANNON_SHOPMATERIAL_C" />
			<xs:enumeration value="JUNK_ROCK" />
			<xs:enumeration value="FIRECRACKER_RED" />
			<xs:enumeration value="FIRECRACKER_YELLOW" />
			<xs:enumeration value="FIRECRACKER_BLUE" />
			<xs:enumeration value="FIRECRACKER_GREEN" />
			<xs:enumeration value="FIRECRACKER_VIOLET" />
			<xs:enumeration value="LETTER" />
			<xs:enumeration value="PAGE" />
			<xs:enumeration value="BADGE" />
			<xs:enumeration value="GRAIN" />
			<xs:enumeration value="IDEVENT_ROOT" />
			<xs:enumeration value="IDEVENT_TOWERHEAL" />
			<xs:enumeration value="BINDSTONE" />
			<xs:enumeration value="PIERRO" />
			<xs:enumeration value="TICKET" />
			<xs:enumeration value="PINCER" />
			<xs:enumeration value="EXTRACT" />
			<xs:enumeration value="HAMMER" />
			<xs:enumeration value="SYNTHESIS" />
			<xs:enumeration value="ENCHANTSTONE" />
			<xs:enumeration value="S2TENCHANT_WATER" />
			<xs:enumeration value="POLISH" />
			<xs:enumeration value="SUB_MATTER" />
			<xs:enumeration value="CASH_SUB" />
			<xs:enumeration value="ITEM" />
			<xs:enumeration value="ANCIENTSTONE" />
			<xs:enumeration value="TYPE_MAGICSTONE" />
			<xs:enumeration value="HOLYSTONE" />
			<xs:enumeration value="HOLYSTONE_LEGEND_STUN" />
			<xs:enumeration value="HOLYSTONE_LEGEND_PARALYZE" />
			<xs:enumeration value="HOLYSTONE_LEGEND_ROOT" />
			<xs:enumeration value="HOLYSTONE_LEGEND_SLIENCE" />
			<xs:enumeration value="HOLYSTONE_LEGEND_BLIND" />
			<xs:enumeration value="HOLYSTONE_LEGEND_SNARE" />
			<xs:enumeration value="HOLYSTONE_LEGEND_SLOW" />
			<xs:enumeration value="HOLYSTONE_LEGEND_POISON" />
			<xs:enumeration value="HOLYSTONE_LEGEND_BLEED" />
			<xs:enumeration value="HOLYSTONE_LEGEND_FIRE_DEMAGE" />
			<xs:enumeration value="HOLYSTONE_LEGEND_WATER_DEMAGE" />
			<xs:enumeration value="HOLYSTONE_LEGEND_AIR_DEMAGE" />
			<xs:enumeration value="HOLYSTONE_LEGEND_EARTH_DEMAGE" />
			<xs:enumeration value="HOLYSTONE_UNIQUE_FIRE_DEMAGE" />
			<xs:enumeration value="HOLYSTONE_UNIQUE_WATER_DEMAGE" />
			<xs:enumeration value="HOLYSTONE_UNIQUE_EARTH_DEMAGE" />
			<xs:enumeration value="HOLYSTONE_UNIQUE_AIR_DEMAGE" />
			<xs:enumeration value="HOLYSTONE_RARE_POISON" />
			<xs:enumeration value="HOLYSTONE_RARE_BLEED" />
			<xs:enumeration value="HOLYSTONE_RARE_FIRE_DEMAGE" />
			<xs:enumeration value="HOLYSTONE_RARE_WATER_DEMAGE" />
			<xs:enumeration value="HOLYSTONE_RARE_EARTH_DEMAGE" />
			<xs:enumeration value="HOLYSTONE_RARE_AIR_DEMAGE" />
			<xs:enumeration value="HOLYSTONE_UNIQUE_STUN" />
			<xs:enumeration value="HOLYSTONE_UNIQUE_PARALYZE" />
			<xs:enumeration value="HOLYSTONE_UNIQUE_ROOT" />
			<xs:enumeration value="HOLYSTONE_UNIQUE_SLIENCE" />
			<xs:enumeration value="HOLYSTONE_UNIQUE_BLIND" />
			<xs:enumeration value="HOLYSTONE_UNIQUE_SLOW" />
			<xs:enumeration value="HOLYSTONE_UNIQUE_SNARE" />
			<xs:enumeration value="HOLYSTONE_UNIQUE_POISON" />
			<xs:enumeration value="HOLYSTONE_UNIQUE_BLEED" />
			<xs:enumeration value="HOLYSTONE_RARE_PARALYZE" />
			<xs:enumeration value="HOLYSTONE_RARE_SILENCE" />
			<xs:enumeration value="HOLYSTONE_RARE_BLIND" />
			<xs:enumeration value="_UNIQUE_STUN" />
			<xs:enumeration value="_UNIQUE_PARALYZE" />
			<xs:enumeration value="_UNIQUE_ROOT" />
			<xs:enumeration value="_UNIQUE_SLIENCE" />
			<xs:enumeration value="_UNIQUE_BLIND" />
			<xs:enumeration value="_UNIQUE_SLOW" />
			<xs:enumeration value="_UNIQUE_SNARE" />
			<xs:enumeration value="_UNIQUE_POISON" />
			<xs:enumeration value="_UNIQUE_BLEED" />
			<xs:enumeration value="_UNIQUE_FIRE_DEMAGE" />
			<xs:enumeration value="_UNIQUE_WATER_DEMAGE" />
			<xs:enumeration value="_UNIQUE_EARTH_DEMAGE" />
			<xs:enumeration value="_UNIQUE_AIR_DEMAGE" />
			<xs:enumeration value="SCROLL_SEAL" />
			<xs:enumeration value="SCROLL_UNSEAL" />
			<xs:enumeration value="BATTERY" />
			<xs:enumeration value="ADHESIVE" />
			<xs:enumeration value="CASH_DYE_RED" />
			<xs:enumeration value="HOUSING_RED" />
			<xs:enumeration value="HOUSING_PINK" />
			<xs:enumeration value="HOUSING_BLUE" />
			<xs:enumeration value="HOUSING_PURPLE" />
			<xs:enumeration value="HOUSING_ORANGE" />
			<xs:enumeration value="HOUSING_YELLOW" />
			<xs:enumeration value="HOUSING_GREEN" />
			<xs:enumeration value="CASH_DYE_OLIVE_GREEN" />
			<xs:enumeration value="CASH_DYE_DEEP_BLUE" />
			<xs:enumeration value="CASH_DYE_ORANGE" />
			<xs:enumeration value="CASH_DYE_DEEP_PURPLE" />
			<xs:enumeration value="CASH_DYE_WHITE" />
			<xs:enumeration value="CASH_DYE_BLACK" />
			<xs:enumeration value="CASH_DYE_PINK" />
			<xs:enumeration value="CASH_DYE_YELLOW" />
			<xs:enumeration value="CASH_DYE_GREEN" />
			<xs:enumeration value="CASH_DYE_ROMANTIC_PURPLE" />
			<xs:enumeration value="FLOWER" />
			<xs:enumeration value="PAPER" />
			<xs:enumeration value="POUCH" />
			<xs:enumeration value="CHARCOAL" />
			<xs:enumeration value="POWDER" />
			<xs:enumeration value="SAND" />
			<xs:enumeration value="WHETSTONE" />
			<xs:enumeration value="MOLD" />
			<xs:enumeration value="SALVE" />
			<xs:enumeration value="BUTTON" />
			<xs:enumeration value="THREAD" />
			<xs:enumeration value="SANDPAPER" />
			<xs:enumeration value="PLANEBLADE" />
			<xs:enumeration value="THORN" />
			<xs:enumeration value="STING" />
			<xs:enumeration value="TAIL" />
			<xs:enumeration value="TESTTUBE" />
			<xs:enumeration value="EGG" />
			<xs:enumeration value="POT" />
			<xs:enumeration value="SAC" />
			<xs:enumeration value="FLOUR" />
			<xs:enumeration value="NODDLES" />
			<xs:enumeration value="NEEDLE" />
			<xs:enumeration value="RULER" />
			<xs:enumeration value="BRUSH" />
			<xs:enumeration value="PLANE" />
			<xs:enumeration value="ELEMENTALWATER_EMPTY" />
			<xs:enumeration value="_N_WS_SHOPMATERIAL_C" />
			<xs:enumeration value="_N_PL_SHOPMATERIAL_C" />
			<xs:enumeration value="_N_CH_SHOPMATERIAL_C" />
			<xs:enumeration value="_N_LT_SHOPMATERIAL_C" />
			<xs:enumeration value="_N_RB_SHOPMATERIAL_C" />
			<xs:enumeration value="_N_HA_SHOPMATERIAL_C" />
			<xs:enumeration value="_N_AC_SHOPMATERIAL_C" />
			<xs:enumeration value="_N_AL_SHOPMATERIAL_C" />
			<xs:enumeration value="_N_PO_SHOPMATERIAL_C" />
			<xs:enumeration value="_N_SCR_SHOPMATERIAL_C" />
			<xs:enumeration value="_N_CS_SHOPMATERIAL_C" />
			<xs:enumeration value="_DR_ALL_SHOPMATERIAL_C" />
			<xs:enumeration value="FAOP_CANNON_SHOPMATERIAL_C" />
			<xs:enumeration value="FBOP_CANNON_SHOPMATERIAL_C" />
			<xs:enumeration value="UOP_CANNON_SHOPMATERIAL_C" />
			<xs:enumeration value="_DR_ALL_SHOPCMATERIAL_C" />
			<xs:enumeration value="WS_SHOPMATERIAL_C" />
			<xs:enumeration value="AS_SHOPMATERIAL_C" />
			<xs:enumeration value="TA_SHOPMATERIAL_C" />
			<xs:enumeration value="HA_SHOPMATERIAL_C" />
			<xs:enumeration value="AL_SHOPMATERIAL_C" />
			<xs:enumeration value="CO_SHOPMATERIAL_C" />
			<xs:enumeration value="WS_SHOPCMATERIAL_C" />
			<xs:enumeration value="AS_SHOPCMATERIAL_C" />
			<xs:enumeration value="TA_SHOPCMATERIAL_C" />
			<xs:enumeration value="HA_SHOPCMATERIAL_C" />
			<xs:enumeration value="AL_SHOPCMATERIAL_C" />
			<xs:enumeration value="SHAKER" />
			<xs:enumeration value="WE_SHOPCMATERIAL_S_PVP_E" />
			<xs:enumeration value="WE_SHOPCMATERIAL_S_PVE_E" />
			<xs:enumeration value="WE_SHOPCMATERIAL_S_PVE_M" />
			<xs:enumeration value="AR_SHOPCMATERIAL_S_PVP_E" />
			<xs:enumeration value="AR_SHOPCMATERIAL_S_PVE_E" />
			<xs:enumeration value="AR_SHOPCMATERIAL_S_PVE_M" />
			<xs:enumeration value="POTION_SHOPCMATERIAL_S_N_U" />
			<xs:enumeration value="WS_WE_LONG_PARTS_N_E" />
			<xs:enumeration value="HA_WE_LONG_PARTS_N_E" />
			<xs:enumeration value="WE_MATERIAL_ID_S_LONG_E" />
			<xs:enumeration value="POTION_MATERIAL_ID_S_N_R" />
			<xs:enumeration value="AC_MATERIAL_ID_S_PVE_M" />
			<xs:enumeration value="AC_SHOPCMATERIAL_S_PVE_M" />
			<xs:enumeration value="MATERIAL_DUST" />
			<xs:enumeration value="MATERIAL_CRYSTAL" />
			<xs:enumeration value="FUEL_SHOP" />
			<xs:enumeration value="SHOT_SHOP" />
			<xs:enumeration value="FUEL_COMBINESKILL" />
			<xs:enumeration value="SHOT_COMBINESKILL" />
			<xs:enumeration value="VIGOR" />
			<xs:enumeration value="BATTERY_SHOP" />
			<xs:enumeration value="BATTERY_COMBINESKILL" />
			<xs:enumeration value="PVP_MATERIAL_JEWEL" />
			<xs:enumeration value="PVP_MATERIAL_GEM" />
			<xs:enumeration value="PVE_MATERIAL_JEWEL" />
			<xs:enumeration value="PVE_MATERIAL_GEM" />
			<xs:enumeration value="POLISH_MATERIAL_WATER" />
			<xs:enumeration value="MATTER_MATERIAL_SHOP" />
			<xs:enumeration value="LOOK_LIGHT_TORSO" />
			<xs:enumeration value="LOOK_LIGHT_PANTS" />
			<xs:enumeration value="LOOK_LIGHT_SHOES" />
			<xs:enumeration value="LOOK_LIGHT_GLOVE" />
			<xs:enumeration value="LOOK_LIGHT_SHOULDER" />
			<xs:enumeration value="LOOK_LIGHT_HEAD" />
			<xs:enumeration value="LOOK_DARK_TORSO" />
			<xs:enumeration value="LOOK_DARK_PANTS" />
			<xs:enumeration value="LOOK_DARK_SHOES" />
			<xs:enumeration value="LOOK_DARK_GLOVE" />
			<xs:enumeration value="LOOK_DARK_SHOULDER" />
			<xs:enumeration value="LOOK_DARK_HEAD" />
			<xs:enumeration value="MYTHICHIGH_SWORD" />
			<xs:enumeration value="MYTHICHIGH_MACE" />
			<xs:enumeration value="MYTHICHIGH" />
			<xs:enumeration value="MYTHICHIGH_SHIELD" />
			<xs:enumeration value="MYTHICHIGH_DAGGER" />
			<xs:enumeration value="MYTHICHIGH_POLEARM" />
			<xs:enumeration value="MYTHICHIGH_BOW" />
			<xs:enumeration value="MYTHICHIGH_STAFF" />
			<xs:enumeration value="MYTHICHIGH_ORB" />
			<xs:enumeration value="MYTHICHIGH_BOOK" />
			<xs:enumeration value="MYTHICHIGH_GUN" />
			<xs:enumeration value="MYTHICHIGH_CANNON" />
			<xs:enumeration value="MYTHICHIGH_HARP" />
			<xs:enumeration value="MYTHICHIGH_KEYBLADE" />
			<xs:enumeration value="MYTHICHIGH_TORSO" />
			<xs:enumeration value="MYTHICHIGH_PANTS" />
			<xs:enumeration value="MYTHICHIGH_GLOVE" />
			<xs:enumeration value="MYTHICHIGH_SHOES" />
			<xs:enumeration value="MYTHICHIGH_SHOULDER" />
			<xs:enumeration value="WORLDRAID_ATT" />
			<xs:enumeration value="WORLDRAID_DEF" />
			<xs:enumeration value="COIN_MHIGH_PW" />
			<xs:enumeration value="COIN_MHIGH_MW" />
			<xs:enumeration value="COIN_MHIGH_AR" />
			<xs:enumeration value="COIN_MATERIAL_ABYSS" />
			<xs:enumeration value="SUB_MATERIAL_SOUL" />
			<xs:enumeration value="EXP_EXTRACTION" />
			<xs:enumeration value="SKILLBOOK" />
			<xs:enumeration value="SKILLBOOK_HOUSING" />
			<xs:enumeration value="DANCE" />
			<xs:enumeration value="CASH_ACTION" />
			<xs:enumeration value="CASH_ACTION_MOCK" />
			<xs:enumeration value="CASH_ACTION_SURR" />
			<xs:enumeration value="CASH_ACTION_JUGG" />
			<xs:enumeration value="CASH_ACTION_CANCAN" />
			<xs:enumeration value="CASH_ACTION_PLANE" />
			<xs:enumeration value="CASH_ACTION_PETAL" />
			<xs:enumeration value="CASH_ACTION_GUM" />
			<xs:enumeration value="CASH_ACTION_HUG" />
			<xs:enumeration value="CASH_ACTION_PIPE" />
			<xs:enumeration value="CASH_ACTION_SCISSOR" />
			<xs:enumeration value="CASH_ACTION_ROCK" />
			<xs:enumeration value="CASH_ACTION_PAPERS" />
			<xs:enumeration value="CASH_ACTION_TRUE" />
			<xs:enumeration value="CASH_ACTION_FALSE" />
			<xs:enumeration value="CASH_ACTION_ZERO" />
			<xs:enumeration value="CASH_ACTION_ONE" />
			<xs:enumeration value="CASH_ACTION_TWO" />
			<xs:enumeration value="CASH_ACTION_THREE" />
			<xs:enumeration value="CASH_ACTION_FOUR" />
			<xs:enumeration value="CASH_ACTION_FIVE" />
			<xs:enumeration value="CASH_ACTION_SIX" />
			<xs:enumeration value="CASH_ACTION_SEVEN" />
			<xs:enumeration value="CASH_ACTION_EIGHT" />
			<xs:enumeration value="CASH_ACTION_NINE" />
			<xs:enumeration value="CASH_ACTION_BLUE" />
			<xs:enumeration value="CASH_ACTION_WHITE" />
			<xs:enumeration value="CASH_ACTION_DRUM" />
			<xs:enumeration value="CASH_ACTION_HARP" />
			<xs:enumeration value="CASH_ACTION_SAXOPH" />
			<xs:enumeration value="CASH_ACTION_DEAD" />
			<xs:enumeration value="CASH_ACTION_SING" />
			<xs:enumeration value="CASH_ACTION_TWOFLAG" />
			<xs:enumeration value="CASH_ACTION_OMELETTE" />
			<xs:enumeration value="CASH_ACTION_SWEEPING" />
			<xs:enumeration value="CASH_ACTION_DIVING" />
			<xs:enumeration value="CASH_ACTION_CATCHER" />
			<xs:enumeration value="CASH_ACTION_PITCHER" />
			<xs:enumeration value="CASH_ACTION_BATTER" />
			<xs:enumeration value="CASH_ACTION_BRUCELEE" />
			<xs:enumeration value="CASH_ACTION_BOW" />
			<xs:enumeration value="CASH_ACTION_BOW_MISS" />
			<xs:enumeration value="CASH_ACTION_SNOWMAN" />
			<xs:enumeration value="CASH_CARD_TITLE" />
			<xs:enumeration value="CARD_TITLE" />
			<xs:enumeration value="XPBOOST" />
			<xs:enumeration value="CASH_APBOOST" />
			<xs:enumeration value="CASH_GATHERBOOST" />
			<xs:enumeration value="CASH_CRAFTBOOST" />
			<xs:enumeration value="CUBE" />
			<xs:enumeration value="CASH_CARD" />
			<xs:enumeration value="CASH_COUPON_CUSTOMIZE" />
			<xs:enumeration value="CASH_COUPON_SEX_EXCHANGE" />
			<xs:enumeration value="CASH_HOUSE_EXCHANGE" />
			<xs:enumeration value="CHANGE_CHARACTER_NAME" />
			<xs:enumeration value="CHANGE_LEGION_NAME" />
			<xs:enumeration value="CASH_COUPON_HAIR_EXCHANGE_M" />
			<xs:enumeration value="CASH_COUPON_HAIR_EXCHANGE_W" />
			<xs:enumeration value="DYE_HAIR" />
			<xs:enumeration value="DYE_SKIN" />
			<xs:enumeration value="HOUSING" />
			<xs:enumeration value="HOUSING_SHELF_WORLD_BM_HLWN" />
			<xs:enumeration value="HOUSING_WORLD_SHELF_BM_ORI" />
			<xs:enumeration value="HOUSING_VALENTINE_BM" />
			<xs:enumeration value="HOUSING_WHITE_BM" />
			<xs:enumeration value="HOUSING_WORLD_WALLDECO_BM_CHN" />
			<xs:enumeration value="HOUSING_WORLD_WALLDECO_BM_JPN" />
			<xs:enumeration value="HOUSING_PETAL_WALLDECO" />
			<xs:enumeration value="HOUSING_IVBOX_TYPEA_WORLD_BM_XMAS" />
			<xs:enumeration value="HOUSING_IVBOX_TYPEA_WORLD_BM_HLWN" />
			<xs:enumeration value="HOUSING_IVBOX_TYPEB_WORLD_BM_HLWN" />
			<xs:enumeration value="HOUSING_WORLD_IVBOX_TYPEA_BM_ORI" />
			<xs:enumeration value="HOUSING_WORLD_IVBOX_TYPEB_BM_ORI" />
			<xs:enumeration value="HOUSING_PETAL_DRESSER" />
			<xs:enumeration value="HOUSING_CLOUD_BM" />
			<xs:enumeration value="HOUSING_BED_WORLD_BM_XMAS" />
			<xs:enumeration value="HOUSING_BED_WORLD_BM_HLWN" />
			<xs:enumeration value="HOUSING_WORLD_BED_BM_ORI" />
			<xs:enumeration value="HOUSING_PETAL_BED" />
			<xs:enumeration value="HOUSING_SOFA_WORLD_BM_XMAS" />
			<xs:enumeration value="HOUSING_SOFA_WORLD_BM_HLWN" />
			<xs:enumeration value="HOUSING_WORLD_SOFA_BM_ORI" />
			<xs:enumeration value="HOUSING_PETAL_SOFA" />
			<xs:enumeration value="HOUSING_LIGHT_STAND_WORLD_BM_HLWN" />
			<xs:enumeration value="HOUSING_LIGHT_TABLE_WORLD_BM_HLWN" />
			<xs:enumeration value="HOUSING_WORLD_LIGHT_STAND_BM_ORI" />
			<xs:enumeration value="HOUSING_WORLD_LIGHT_TABLE_BM_ORI" />
			<xs:enumeration value="HOUSING_PETAL_LAMP" />
			<xs:enumeration value="HOUSING_PART" />
			<xs:enumeration value="HOUSING_PARTION_WORLD_BM_XMAS" />
			<xs:enumeration value="HOUSING_PARTION_WORLD_BM_HLWN" />
			<xs:enumeration value="HOUSING_WORLD_PARTION_BM_ORI" />
			<xs:enumeration value="HOUSING_PETAL_PARTITION" />
			<xs:enumeration value="HOUSING_OWNER_POT_COMMON" />
			<xs:enumeration value="HOUSING_VISITOR_POT_WING" />
			<xs:enumeration value="HOUSING_OWNER_POT_RARE" />
			<xs:enumeration value="HOUSING_JUKEBOX" />
			<xs:enumeration value="HOUSING_LIVEBOX_CUBE" />
			<xs:enumeration value="HOUSING_LIVEBOX_CIRCLE" />
			<xs:enumeration value="HOUSING_LIVEBOX_CIRYLINDER" />
			<xs:enumeration value="HOUSING_LIVEBOX_TRIANGLE" />
			<xs:enumeration value="HOUSING_BIRTHCAKE" />
			<xs:enumeration value="HOUSING_VISITOR_POT_COIN" />
			<xs:enumeration value="HOUSING_VISITOR_POT_UNIQUE" />
			<xs:enumeration value="HOUSING_VISITOR_POT_RARE" />
			<xs:enumeration value="HOUSING_VISITOR_POT_LEGEND" />
			<xs:enumeration value="HOUSING_OWNER_POT_BM" />
			<xs:enumeration value="HOUSING_VISITOR_POT_EGG" />
			<xs:enumeration value="HOUSING_VISITOR_POT_ARMOR" />
			<xs:enumeration value="HOUSING_VISITOR_POT_DRAGONMATERIAL" />
			<xs:enumeration value="HOUSING_PLAYER" />
			<xs:enumeration value="HOUSING__C" />
			<xs:enumeration value="HOUSING_RUG_BM_HLWN" />
			<xs:enumeration value="HOUSING_PETAL_RUG" />
			<xs:enumeration value="HOUSING_PETAL_PARASOLCHAIR" />
			<xs:enumeration value="HOUSING_PETAL_PARASOL" />
			<xs:enumeration value="HOUSING_VISITOR_POT_FARM" />
			<xs:enumeration value="HOUSING_VISITOR_POT_STONE" />
			<xs:enumeration value="OWNERTREE_GOLD" />
			<xs:enumeration value="LEGION_FLAG" />
			<xs:enumeration value="HOUSING_VISITOR_POT_MATERIAL" />
			<xs:enumeration value="HOUSING_CHAIR_WORLD_BM_XMAS" />
			<xs:enumeration value="HOUSING_CHAIR_WORLD_BM_HLWN" />
			<xs:enumeration value="HOUSING_WORLD_CHAIR_BM_ORI" />
			<xs:enumeration value="HOUSING_PETAL_CHAIR" />
			<xs:enumeration value="HOUSING_TABLE_DISH" />
			<xs:enumeration value="HOUSING_TABLE_DISH_WORLD_BM_XMAS" />
			<xs:enumeration value="HOUSING_TABLE_DISH_WORLD_BM_HLWN" />
			<xs:enumeration value="HOUSING_STTABLE_WORLD_BM_XMAS" />
			<xs:enumeration value="HOUSING_STTABLE_WORLD_BM_HLWN" />
			<xs:enumeration value="HOUSING_WORLD_TABLE_DISH_BM_ORI" />
			<xs:enumeration value="HOUSING_WORLD_STTABLE_BM_ORI" />
			<xs:enumeration value="HOUSING_PETAL_TABLE" />
			<xs:enumeration value="HOUSING_OWNER_POT_MATERIAL" />
			<xs:enumeration value="HOUSING_CP" />
			<xs:enumeration value="HOOF" />
			<xs:enumeration value="NIPPER" />
			<xs:enumeration value="PATTERN" />
			<xs:enumeration value="SEAL" />
			<xs:enumeration value="SHACKLES" />
			<xs:enumeration value="SPOROCYST" />
			<xs:enumeration value="WING" />
			<xs:enumeration value="CLOTH" />
			<xs:enumeration value="COIN" />
			<xs:enumeration value="COMPASS" />
			<xs:enumeration value="MIRROR" />
			<xs:enumeration value="ELIM_SKILLSTONE" />
			<xs:enumeration value="KEY" />
			<xs:enumeration value="CASH_FEED" />
			<xs:enumeration value="HANDKERCHIEF" />
			<xs:enumeration value="COMB" />
			<xs:enumeration value="WOOL" />
			<xs:enumeration value="HAIR" />
			<xs:enumeration value="TUNIC" />
			<xs:enumeration value="EVENT_DAYFORDEVA_NVIDIA" />
			<xs:enumeration value="POUPON" />
			<xs:enumeration value="JUNK_BUG" />
			<xs:enumeration value="JUNK_ANIMAL" />
			<xs:enumeration value="JUNK_CRYSTAL" />
			<xs:enumeration value="JUNK_PLANT" />
			<xs:enumeration value="JUNK_HUMANOID" />
			<xs:enumeration value="JUNK_ELEMENTAL" />
			<xs:enumeration value="JUNK_SOUL" />
			<xs:enumeration value="JUNK_REPTILE" />
			<xs:enumeration value="TRUMP_DIAMOND" />
			<xs:enumeration value="TRUMP_SPADE" />
			<xs:enumeration value="WDAY_FLOWER" />
			<xs:enumeration value="WORDCARD_A" />
			<xs:enumeration value="WORDCARD_I" />
			<xs:enumeration value="WORDCARD_O" />
			<xs:enumeration value="WORDCARD_B" />
			<xs:enumeration value="WORDCARD_S" />
			<xs:enumeration value="WORDCARD_D" />
			<xs:enumeration value="WORDCARD_E" />
			<xs:enumeration value="WORDCARD_N" />
			<xs:enumeration value="WORDCARD_Y" />
			<xs:enumeration value="WORDCARD_V" />
			<xs:enumeration value="WORDCARD_PIECE" />
			<xs:enumeration value="WORDCARD_POUCH" />
			<xs:enumeration value="WINTER_OPPORTUNITY" />
			<xs:enumeration value="CASH_AIONJEWELS" />
			<xs:enumeration value="BADGE_DRAGON" />
			<xs:enumeration value="ITEM_GOLD_MEDAL" />
			<xs:enumeration value="FOSSIL" />
			<xs:enumeration value="ODD_CHERRY" />
			<xs:enumeration value="HOLYWATER" />
			<xs:enumeration value="INSTRUMENT" />
			<xs:enumeration value="POLEARMBODY" />
			<xs:enumeration value="SCOOP" />
			<xs:enumeration value="HEADGEAR" />
			<xs:enumeration value="BACKPACK" />
			<xs:enumeration value="POLEARMBLADE" />
			<xs:enumeration value="SKULL" />
			<xs:enumeration value="ACCESSORY" />
			<xs:enumeration value="MOSS" />
			<xs:enumeration value="MAP" />
			<xs:enumeration value="CUP" />
			<xs:enumeration value="MOVE" />
			<xs:enumeration value="BLADE" />
			<xs:enumeration value="HILT" />
			<xs:enumeration value="SPAWN" />
			<xs:enumeration value="SIEGE" />
			<xs:enumeration value="KNIFE" />
			<xs:enumeration value="BEAK" />
			<xs:enumeration value="GOATEE" />
			<xs:enumeration value="MACEBODY" />
			<xs:enumeration value="SEAWEED" />
			<xs:enumeration value="STAFFBODY" />
			<xs:enumeration value="ARTIFACTSTONE" />
			<xs:enumeration value="DRAGEL" />
			<xs:enumeration value="ENHANCED" />
			<xs:enumeration value="HEARBAND" />
			<xs:enumeration value="ORGAN" />
			<xs:enumeration value="MACEHEAD" />
			<xs:enumeration value="TREE" />
			<xs:enumeration value="CACTUS" />
			<xs:enumeration value="WOOD" />
			<xs:enumeration value="ETC" />
			<xs:enumeration value="HEAD" />
			<xs:enumeration value="ORB_EARTH" />
			<xs:enumeration value="ORB_FIRE" />
			<xs:enumeration value="ORB_WATER" />
			<xs:enumeration value="ORB_WIND" />
			<xs:enumeration value="CLOAK" />
			<xs:enumeration value="OLDSEAL" />
			<xs:enumeration value="DRAGONWING" />
			<xs:enumeration value="OLDTORSO" />
			<xs:enumeration value="DARK_WING" />
			<xs:enumeration value="DR_CAPSULE" />
			<xs:enumeration value="SPECIALBOX" />
			<xs:enumeration value="CAGE" />
			<xs:enumeration value="SHOVEL" />
			<xs:enumeration value="IDGEL" />
			<xs:enumeration value="SAW" />
			<xs:enumeration value="TIAMAT_INTEGER" />
			<xs:enumeration value="CASH_SOCKS" />
			<xs:enumeration value="RIBBON" />
			<xs:enumeration value="TIME_TRAVEL_DEVICE" />
			<xs:enumeration value="ID_FRAGMENTS" />
			<xs:enumeration value="EVENT_HEART" />
			<xs:enumeration value="KEY_RED" />
			<xs:enumeration value="KEY_GREEN" />
			<xs:enumeration value="KEY_GOLD" />
			<xs:enumeration value="TRUMP_JOKERKEY" />
			<xs:enumeration value="OLDCROWN" />
			<xs:enumeration value="OLDCUP" />
			<xs:enumeration value="CASH_COIN" />
			<xs:enumeration value="CHINESE" />
			<xs:enumeration value="EVENT_CHRISTMAS_COIN" />
			<xs:enumeration value="EVENT__COIN" />
			<xs:enumeration value="COIN_LEGION" />
			<xs:enumeration value="COIN_TIAMAT" />
			<xs:enumeration value="BLOOD_INTEGER" />
			<xs:enumeration value="TIAMAT_INFINITIVE_SW" />
			<xs:enumeration value="TIAMAT_INFINITIVE_DA" />
			<xs:enumeration value="TIAMAT_INFINITIVE_MA" />
			<xs:enumeration value="TIAMAT_INFINITIVE_OR" />
			<xs:enumeration value="TIAMAT_INFINITIVE_BO" />
			<xs:enumeration value="TIAMAT_INFINITIVE_TS" />
			<xs:enumeration value="TIAMAT_INFINITIVE_PA" />
			<xs:enumeration value="TIAMAT_INFINITIVE_ST" />
			<xs:enumeration value="TIAMAT_INFINITIVE_BW" />
			<xs:enumeration value="TIAMAT_INFINITIVE_SH" />
			<xs:enumeration value="TOKEN" />
			<xs:enumeration value="COIN_PVE" />
			<xs:enumeration value="COIN_SILVERLIN" />
			<xs:enumeration value="COIN_GREENHAT" />
			<xs:enumeration value="DR_TRADE_RECIPE" />
			<xs:enumeration value="COIN_PVP" />
			<xs:enumeration value="TOKEN_NAMED" />
			<xs:enumeration value="TOKEN_FORTRESS" />
			<xs:enumeration value="EVENT_PC_CARE_COIN" />
			<xs:enumeration value="GOLDEN_FEATHER" />
			<xs:enumeration value="FUEL_SIEGE_WEAPON" />
			<xs:enumeration value="SHOT_SIEGE_WEAPON" />
			<xs:enumeration value="EVENT_FOOTPOINT_COIN" />
			<xs:enumeration value="HERLOCK_MARK" />
			<xs:enumeration value="CASH_CHN" />
			<xs:enumeration value="GAB_TIME_CRYSTAL" />
			<xs:enumeration value="GAB_SHOP_CRYSTAL" />
			<xs:enumeration value="GAB_SPECIAL_JUNK_C" />
			<xs:enumeration value="GAB_SPECIAL_JUNK_R" />
			<xs:enumeration value="REVOLUTION_FLAG" />
			<xs:enumeration value="GACHA_COIN" />
			<xs:enumeration value="WINGCASH" />
			<xs:enumeration value="CASH_WING" />
			<xs:enumeration value="TSHIRT" />
			<xs:enumeration value="REPAIRSTONE" />
			<xs:enumeration value="SHOP_PRESET_DEFAULT" />
			<xs:enumeration value="ROCKPACK" />
			<xs:enumeration value="GEMPACK" />
			<xs:enumeration value="ENCHANTPACK" />
			<xs:enumeration value="ORDPACK" />
			<xs:enumeration value="DRAGONPACK" />
			<xs:enumeration value="RANDOMPACK" />
			<xs:enumeration value="CASH_BOX" />
			<xs:enumeration value="CASH_DYE_PACK" />
			<xs:enumeration value="CASH_BASKET" />
			<xs:enumeration value="SHOPPINGBAG" />
			<xs:enumeration value="UNDEFINEABLE" />
			<xs:enumeration value="COCKTAILSHAKER" />
			<xs:enumeration value="_RE" />
			<xs:enumeration value="GOLDEN_WING" />
			<xs:enumeration value="REWARD_BRONZE" />
			<xs:enumeration value="REWARD_SILVER" />
			<xs:enumeration value="REWARD_GOLD" />
			<xs:enumeration value="EVENT_SMALL_RABBITFOOT" />
			<xs:enumeration value="EVENT_BIG_RABBITFOOT" />
			<xs:enumeration value="WDAY_FLOWERBASKET" />
			<xs:enumeration value="WINTER_ONETOFIVE" />
			<xs:enumeration value="WINTER_SIXTONINE" />
			<xs:enumeration value="WINTER_TEN" />
			<xs:enumeration value="WINTER_OVERELEVEN" />
			<xs:enumeration value="WINTER_FIFTEEN" />
			<xs:enumeration value="WINTER_PRISM" />
			<xs:enumeration value="WINTER_CRYSTAL" />
			<xs:enumeration value="COLORED" />
			<xs:enumeration value="GACHA_SACK" />
			<xs:enumeration value="HOUSING_ACHIEVE" />
			<xs:enumeration value="ICEHAMMER" />
			<xs:enumeration value="RABBITFOOT_LEFT_SMALL" />
			<xs:enumeration value="RABBITFOOT_RIGHT_SMALL" />
			<xs:enumeration value="RABBITFOOT_LEFT_BIG" />
			<xs:enumeration value="RABBITFOOT_RIGHT_BIG" />
			<xs:enumeration value="COSMO_BULLET" />
			<xs:enumeration value="WINTER_AURORA" />
			<xs:enumeration value="MATROSKA" />
			<xs:enumeration value="CASH_MOTION_CUSTOMIZE" />
			<xs:enumeration value="CASH_INIT_COOLT" />
			<xs:enumeration value="CASH" />
			<xs:enumeration value="XP_RECOVERY" />
			<xs:enumeration value="CASH_MEGAPHONE" />
			<xs:enumeration value="CASH_EXP_EXTRACTION" />
			<xs:enumeration value="PETADOPTION" />
			<xs:enumeration value="CASH_PETADOPTION" />
			<xs:enumeration value="CASH_GUARDIAN_ANGEL" />
			<xs:enumeration value="CLOUD" />
			<xs:enumeration value="PAGATI" />
			<xs:enumeration value="BIKE" />
			<xs:enumeration value="WHALE" />
			<xs:enumeration value="LEGION_HORSE" />
			<xs:enumeration value="CRESTLICH" />
			<xs:enumeration value="BIKE_SF" />
			<xs:enumeration value="COLOREDCLOUD" />
			<xs:enumeration value="MOBILE_CRESTLICH" />
			<xs:enumeration value="MOBILE_FRILLFAIMAM" />
			<xs:enumeration value="MOBILE_BIKE" />
			<xs:enumeration value="SNOWBOARD" />
			<xs:enumeration value="COLOREDHORSE" />
			<xs:enumeration value="STEAMPUNK" />
			<xs:enumeration value="TRICORIDER" />
			<xs:enumeration value="SPACEHOVER" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="itemType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NORMAL" />
			<xs:enumeration value="ABYSS" />
			<xs:enumeration value="DRACONIC" />
			<xs:enumeration value="DEVANION" />
			<xs:enumeration value="LEGEND" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="itemRace">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ELYOS" />
			<xs:enumeration value="ASMODIANS" />
			<xs:enumeration value="ALL" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="bonusApplyType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="EQUIP" />
			<xs:enumeration value="INVENTORY" />
		</xs:restriction>
	</xs:simpleType>

	<xs:complexType name="ItemBonus">
		<xs:attribute name="type" type="bonusType" />
		<xs:attribute name="bonusLevel" type="xs:string" />
	</xs:complexType>

	<xs:complexType name="ItemFeed">
		<xs:attribute name="type" type="xs:string" />
		<xs:attribute name="level" type="xs:int" use="optional" />
	</xs:complexType>

	<xs:complexType name="ItemConditioning">
		<xs:sequence>
			<xs:element name="stats" type="ConditioningLevel" minOccurs="1"
				maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="level" type="xs:int" />
		<xs:attribute name="burn_attack" type="xs:int" />
		<xs:attribute name="burn_defend" type="xs:int" />
		<xs:attribute name="charge_price1" type="xs:float" />
		<xs:attribute name="charge_price2" type="xs:float" default="0" />
		<xs:attribute name="charge_way" type="xs:int" default="0" />
	</xs:complexType>

	<xs:complexType name="ConditioningLevel">
		<xs:sequence>
			<xs:element name="modifiers" type="Modifiers" minOccurs="1"
				maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="level" type="xs:int" />
	</xs:complexType>

	<xs:complexType name="Robot">
		<xs:attribute name="id" type="xs:int" />
	</xs:complexType>

	<xs:complexType name="ItemIdian">
		<xs:attribute name="set_id" type="xs:int" />
	</xs:complexType>

	<xs:complexType name="CanIdian">
	</xs:complexType>

	<xs:complexType name="ItemEnhance">
		<xs:attribute name="stone_slots" type="xs:int" />
		<xs:attribute name="special_slots" type="xs:int" />
		<xs:attribute name="max_enchant" type="xs:int" />
		<xs:attribute name="max_enchant_bonus" type="xs:int" />
		<xs:attribute name="max_temperance" type="xs:int" />
		<xs:attribute name="random_option" type="xs:int" />
		<xs:attribute name="retune" type="xs:int" />
		<xs:attribute name="can_idian" type="xs:boolean" />
		<xs:attribute name="can_amplify" type="xs:boolean" />
		<xs:attribute name="pre_amplified" type="xs:boolean" />
	</xs:complexType>

	<xs:simpleType name="typeGender">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MALE" />
			<xs:enumeration value="FEMALE" />
			<xs:enumeration value="ALL" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="EAttackType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="PHYSICAL" />
			<xs:enumeration value="MAGICAL_FIRE" />
			<xs:enumeration value="MAGICAL_WATER" />
			<xs:enumeration value="MAGICAL_WIND" />
			<xs:enumeration value="MAGICAL_EARTH" />
		</xs:restriction>
	</xs:simpleType>

</xs:schema>
