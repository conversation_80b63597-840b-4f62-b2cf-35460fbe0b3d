/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import java.util.Map;

import javolution.util.FastMap;
import gameserver.model.ChatType;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_MESSAGE;
import gameserver.services.CommanderChatService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;
import gameserver.world.Executor;

/**
 * 
 * <AUTHOR>
 */
public class Commander extends UserCommand {
    private static Map<Integer, Long> lastExecute = new FastMap<Integer, Long>();

    public Commander() {
        super("commander");
    }

    public void executeCommand(final Player player, String param) {
        if (!CommanderChatService.getInstance().isCommander(player)) {
            PacketSendUtility
                .sendMessage(player, "You do not have the rights to use this command.");
            return;
        }
        else if (param.isEmpty()) {
            PacketSendUtility.sendMessage(player, "Syntax: .commander <message>");
            return;
        }

        if (lastExecute.containsKey(player.getObjectId())) {
            if ((System.currentTimeMillis() - lastExecute.get(player.getObjectId())) < 5000) {
                PacketSendUtility.sendMessage(player,
                    "You cannot use this command more than every 5 seconds!");
                return;
            }
        }

        final SM_MESSAGE pck = new SM_MESSAGE(player.getObjectId(), player.getName(), param,
            ChatType.COMMAND);

        player.getWorldMapInstance().doOnAllPlayers(new Executor<Player>() {
            @Override
            public boolean run(Player pl) {
                if (pl.getCommonData().getRace() == player.getCommonData().getRace()
                    && !pl.getBlockList().contains(player.getObjectId()))
                    PacketSendUtility.sendPacket(pl, pck);

                return true;
            }
        });

        lastExecute.put(player.getObjectId(), System.currentTimeMillis());
    }
}