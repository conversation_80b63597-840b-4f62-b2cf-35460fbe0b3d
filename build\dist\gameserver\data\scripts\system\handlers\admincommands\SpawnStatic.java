/**
 *  This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.spawnengine.SpawnEngine;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;

/**
 * <AUTHOR>
 */
public class SpawnStatic extends AdminCommand {
    public SpawnStatic() {
        super("spawnstatic");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_SPAWN) {
            PacketSendUtility.sendMessage(admin,
                "You don't have enough rights to execute this command");
            return;
        }

        if (params.length < 2) {
            PacketSendUtility.sendMessage(admin, "Syntax: //spawnstatic <npc id> <static id>");
            return;
        }

        float x = admin.getX();
        float y = admin.getY();
        float z = admin.getZ();
        byte h = admin.getHeading();
        int worldId = admin.getWorldId();

        int npcId = Integer.parseInt(params[0]);
        int staticId = Integer.parseInt(params[1]);

        SpawnTemplate sTemplate = SpawnEngine.getInstance().addNewSpawn(worldId, 1, npcId, x, y, z,
            h, 0, 0, true);
        sTemplate.setStaticid(staticId);

        VisibleObject vObject = SpawnEngine.getInstance().spawnObject(sTemplate, 1, true);
        if (vObject == null) {
            PacketSendUtility.sendMessage(admin, "NPC could not be spawned!!!");
            return;
        }

        PacketSendUtility.sendMessage(admin, "NPC with id " + npcId + " and staticId " + staticId
            + " has been spawned!");
    }
}