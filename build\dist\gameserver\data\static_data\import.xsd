<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" version="1.0">
	<xs:element name="import">
		<xs:annotation>
			<xs:documentation><![CDATA[
      Attributes:
		'file' :
				Required attribute.
				Specified path to imported file or directory.
		'skipRoot' :
				Optional attribute.
				Default value: 'false'
				If enabled, then root tags of imported files are ignored.
		'recirsiveImport':
				Optional attribute.
				Default value: 'true'
				If enabled and attribute 'file' points to the directory, then all xml files in that
				directory ( and deeper - recursively ) will be imported, otherwise only files inside that directory (without it subdirectories)
	]]></xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:attribute name="file" type="xs:string" use="required"/>
			<xs:attribute name="skipRoot" type="xs:boolean" use="optional" default="false"/>
			<xs:attribute name="recursiveImport" type="xs:boolean" use="optional" default="true"/>
		</xs:complexType>
	</xs:element>
</xs:schema>
