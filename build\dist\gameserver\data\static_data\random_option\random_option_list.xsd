<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:include schemaLocation="../modifiers.xsd" />
    <xs:include schemaLocation="../import.xsd"/>

    <xs:element name="random_options">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="random_option" type="RandomOptionTemplate" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:complexType name="RandomOptionTemplate">
		<xs:sequence>
			<xs:element name="group" type="RandomGroup" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="id" type="xs:int" use="required"/>
		<xs:attribute name="name" type="xs:string" use="required"/>
	</xs:complexType>

	<xs:complexType name="RandomGroup">
		<xs:sequence>
			<xs:element name="modifiers" type="Modifiers" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="id" type="xs:int" use="required"/>
		<xs:attribute name="prob" type="xs:int" use="required"/>
	</xs:complexType>
</xs:schema>