<?xml version="1.0" encoding="UTF-8"?>
<quest_scripts>
	<!--
       This file is part of Aion X Emu <aionxemu.com>.

	This is free software: you can redistribute it and/or modify
	it under the terms of the GNU Lesser Public License as published by
	the Free Software Foundation, either version 3 of the License, or
	(at your option) any later version.

	This software is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY; without even the implied warranty of
	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
	GNU Lesser Public License for more details.

	You should have received a copy of the GNU Lesser Public License
	along with this software.  If not, see <http://www.gnu.org/licenses/>.
 -->
	<!-- 10020: TODO: Proving Yourself to Outremus -->
	<!-- 10021: TODO: Friends for Life -->
	<!-- 10022: TODO: Support the Inggison Outpost -->
	<!-- 10023: TODO: <PERSON><PERSON>'s Startling Discovery -->
	<!-- 10024: TODO: Will the Aether Rain? -->
	<!-- 10025: TODO: It's Better! It's an Obelisk! -->
	<!-- 10026: TODO: Secret of Inggison -->
	<!-- 10027: TODO: Unable to Pick up in ?? State 2 -->
	<!-- 10028: TODO: Unable to Pick up in ?? State 3 -->
	<!-- 10029: TODO: Unable to Pick up in ?? State 4 -->
	<!-- 10030: TODO: Unable to Pick up in ?? State 5 -->
	<!-- 11000: TODO: Wisplight Chat Tour -->
	<!-- 11001: TODO: Kind Meiria -->
	<!-- 11002: Oppressed! All Right, Ignored -->
	<monster_hunt start_npc_id="798939" id="11002">
		<monster_infos var_id="0" npc_id="215780" max_kill="15"/>
		<monster_infos var_id="0" npc_id="215781" max_kill="15"/>
		<monster_infos var_id="1" npc_id="215496" max_kill="20"/>
		<monster_infos var_id="1" npc_id="215497" max_kill="20"/>
	</monster_hunt>
	<!-- 11003: Maintaining the Illusion -->
	<item_collecting id="11003" start_npc_id="798933" end_npc_id="798942"/>
	<!-- 11004: Alchemical Solution -->
	<item_collecting id="11004" start_npc_id="798933" action_item_id="700609"/>
	<!-- 11005: The Limits of Genius handled by script -->
	<!-- 11006: TODO: Testing the Waters -->
	<!-- 11007: TODO: Finding Lephar -->
	<!-- 11008: Letter Of Encouragement  handled by script -->
	<!-- 11009: TODO: Meiria's Friendly Suggestion -->
	<!-- 11010: TODO: Angel to the Wounded -->
	<!-- 11011: Healing Elixir -->
	<item_collecting id="11011" start_npc_id="799071" action_item_id="700908"/>
	<!-- 11012: TODO: Practical Nursing -->
	<!-- 11013: [Spy/Alliance] Kill Them Twice -->
	<monster_hunt start_npc_id="204588" id="11013">
		<monster_infos var_id="0" npc_id="211136" max_kill="7"/>
		<monster_infos var_id="1" npc_id="211137" max_kill="5"/>
	</monster_hunt>
	<!-- 11014: [Spy/Alliance] A Fool Such As I -->
	<monster_hunt start_npc_id="204507" id="11014">
		<monster_infos var_id="0" npc_id="211046" max_kill="5"/>
	</monster_hunt>
	<!-- 11015: [Manastone Exchange] For Ribbits -->
	<item_collecting id="11015" start_npc_id="798910"/>
	<!-- 11016: [Manastone Exchange] Trading Up -->
	<item_collecting id="11016" start_npc_id="798910"/>
	<!-- 11017: TODO: [Mithril Coin Reward] CoinReward_Warrior -->
	<!-- 11018: TODO: [Mithril Coin Reward] CoinReward_Scout -->
	<!-- 11019: TODO: [Mithril Coin Reward] CoinReward_Mage -->
	<!-- 11020: TODO: [Mithril Coin Reward] CoinReward_Priest -->
	<!-- 11021: TODO: MithrilCoin_GamblingQuest -->
	<!-- 11022: [Coin] For the Scholars -->
	<item_collecting start_npc_id="798914" id="11022"/>
	<!-- 11023: Lapide Preservation -->
	<monster_hunt start_npc_id="798951" id="11023">
		<monster_infos var_id="0" npc_id="215498" max_kill="10"/>
		<monster_infos var_id="0" npc_id="215499" max_kill="10"/>
		<monster_infos var_id="1" npc_id="215490" max_kill="20"/>
		<monster_infos var_id="1" npc_id="215491" max_kill="20"/>
	</monster_hunt>
	<!-- 11024: Talking Crestlich -->
	<item_collecting id="11024" start_npc_id="798950"/>
	<!-- 11025: Do Bocos Tell Bawk-Bawk Jokes? -->
	<item_collecting id="11025" start_npc_id="798950"/>
	<!-- 11026: Solid Evidence handled by script -->
	<!-- 11027: Stelvia's Sanctum Support Structure -->
	<monster_hunt start_npc_id="798951" id="11027">
		<monster_infos var_id="0" npc_id="215517" max_kill="7"/>
		<monster_infos var_id="1" npc_id="215516" max_kill="8"/>
	</monster_hunt>
	<!-- 11028: Fluid Nature of Evolution -->
	<item_collecting id="11028" start_npc_id="798951"/>
	<!-- 11029: Poor, Poor Delus -->
	<monster_hunt start_npc_id="798960" id="11029">
		<monster_infos var_id="0" npc_id="215536" max_kill="20"/>
		<monster_infos var_id="0" npc_id="215537" max_kill="20"/>
		<monster_infos var_id="0" npc_id="216693" max_kill="20"/>
		<monster_infos var_id="0" npc_id="216694" max_kill="20"/>
	</monster_hunt>
	<!-- 11030: Murder The Mosswing -->
	<monster_hunt start_npc_id="798960" id="11030">
		<monster_infos var_id="0" npc_id="216486" max_kill="1"/>
	</monster_hunt>
	<!-- 11031: TODO: Can I Eat It? -->
	<!-- 11032: TODO: Everything's Better With Tentacles -->
	<!-- 11033: TODO: You Make Me Sick -->
	<!-- 11034: Stelvia's Sanctum Support Structure -->
	<monster_hunt start_npc_id="798957" id="11034">
		<monster_infos var_id="0" npc_id="215576" max_kill="12"/>
		<monster_infos var_id="1" npc_id="215528" max_kill="10"/>
	</monster_hunt>
	<!-- 11035: A Pack Of Plumas -->
	<monster_hunt start_npc_id="798955" id="11035">
		<monster_infos var_id="0" npc_id="215530" max_kill="15"/>
		<monster_infos var_id="1" npc_id="216487" max_kill="1"/>
	</monster_hunt>
	<!-- 11036: TODO: Uncommon Recipe -->
	<!-- 11037: An Amoral Meal -->
	<item_collecting id="11037" start_npc_id="798956"/>
	<!-- 11038: [Group] Squip's Lamentation -->
	<monster_hunt start_npc_id="799035" id="11038">
		<monster_infos var_id="0" npc_id="215550" max_kill="13"/>
		<monster_infos var_id="1" npc_id="215562" max_kill="10"/>
	</monster_hunt>
	<!-- 11039: [Group] Undying Sorrow -->
	<item_collecting start_npc_id="799035" id="11039"/>
	<!-- 11040: TODO: Squamp on the Cooking Plate -->
	<!-- 11041: Marica's Collection -->
	<item_collecting start_npc_id="798955" id="11041"/>
	<!-- 11042: [Coin/Group] Example: Shulacks -->
	<item_collecting id="11042" start_npc_id="798957"/>
	<!-- 11043: TODO: Core of the Problem -->
	<!-- 11044: Pompous Adias -->
	<item_collecting id="11044" start_npc_id="798953"/>
	<!-- 11045: Crosia's Circumstances -->
	<monster_hunt start_npc_id="798952" id="11045">
		<monster_infos var_id="0" npc_id="215525" max_kill="22"/>
	</monster_hunt>
	<!-- 11046: TODO: That Pretty Box -->
	<!-- 11047: [Coin] What's Up, Brohum? -->
	<item_collecting id="11047" start_npc_id="798952"/>
	<!-- 11048: [Coin] Taloc Rebirth Operation -->
	<monster_hunt start_npc_id="798954" id="11048">
		<monster_infos var_id="0" npc_id="216139" max_kill="329"/>
		<monster_infos var_id="0" npc_id="215464" max_kill="231"/>
		<monster_infos var_id="1" npc_id="215454" max_kill="77"/>
	</monster_hunt>
	<!-- 11050: TODO: Inggison Coin Fountain -->
	<!-- 11056: TODO: [Spy/Alliance] Elimination Order -->
	<!-- 11057: TODO: [Spy/Alliance] Stanis's Secret Order -->
	<!-- 11058: TODO: [Spy/Alliance] Temenos's Secret Order -->
	<!-- 11059: TODO: Omega's Fragment -->
	<!-- 11060: TODO: [League] The Orb's Orders -->
	<!-- 11061: TODO: [Spy/League] Twilight of Ragnarok -->
	<!-- 11062: TODO: [Spy/League] Expedition to Gelkmaros -->
	<!-- 11063: TODO: [Spy/League] Quell Mastarius -->
	<!-- 11064: [Spy] Stanis's Worry -->
	<item_collecting id="11064" start_npc_id="799043"/>
	<!-- 11065: [PvP] Mudthorn Of Viscum Swamp -->
	<item_collecting id="11065" start_npc_id="799043"/>
	<!-- 11066: [PvP] A Shield Situation -->
	<item_collecting id="11066" start_npc_id="799049"/>
	<!-- 11067: [PvP] Temenos's Request -->
	<item_collecting id="11067" start_npc_id="799049"/>
	<!-- 11068: TODO: A Mysterious Wind -->
	<!-- 11069: TODO: Mookie Travel Tips -->
	<!-- 11070: TODO: Crafty Messenger -->
	<!-- 11071: Fish With Feet -->
	<monster_hunt start_npc_id="798943" id="11071">
		<monster_infos var_id="0" npc_id="215532" max_kill="25"/>
	</monster_hunt>
	<!-- 11072: TODO: Delus, Culinary Victim -->
	<!-- 11073: Night Hunter -->
	<monster_hunt start_npc_id="798936" id="11073">
		<monster_infos var_id="0" npc_id="215534" max_kill="11"/>
		<monster_infos var_id="1" npc_id="215528" max_kill="20"/>
	</monster_hunt>
	<!-- 11074: Ride The Wind -->
	<report_to start_npc_id="798938" end_npc_id="799025" id="11074"/>
	<!-- 11075: The Netherworld Breathes -->
	<report_to start_npc_id="798939" end_npc_id="799067" id="11075"/>
	<!-- 11100: Allergic To What? -->
	<monster_hunt start_npc_id="798983" id="11100">
		<monster_infos var_id="0" npc_id="215593" max_kill="20"/>
	</monster_hunt>
	<!-- 11101: Dragon's Breath -->
	<item_collecting id="11101" start_npc_id="798981"/>
	<!-- 11102: Petrified Sampling -->
	<monster_hunt start_npc_id="798981" id="11102">
		<monster_infos var_id="0" npc_id="216489" max_kill="1"/>
		<monster_infos var_id="1" npc_id="216490" max_kill="1"/>
		<monster_infos var_id="2" npc_id="216491" max_kill="1"/>
	</monster_hunt>
	<!-- 11103: Finite for a Walk -->
	<item_collecting id="11103" start_npc_id="798933" end_npc_id="798942"/>
	<!-- 11104: Savinus's Torn Sack -->
	<item_collecting start_npc_id="798978" id="11104"/>
	<!-- 11105: TODO: A Jewel to Quiet Her -->
	<!-- 11106: TODO: Rewriting History -->
	<!-- 11107: TODO: Comfort is a Box -->
	<!-- 11108: Levilus And His Leather -->
	<item_collecting id="11108" start_npc_id="798965"/>
	<!-- 11109: TODO: The Negotiators -->
	<!-- 11110: Killing Time -->
	<monster_hunt start_npc_id="799075" id="11110">
		<monster_infos var_id="0" npc_id="217039" max_kill="10"/>
		<monster_infos var_id="0" npc_id="217040" max_kill="10"/>
	</monster_hunt>
	<!-- 11111: Kill the Corrupted Sage -->
	<item_collecting start_npc_id="799075" id="11111"/>
	<!-- 11112: There's Gold in That Thar Valley -->
	<item_collecting start_npc_id="798984" action_item_id="700615" id="11112"/>
	<!-- 11113: Not For You! -->
	<item_collecting id="11113" start_npc_id="798984"/>
	<!-- 11114: Pixiebelle's Gold -->
	<item_collecting id="11114" start_npc_id="798984"/>
	<!-- 11115: In Our Hour Of Need -->
	<item_collecting id="11115" start_npc_id="798985"/>
	<!-- 11116: TODO: Munching Mookie Pickles -->
	<!-- 11117: TODO: Medication for Setzkiki -->
	<!-- 11118: TODO: Making Setzkiki Laugh -->
	<!-- 11119: Menacing Creature -->
	<monster_hunt start_npc_id="798989" id="11119">
		<monster_infos var_id="0" npc_id="215658" max_kill="15"/>
		<monster_infos var_id="1" npc_id="215652" max_kill="13"/>
	</monster_hunt>
	<!-- 11120: Enter The Dragonbound -->
	<monster_hunt start_npc_id="798991" id="11120">
		<monster_infos var_id="0" npc_id="215642" max_kill="7"/>
		<monster_infos var_id="0" npc_id="215643" max_kill="7"/>
		<monster_infos var_id="1" npc_id="215644" max_kill="7"/>
		<monster_infos var_id="1" npc_id="215645" max_kill="7"/>
		<monster_infos var_id="2" npc_id="215648" max_kill="7"/>
		<monster_infos var_id="2" npc_id="215649" max_kill="7"/>
	</monster_hunt>
	<!-- 11121: [Group] Lost Symbols -->
	<item_collecting start_npc_id="798991" id="11121"/>
	<!-- 11122: [Group] A Hard Reian's Gonna Fall -->
	<monster_hunt start_npc_id="798990" id="11122">
		<monster_infos var_id="0" npc_id="216495" max_kill="1"/>
		<monster_infos var_id="1" npc_id="216496" max_kill="1"/>
	</monster_hunt>
	<!-- 11123: TODO: Suspicious Book -->
	<!-- 11124: Auralia's Been Angry -->
	<monster_hunt start_npc_id="798999" id="11124">
		<monster_infos var_id="0" npc_id="215716" max_kill="12"/>
		<monster_infos var_id="1" npc_id="215718" max_kill="8"/>
	</monster_hunt>
	<!-- 11125: Leaking Drana -->
	<item_collecting id="11125" start_npc_id="798996"/>
	<!-- 11126: A Worthy Adversary -->
	<monster_hunt start_npc_id="798998" id="11126">
		<monster_infos var_id="0" npc_id="215694" max_kill="17"/>
		<monster_infos var_id="1" npc_id="215696" max_kill="15"/>
	</monster_hunt>
	<!-- 11127: Perfect Killing Machines -->
	<item_collecting id="11127" start_npc_id="798998"/>
	<!-- 11128: Dead In Three Breaths -->
	<item_collecting id="11128" start_npc_id="798998"/>
	<!-- 11129: TODO: Blessing to the Agent -->
	<!-- 11130: [Coin] Tiamat's Mark -->
	<item_collecting id="11130" start_npc_id="798996"/>
	<!-- 11131: TODO: Blessing to the Agent -->
	<!-- 11132: TODO: Blessing to the Agent -->
	<!-- 11133: TODO: Blessing to the Agent -->
	<!-- 11134: TODO: Blessing to the Agent -->
	<!-- 11135: TODO: Blessing to the Agent -->
	<!-- 11136: TODO: Blessing to the Agent -->
	<!-- 11137: TODO: Blessing to the Agent -->
	<!-- 11138: Death Comes With A Smile -->
	<monster_hunt start_npc_id="798981" id="11138">
		<monster_infos var_id="0" npc_id="215597" max_kill="15"/>
	</monster_hunt>
	<!-- 11139: TODO: The Bad News -->
	<!-- 11140: Me Jewels Be Stoled! -->
	<monster_hunt start_npc_id="799076" id="11140">
		<monster_infos var_id="0" npc_id="215599" max_kill="20"/>
	</monster_hunt>
	<!-- 11141: A Mother's Revenge -->
	<monster_hunt start_npc_id="798985" id="11141">
		<monster_infos var_id="0" npc_id="216473" max_kill="12"/>
	</monster_hunt>
	<!-- 11142: Warm Love -->
	<item_collecting id="11142" start_npc_id="798986"/>
	<!-- 11143: TODO: Baby Shulack's Journey -->
	<!-- 11144: Sueros's Headache -->
	<monster_hunt start_npc_id="799030" id="11144">
		<monster_infos var_id="0" npc_id="215656" max_kill="10"/>
		<monster_infos var_id="1" npc_id="215654" max_kill="7"/>
	</monster_hunt>
	<!-- 11145: Undirborg Enterprise -->
	<monster_hunt start_npc_id="799030" id="11145">
		<monster_infos var_id="0" npc_id="215676" max_kill="10"/>
		<monster_infos var_id="1" npc_id="215686" max_kill="10"/>
	</monster_hunt>
	<!-- 11146: What Rollia Likes -->
	<item_collecting id="11146" start_npc_id="798997"/>
	<!-- 11147: TODO: Cute Beady Eyes? -->
	<!-- 11148: Local Delicacies -->
	<item_collecting id="11148" start_npc_id="798993"/>
	<!-- 11149: TODO: The Lady's Layout -->
	<!-- 11200: [Alliance] An Urgent Mission -->
	<monster_hunt start_npc_id="799007" end_npc_id="799005" id="11200">
		<monster_infos var_id="0" npc_id="257065" max_kill="20"/>
	</monster_hunt>
	<!-- 11201: TODO: [League] Securing Avarice -->
	<!-- 11202: A Safer Crater -->
	<monster_hunt start_npc_id="799009" id="11202">
		<monster_infos var_id="0" npc_id="215730" max_kill="10"/>
		<monster_infos var_id="1" npc_id="215733" max_kill="12"/>
	</monster_hunt>
	<!-- 11203: Essence Of Sulfur -->
	<item_collecting id="11203" start_npc_id="799008"/>
	<!-- 11204: TODO: For the Crater Good -->
	<!-- 11205: Protect What's Ours -->
	<item_collecting id="11205" start_npc_id="799004"/>
	<!-- Acidproof Armor -->
	<item_collecting id="11206" start_npc_id="799005"/>
	<!-- 11207: TODO: [Spy] Balaur Bind Point 1 (Infiltration 1) -->
	<!-- 11208: TODO: [Spy/Alliance] Balaur Bind Point 1 (Infiltration 2) -->
	<!-- 11209: TODO: [Spy/Alliance] Balaur Bind Point 1 (Infiltration 3) -->
	<!-- 11210: [Group] The Dramata's Long Shadow -->
	<monster_hunt start_npc_id="799010" id="11210">
		<monster_infos var_id="0" npc_id="215746" max_kill="10"/>
		<monster_infos var_id="1" npc_id="215752" max_kill="3"/>
	</monster_hunt>
	<!-- 11211: TODO: Chaser's Backpack -->
	<!-- 11212: TODO: Balaur Records -->
	<!-- 11213: Damn Tree Creatures -->
	<monster_hunt start_npc_id="799019" id="11213">
		<monster_infos var_id="0" npc_id="215758" max_kill="20"/>
		<monster_infos var_id="1" npc_id="215870" max_kill="10"/>
	</monster_hunt>
	<!-- 11214: [Alliance] Moras's Cool Judgment -->
	<monster_hunt start_npc_id="799018" end_npc_id="799015" id="11214">
		<monster_infos var_id="0" npc_id="257065" max_kill="20"/>
	</monster_hunt>
	<!-- 11215: TODO: [League] Reclaim Temple of Scales -->
	<!-- 11216: TODO: How Many Draks Does It Take To Map? -->
	<!-- 11217: [Group] Seat of Civilization? -->
	<monster_hunt start_npc_id="799017" id="11217">
		<monster_infos var_id="0" npc_id="215768" max_kill="20"/>
		<monster_infos var_id="1" npc_id="215773" max_kill="10"/>
	</monster_hunt>
	<!-- 11218: [Group] Drakanstaffs at Dawn -->
	<item_collecting id="11218" start_npc_id="799017"/>
	<!-- 11219: [Group] A Stealthy Science -->
	<item_collecting id="11219" start_npc_id="799017"/>
	<!-- 11220: Making Do -->
	<item_collecting id="11220" start_npc_id="799016"/>
	<!-- 11221: Living In Obscura Ty -->
	<item_collecting id="11221" start_npc_id="799015"/>
	<!-- 11222: Watching The Past -->
	<monster_hunt start_npc_id="257225" id="11222">
		<monster_infos var_id="0" npc_id="215873" max_kill="10"/>
		<monster_infos var_id="1" npc_id="215872" max_kill="15"/>
	</monster_hunt>
	<!-- 11223: Just A Taste -->
	<item_collecting id="11223" start_npc_id="799000"/>
	<!-- 11224: Swords And Spectacles -->
	<monster_hunt start_npc_id="799019" id="11224">
		<monster_infos var_id="0" npc_id="216593" max_kill="7"/>
		<monster_infos var_id="1" npc_id="216594" max_kill="7"/>
	</monster_hunt>
	<!-- 11225: [Group] Shadows on the Road -->
	<monster_hunt start_npc_id="799006" id="11225">
		<monster_infos var_id="0" npc_id="215871" max_kill="10"/>
	</monster_hunt>
	<!-- 11226: Pudding it Together -->
	<item_collecting id="11226" start_npc_id="798971"/>
	<!-- 11227: Easy As 4,3,2,1 (to see if it has not do .java)-->
	<monster_hunt start_npc_id="799076" id="11227">
		<monster_infos var_id="0" npc_id="217071" max_kill="1"/>
		<monster_infos var_id="0" npc_id="217070" max_kill="1"/>
		<monster_infos var_id="1" npc_id="217069" max_kill="1"/>
		<monster_infos var_id="1" npc_id="217068" max_kill="1"/>
	</monster_hunt>
	<!-- 11228: TODO: No, He Never Returned -->
	<!-- 11229: Allergic Reaction (to see if it has not do .java)-->
	<item_collecting id="11229" start_npc_id="296491"/>
	<!-- 11230: Reian History -->
	<item_collecting id="11230" start_npc_id="799077"/>
	<!-- 11231: Reian Present -->
	<item_collecting id="11231" start_npc_id="799077"/>
	<!-- 11232: Reian Future -->
	<item_collecting id="11232" start_npc_id="799077"/>
	<!-- 11233: TODO: Suleion's Treasure -->
	<!-- 11250: TODO: [Spy/League] Price of Agency -->
	<!-- 11251: Greatsword Predilection -->
	<item_collecting start_npc_id="799038" end_npc_id="798316" id="11251"/>
	<!-- 11252: TODO: [Spy/League] Veilled Reward -->
	<!-- 11253: Spellbook Predilection -->
	<item_collecting start_npc_id="799038" end_npc_id="798316" id="11253"/>
	<!-- 11254: TODO: [Spy/League] Agency's Prize -->
	<!-- 11255: Bow Predilection -->
	<item_collecting start_npc_id="799038" end_npc_id="798316" id="11255"/>
	<!-- 11256: TODO: [Spy/League] Veilled Compliment -->
	<!-- 11257: Dagger Predilection -->
	<item_collecting start_npc_id="799038" end_npc_id="798316" id="11257"/>
	<!-- 11258: TODO: [Spy/League] Agent's Operative -->
	<!-- 11259: Mace Predilection -->
	<item_collecting start_npc_id="799038" end_npc_id="798316" id="11259"/>
	<!-- 11260: TODO: [Spy/League] Agent's Agent -->
	<!-- 11261: Orb Predilection -->
	<item_collecting start_npc_id="799038" end_npc_id="798316" id="11261"/>
	<!-- 11262: TODO: [Spy/League] Agency's Effect -->
	<!-- 11263: Polearm Predilection -->
	<item_collecting start_npc_id="799038" end_npc_id="798316" id="11263"/>
	<!-- 11264: TODO: [Spy/League] Agency's Toll -->
	<!-- 11265: Staff Predilection -->
	<item_collecting start_npc_id="799038" end_npc_id="798316" id="11265"/>
	<!-- 11266: TODO: [Spy/League] Agency's Aftermath -->
	<!-- 11267: Sword Predilection -->
	<item_collecting start_npc_id="799038" end_npc_id="798316" id="11267"/>
	<!-- 11268: Winning Veille's Favor -->
	<item_collecting start_npc_id="798958" id="11268"/>
	<!-- 11269: Favors for Veille -->
	<item_collecting start_npc_id="798958" id="11269"/>
	<!-- 11270: Veille's Favor-ite -->
	<item_collecting start_npc_id="798958" id="11270"/>
	<!-- 11271: Favor Feud -->
	<item_collecting start_npc_id="798958" id="11271"/>
	<!-- 11272: Favors of Fancy -->
	<item_collecting start_npc_id="798958" id="11272"/>
	<!-- 11273: Veilled Weapon -->
	<item_collecting start_npc_id="798958" id="11273"/>
	<!-- 11274: Shield Veille -->
	<item_collecting start_npc_id="798958" id="11274"/>
	<!-- 11450: Many Nibbles in a Bite -->
	<item_collecting start_npc_id="798952" action_item_id="700634" id="11450"/>
	<!-- 11451: For What Ails You -->
	<report_to start_npc_id="798952" end_npc_id="798933" id="11451"/>
	<!-- 11452: Last Part Of The Cure -->
	<item_collecting id="11452" start_npc_id="798933"/>
	<!-- 11453: Fruit of My Labor -->
	<report_to start_npc_id="798933" end_npc_id="799070" id="11453"/>
	<!-- 11455: When the Time is Ripe -->
	<!-- 11456: The Exterminator -->
	<monster_hunt start_npc_id="798954" id="11456">
		<monster_infos var_id="0" npc_id="216139" max_kill="100"/>
	</monster_hunt>
	<!-- 11457: Wood-n't You Know It? -->
	<item_collecting start_npc_id="798953" action_item_id="700635" id="11457"/>
	<!-- 11458: TODO: Adias's Report -->
	<!-- 11459: The Balance -->
	<monster_hunt start_npc_id="798952" id="11459">
		<monster_infos var_id="0" npc_id="215464" max_kill="40"/>
	</monster_hunt>
	<!-- 11460: TODO: The Shulack of Taloc -->
	<!-- 11461: What Book? -->
	<item_collecting start_npc_id="799502" end_npc_id="798955" action_item_id="700636" id="11461"/>
	<!-- 30001: Building Trust -->
	<report_to start_npc_id="798927" end_npc_id="799029" id="30001"/>
	<!-- 30012: Axe a Favor -->
	<report_to start_npc_id="799032" end_npc_id="799030" id="30012"/>
	<!-- 30015: Sword of the Reians -->
	<report_to start_npc_id="798903" end_npc_id="799032" id="30015"/>
	<!-- 30016: Noble Siel's Supreme Sword -->
	<item_collecting start_npc_id="799032" id="30016"/>
	<!-- 30017: Greatsword of the Reians -->
	<report_to start_npc_id="798903" end_npc_id="799032" id="30018"/>
	<!-- 30018: Noble Siel's Supreme Greatsword -->
	<!-- 30019: Polearm of the Reians -->
	<report_to start_npc_id="798903" end_npc_id="799032" id="30019"/>
	<!-- 30020:  Noble Siel's Supreme Polearm -->
	<item_collecting start_npc_id="799032" id="30020"/>
	<!-- 30021: Staff of the Reians -->
	<report_to start_npc_id="798903" end_npc_id="799032" id="30021"/>
	<!-- 30022: Noble Siel's Supreme Staff -->
	<item_collecting start_npc_id="799032" id="30022"/>
	<!-- 30023: Spellbook of the Reians -->
	<report_to start_npc_id="798903" end_npc_id="799032" id="30023"/>
	<!-- 30024: Noble Siel's Supreme Spellbook -->
	<item_collecting start_npc_id="799032" id="30024"/>
	<!-- 30025: Orb of the Reians -->
	<report_to start_npc_id="798903" end_npc_id="799032" id="30025"/>
	<!-- 30026: Noble Siel's Supreme Orb -->
	<item_collecting start_npc_id="799032" id="30026"/>
	<!-- 30027: Bow of the Reians -->
	<report_to start_npc_id="798903" end_npc_id="799032" id="30027"/>
	<!-- 30028: Noble Siel's Supreme Bow -->
	<item_collecting start_npc_id="799032" id="30028"/>
	<!-- 30029: Dagger of the Reians -->
	<report_to start_npc_id="798903" end_npc_id="799032" id="30029"/>
	<!-- 30030: Noble Siel's Supreme Dagger -->
	<item_collecting start_npc_id="799032" id="30030"/>
	<!-- 30031: Mace of the Reians -->
	<report_to start_npc_id="798903" end_npc_id="799032" id="30031"/>
	<!-- 30032: Noble Siel's Supreme Mace -->
	<item_collecting start_npc_id="799032" id="30032"/>
	<!-- 38502: Sake Of Beauty -->
	<item_collecting id="38502" start_npc_id="799085"/>
	<!-- 38503: High Flying Shugo -->
	<item_collecting id="38503" start_npc_id="799086"/>
	<!-- 38505: Sake Of Balance -->
	<item_collecting id="38505" start_npc_id="799085"/>
	<!-- 38506: Token Shugo -->
	<item_collecting id="38506" start_npc_id="799086"/>
</quest_scripts>
