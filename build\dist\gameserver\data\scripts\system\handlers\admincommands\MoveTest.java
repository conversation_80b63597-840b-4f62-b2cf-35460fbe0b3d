/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.controllers.movement.MovementType;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_MOVE;
import gameserver.network.aion.serverpackets.SM_POSITION_CORRECTION;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.World;

/**
 * 
 * <AUTHOR>
 */
public class MoveTest extends AdminCommand {
    public MoveTest() {
        super("move");
    }

    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_MOVETO) {
            PacketSendUtility.sendMessage(admin,
                "You don't have enough rights to execute this command");
            return;
        }

        if (params.length < 3 && params.length != 1) {
            PacketSendUtility.sendMessage(admin, "Syntax: //move <offset x> <offset y> <offset z>"
                + "\nOR Syntax: //move <offset z>");
            return;
        }

        float x = admin.getX(), y = admin.getY(), z = admin.getZ();

        if (params.length == 1) {
            z += Float.parseFloat(params[0]);
        }
        else {
            x += Float.parseFloat(params[0]);
            y += Float.parseFloat(params[1]);
            z += Float.parseFloat(params[2]);
        }

        admin.getMoveController().stop();

        World.getInstance().updatePosition(admin, x, y, z, admin.getHeading());
        admin.getMoveController().setPosition(x, y, z);

        SM_MOVE movePck = new SM_MOVE(admin, x, y, z, admin.getHeading(),
            MovementType.MOVEMENT_STOP);

        PacketSendUtility.sendPacket(admin, new SM_POSITION_CORRECTION(admin));
        PacketSendUtility.broadcastPacket(admin, movePck);
        admin.getController().onStopMove();
        // admin.getFlyController().onStopGliding();
    }
}