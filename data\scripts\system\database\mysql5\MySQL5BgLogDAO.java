/*
 * This file is part of aion-emu <aion-emu.com>.
 *
 *  aion-emu is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  aion-emu is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with aion-emu.  If not, see <http://www.gnu.org/licenses/>.
 */
package mysql5;

import gameserver.dao.BgLogDAO;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.LadderService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.DB;
import com.aionemu.commons.database.DatabaseFactory;

/**
 * <AUTHOR>
 * 
 */
public class MySQL5BgLogDAO extends BgLogDAO {
    private static final Logger log = Logger.getLogger(MySQL5BgLogDAO.class);

    @Override
    public int insertBg(String name) {
        Connection con = null;
        PreparedStatement ps = null;

        int bgId = 0;

        try {
            con = DatabaseFactory.getConnection();
            ps = con.prepareStatement("INSERT INTO bg_log (name,date) VALUES (?,NOW())",
                Statement.RETURN_GENERATED_KEYS);

            ps.setString(1, name);

            ps.executeUpdate();

            ResultSet rs = ps.getGeneratedKeys();

            if (rs.next())
                bgId = rs.getInt(1);
        }
        catch (Exception e) {
            log.error("Error inserting BG log", e);
        }
        finally {
            DatabaseFactory.close(con);
        }

        return bgId;
    }

    @Override
    public boolean addPlayerToBg(int bgId, Player player) {
        PreparedStatement ps = DB
            .prepareStatement("INSERT INTO bg_players (bg_id,player_id,player_class,player_rating,premade_id,team_id) VALUES (?,?,?,?,?,?)");

        int bgIndex = player.getBgIndex() + 1;

        if (player.isInAlliance())
            bgIndex = player.getPlayerAlliance().getBgIndex() + 1;
        else if (player.isInGroup())
            bgIndex = player.getPlayerGroup().getBgIndex() + 1;

        try {
            ps.setInt(1, bgId);
            ps.setInt(2, player.getObjectId());
            ps.setString(3, player.getPlayerClass().name());
            ps.setInt(4, LadderService.getInstance().getCachedRating(player));
            ps.setInt(5, LadderService.getInstance().getPremadeId(player));
            ps.setInt(6, bgIndex);

            ps.executeUpdate();
        }
        catch (Exception e) {
            log.error("Error inserting BG player log", e);
            return false;
        }
        finally {
            DB.close(ps);
        }

        return true;
    }

    @Override
    public boolean addPremadeToBg(int bgId, int premadeId, int size) {
        PreparedStatement ps = DB
            .prepareStatement("INSERT IGNORE INTO bg_premades (premade_id,bg_id,size) VALUES (?,?,?)");

        try {
            ps.setInt(1, premadeId);
            ps.setInt(2, bgId);
            ps.setInt(3, size);

            ps.executeUpdate();
        }
        catch (Exception e) {
            log.error("Error inserting BG premade log", e);
            return false;
        }
        finally {
            DB.close(ps);
        }

        return true;
    }

    @Override
    public boolean updateWinner(int bgId, int winnerId) {
        PreparedStatement ps = DB.prepareStatement("UPDATE bg_log SET winner = ? WHERE id = ?");

        try {
            ps.setInt(1, winnerId);
            ps.setInt(2, bgId);

            ps.executeUpdate();
        }
        catch (Exception e) {
            log.error("Error inserting updating BG winner log", e);
            return false;
        }
        finally {
            DB.close(ps);
        }

        return true;
    }

    @Override
    public boolean updateLeaver(int bgId, int playerId, boolean left) {
        PreparedStatement ps = DB.prepareStatement("UPDATE bg_log_players SET left_bg = ? WHERE bg_id = ? AND player_id = ?");

        try {
            ps.setBoolean(1, left);
            ps.setInt(2, bgId);
            ps.setInt(3, playerId);

            ps.executeUpdate();
        }
        catch (Exception e) {
            log.error("Error updating BG leaver log", e);
            return false;
        }
        finally {
            DB.close(ps);
        }

        return true;
    }

    @Override
    public boolean supports(String databaseName, int majorVersion, int minorVersion) {
        return MySQL5DAOUtils.supports(databaseName, majorVersion, minorVersion);
    }
}
