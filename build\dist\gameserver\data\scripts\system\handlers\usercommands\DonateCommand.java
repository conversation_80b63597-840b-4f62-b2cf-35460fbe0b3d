/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.configs.main.CustomConfig;
import gameserver.configs.network.NetworkConfig;
import gameserver.dao.MightDAO;
import gameserver.dao.PlayerDailiesDAO;
import gameserver.dao.ShopDAO;
import gameserver.dao.ShopDAO.DonationEntry;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.GloryService;
import gameserver.services.GloryService.PvPMode;
import gameserver.services.HTMLService;
import gameserver.services.PremiumService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.UserCommand;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

import javolution.util.FastMap;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * 
 * <AUTHOR>
 */
public class DonateCommand extends UserCommand {
    private static Map<Integer, Long> lastExecute = new FastMap<Integer, Long>();

    private static final int MIGHT_TO_AP_RATIO = 100;
    private static final int AP_CEILING = 1000000;

    private static final int MIGHT_TO_GP_RATIO = 1;
    private static final int GP_CEILING = 20000;

    private static final int RESET_JOTUN_COST = 250;

    public DonateCommand() {
        super("donate");
    }

    public void executeCommand(Player player, String param) {
        String[] params = param.split(" ");

        if (lastExecute.containsKey(player.getObjectId())) {
            if ((System.currentTimeMillis() - lastExecute.get(player.getObjectId())) < 2000) {
                message(player, "You cannot use this command more than every 2 seconds!");
                return;
            }
        }

        if (params[0].equalsIgnoreCase("history")) {
            List<DonationEntry> donations = DAOManager.getDAO(ShopDAO.class).getDonationEntries(
                player);

            if (donations == null || donations.size() == 0) {
                message(player, "No donation records of your account were found!");
            }
            else {
                float total = DAOManager.getDAO(ShopDAO.class).getDonationTotal(player);

                String msg = "--- DONATION HISTORY ---\n";
                msg += "Total: " + total + " euros\n\n";

                DateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");

                for (DonationEntry donation : donations) {
                    msg += "[" + formatter.format(donation.getTimeStamp()) + "] => "
                        + donation.getAmount() / (NetworkConfig.GAMESERVER_ID == 24 ? 200 : 100)
                        + " Euro\n";
                }

                msg += "--- END OF HISTORY ---";

                message(player, msg);
            }
        }
        else if (params[0].equalsIgnoreCase("might2ap")) {
            if (params.length < 2) {
                message(player, "Syntax: .donate might2ap <amount of might> -- you will get "
                    + MIGHT_TO_AP_RATIO + " AP per might");
                return;
            }

            if (!PremiumService.canMight2Ap(player)) {
                message(player, "Error! To use this command you must have donated at least "
                    + PremiumService.DONATION_MIGHT2AP_THRESHOLD + " euros in total.");
            }
            else {
                if (player.getAbyssRank().getAp() > AP_CEILING) {
                    message(player,
                        "You have more AP than this command can give you. The ceiling is "
                            + AP_CEILING + "!");
                    return;
                }

                int might;
                try {
                    might = Integer.parseInt(params[1]);
                }
                catch (NumberFormatException e) {
                    message(player,
                        "Error! Please type the number of might you want to convert to AP.");
                    return;
                }

                if (might > DAOManager.getDAO(MightDAO.class).getMight(player)) {
                    message(player,
                        "Error! You cannot convert more might than you have. Please choose a lower number.");
                    return;
                }

                int ap = might * MIGHT_TO_AP_RATIO;
                if (player.getAbyssRank().getAp() + ap > AP_CEILING) {
                    ap = AP_CEILING - player.getAbyssRank().getAp();
                    might = ap / MIGHT_TO_AP_RATIO;
                }

                player.getCommonData().addAp(ap);
                DAOManager.getDAO(MightDAO.class).addMight(player, -might);
                DAOManager.getDAO(ShopDAO.class).logPurchase(player, -4, ap, might);

                message(player, "You have spent " + might + " might and gained " + might
                    * MIGHT_TO_AP_RATIO + " AP!");
            }
        }
        else if (params[0].equalsIgnoreCase("might2gp")) {
            if (params.length < 2) {
                message(player, "Syntax: .donate might2gp <amount of might> -- you will get "
                    + MIGHT_TO_GP_RATIO + " GP per might"
                    + "\nNOTE: You cannot use this command to go higher than " + GP_CEILING
                    + " Glory Points.");
                return;
            }

            if (!PremiumService.canMight2Ap(player)) {
                message(player, "Error! To use this command you must have donated at least "
                    + PremiumService.DONATION_MIGHT2AP_THRESHOLD + " euros in total.");
            }
            else {
                if (CustomConfig.OLD_SCHOOL) {
                    message(player, "This command is not available on the Old School server.");
                    return;
                }

                if (player.getAbyssRank().getGlory() > GP_CEILING) {
                    message(player,
                        "You have more GP than this command can give you. The ceiling is "
                            + GP_CEILING + "!");
                    return;
                }

                int might;
                try {
                    might = Integer.parseInt(params[1]);
                }
                catch (NumberFormatException e) {
                    message(player,
                        "Error! Please type the number of might you want to convert to GP.");
                    return;
                }

                if (might > DAOManager.getDAO(MightDAO.class).getMight(player)) {
                    message(player,
                        "Error! You cannot convert more might than you have. Please choose a lower number.");
                    return;
                }

                int gp = might * MIGHT_TO_GP_RATIO;
                if (player.getAbyssRank().getGlory() + gp > GP_CEILING) {
                    gp = GP_CEILING - player.getAbyssRank().getGlory();
                    might = gp / MIGHT_TO_GP_RATIO;
                }

                player.getCommonData().addGlory(gp);
                DAOManager.getDAO(MightDAO.class).addMight(player, -might);
                DAOManager.getDAO(ShopDAO.class).logPurchase(player, -6, gp, might);

                message(player, "You have spent " + might + " might and gained " + might
                    * MIGHT_TO_GP_RATIO + " GP!");
            }
        }
        else if (params[0].equalsIgnoreCase("resetjotun")) {
            if (player.getAccessLevel() < 2 && !PremiumService.isPremium(player)) {
                message(player, "Error! To use this command you must have donated at least "
                    + PremiumService.DONATION_THRESHOLD + " euros in the last month.");
            }
            else if (GloryService.getInstance().checkSlippery(player)) {
                message(player,
                    "Error! You can only use this command after you've used up your daily entries for Jotun Stronghold.");
            }
            else if (DAOManager.getDAO(MightDAO.class).getMight(player) < RESET_JOTUN_COST) {
                message(player, "Error! You need at least " + RESET_JOTUN_COST
                    + " Might to use this command.");
            }
            else {
                if (DAOManager.getDAO(PlayerDailiesDAO.class).resetPlayerCompletedDailies(player,
                    PvPMode.SLIPPERY)) {
                    DAOManager.getDAO(MightDAO.class).addMight(player, -RESET_JOTUN_COST);
                    message(player,
                        "You have successfully reset your daily entries for Jotun Stronghold!");
                }
                else {
                    message(player,
                        "An error occured while resetting your daily entries. No Might has been taken.");
                }
            }
        }
        else {
            HTMLService.showDonationSurvey(player);

            message(player, "Help: .donate <history | might2ap | might2gp | resetjotun>");
            message(player,
                "Help: History shows your donation history. Might2Ap converts might to AP at 1 to "
                    + MIGHT_TO_AP_RATIO + ". Might2GP converts might to GP at 1 to "
                    + MIGHT_TO_GP_RATIO + "."
                    + "\nHelp: resetjotun will reset your daily entries to Jotun Stronghold for "
                    + RESET_JOTUN_COST + " Might.");
            return;
        }

        lastExecute.put(player.getObjectId(), System.currentTimeMillis());
    }

    private void message(Player player, String msg) {
        PacketSendUtility.sendMessage(player, msg);
    }
}