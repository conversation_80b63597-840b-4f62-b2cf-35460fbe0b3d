/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.dao.BetDAO;
import gameserver.dao.PlayerDAO;
import gameserver.model.bet.Condition;
import gameserver.model.bet.Event;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.BetService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * <AUTHOR>
 * 
 */
public class Bet extends AdminCommand {

    private static final Logger log = Logger.getLogger(Bet.class);

    public Bet() {
        super("bet");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        final BetDAO dao = DAOManager.getDAO(BetDAO.class);
        if (admin.getAccessLevel() < AdminConfig.COMMAND_BET) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command!");
            return;
        }

        if (params == null || params.length < 1) {
            PacketSendUtility.sendMessage(admin, "Syntax: //bet <slip | event | checkAll>");
            return;
        }

        int mode;
        int cmd;

        if (params[0].compareTo("slip") == 0) {
            mode = 0;
        }
        else if (params[0].compareTo("event") == 0) {
            mode = 1;
        }
        else if (params[0].compareTo("checkAll") == 0) {
            BetService.getInstance().checkAll();
            PacketSendUtility.sendMessage(admin, "All bettingslips have been checked!");
            return;
        }
        else {
            PacketSendUtility.sendMessage(admin, "Syntax: //bet <slip | event | checkAll>");
            return;
        }

        if (params.length < 2) {
            if (mode == 0) {
                PacketSendUtility.sendMessage(admin,
                    "Syntax: //bet slip <add | delete | list | calculate>");
            }
            else {
                PacketSendUtility.sendMessage(admin,
                    "Syntax: //bet event <add | change | delete | list | calculate>");
            }
            return;
        }

        if (params[1].compareTo("add") == 0) {
            cmd = 0;
        }
        else if (params[1].compareTo("delete") == 0) {
            cmd = 1;
        }
        else if (params[1].compareTo("list") == 0) {
            cmd = 2;
        }
        else if (params[1].compareTo("calculate") == 0) {
            cmd = 3;
        }
        else if (params[1].compareTo("change") == 0 && mode == 1) {
            cmd = 4;
        }
        else {
            if (mode == 0) {
                PacketSendUtility.sendMessage(admin,
                    "Syntax: //bet slip <add | delete | list | calculate>");
            }
            else {
                PacketSendUtility.sendMessage(admin,
                    "Syntax: //bet event <add | change | delete | list | calculate>");
            }
            return;
        }

        switch (mode) {
            case 0:
                slip(cmd, admin, params);
                break;
            case 1:
                event(cmd, admin, params);
                break;
        }
    }

    private void slip(int cmd, Player admin, String[] params) {
        final BetDAO dao = DAOManager.getDAO(BetDAO.class);
        switch (cmd) {
            case 0:
                if (params.length < 8) {
                    PacketSendUtility
                        .sendMessage(admin,
                            "Syntax: //bet slip delete <playerid> <bet> <rate> <enddate> (<eventid> <value>)*");
                    return;
                }
                int lenght = (int) Math.floor((params.length - 6) / 2) * 2;
                int playerid;
                int bet_value;
                double rate;
                Date date;
                List<Condition> conditions = new ArrayList<Condition>();
                try {
                    playerid = Integer.parseInt(params[2]);
                    bet_value = Integer.parseInt(params[3]);
                    rate = Double.parseDouble(params[4]);
                    date = new Date(Long.parseLong(params[5]));
                }
                catch (NumberFormatException e) {
                    PacketSendUtility
                        .sendMessage(admin,
                            "Syntax: //bet slip add <playerid> <bet> <rate> <enddate> (<eventid> <value>)*");
                    return;
                }

                for (int i = 5; i < lenght + 5; i += 2) {
                    int event_id;
                    int value;
                    try {
                        event_id = Integer.parseInt(params[i + 1]);
                        value = Integer.parseInt(params[i + 2]);
                    }
                    catch (NumberFormatException e) {
                        PacketSendUtility
                            .sendMessage(admin,
                                "Syntax: //bet slip add <playerid> <bet> <rate> <enddate> (<eventid> <value>)*");
                        return;
                    }
                    Condition condition = new Condition(event_id, value);
                    conditions.add(condition);
                }
                int betid = dao.addBet(new gameserver.model.bet.Bet(playerid, bet_value, rate,
                    date, conditions));
                PacketSendUtility.sendMessage(admin, "Created bettingslip [" + betid
                    + "] for Player [" + playerid + "]");
                break;

            case 1:
                if (params.length < 3) {
                    PacketSendUtility.sendMessage(admin, "Syntax: //bet slip delete <id>");
                    return;
                }

                int id;
                try {
                    id = Integer.parseInt(params[2]);
                }
                catch (NumberFormatException e) {
                    PacketSendUtility.sendMessage(admin, "Syntax: //bet slip delete <id>");
                    return;
                }
                if (!dao.deleteBet(id)) {
                    PacketSendUtility.sendMessage(admin, "Error: Cant delete bettingslip!");
                    return;
                }
                PacketSendUtility.sendMessage(admin, "Deleted bettingslip [" + id + "]");
                break;

            case 2:
                if (params.length < 3) {
                    PacketSendUtility.sendMessage(admin,
                        "Syntax: //bet slip list <all | playername | playerid>");
                    return;
                }

                if (params[2].compareTo("all") == 0) {
                    PacketSendUtility.sendMessage(admin, "============================");
                    PacketSendUtility.sendMessage(admin, "ID | PLAYER | BET | RATE | ENDDATE");
                    PacketSendUtility.sendMessage(admin, "============================");
                    for (int i : dao.getBetIds()) {
                        gameserver.model.bet.Bet bet = dao.getBet(i);
                        String player_name = DAOManager.getDAO(PlayerDAO.class)
                            .getPlayerNameByObjId(bet.getOwnerObjectId());
                        PacketSendUtility.sendMessage(admin, String.format(
                            "%1$d | %2$s | %3$d | %4$.2f | %5$tF %5$tR", i, player_name,
                            bet.getValue(), bet.getRate(), bet.getEndDate()));
                    }
                    PacketSendUtility.sendMessage(admin, "============================");
                    return;
                }

                int player_id;
                try {
                    player_id = Integer.parseInt(params[2]);
                }
                catch (NumberFormatException e) {
                    player_id = -1;
                }
                if (player_id != -1) {
                    String name = DAOManager.getDAO(PlayerDAO.class)
                        .getPlayerNameByObjId(player_id);
                    log.info("VIA ID | Name=" + name);
                    PacketSendUtility.sendMessage(admin, "============================");
                    PacketSendUtility.sendMessage(admin, "ID | PLAYER | BET | RATE | ENDDATE");
                    PacketSendUtility.sendMessage(admin, "============================");
                    for (int i : dao.getBetIds()) {
                        gameserver.model.bet.Bet bet = dao.getBet(i);
                        String player_name = DAOManager.getDAO(PlayerDAO.class)
                            .getPlayerNameByObjId(bet.getOwnerObjectId());
                        if (player_name.equals(name))
                            PacketSendUtility.sendMessage(admin, String.format(
                                "%1$d | %2$s | %3$d | %4$.2f | %5$tF %5$tR", i, player_name,
                                bet.getValue(), bet.getRate(), bet.getEndDate()));
                    }
                    PacketSendUtility.sendMessage(admin, "============================");
                }
                else {
                    PacketSendUtility.sendMessage(admin, "============================");
                    PacketSendUtility.sendMessage(admin, "ID | PLAYER | BET | RATE | ENDDATE");
                    PacketSendUtility.sendMessage(admin, "============================");
                    for (int i : dao.getBetIds()) {
                        gameserver.model.bet.Bet bet = dao.getBet(i);
                        String player_name = DAOManager.getDAO(PlayerDAO.class)
                            .getPlayerNameByObjId(bet.getOwnerObjectId());
                        if (player_name.equals(params[2]))
                            PacketSendUtility.sendMessage(admin, String.format(
                                "%1$d | %2$s | %3$d | %4$.2f | %5$tF %5$tR", i, player_name,
                                bet.getValue(), bet.getRate(), bet.getEndDate()));
                    }
                    PacketSendUtility.sendMessage(admin, "============================");
                }
                break;

            case 3:
                if (params.length < 3) {
                    PacketSendUtility.sendMessage(admin, "Syntax: //bet slip calculate <id>");
                    return;
                }
                int slip_id;
                try {
                    slip_id = Integer.parseInt(params[2]);
                }
                catch (NumberFormatException e) {
                    slip_id = -1;
                }
                if (slip_id == -1) {
                    PacketSendUtility.sendMessage(admin, "Syntax: //bet slip calculate <id>");
                    return;
                }
                gameserver.model.bet.Bet bet = dao.getBet(slip_id);
                if (bet == null) {
                    PacketSendUtility.sendMessage(admin, "Error: Cant get bettingslip!");
                    return;
                }
                PacketSendUtility.sendMessage(admin,
                    "The calculated rate (based on the current bets) is: "
                        + BetService.getInstance().calculateRate(bet));
                break;
        }
    }

    private void event(int cmd, Player admin, String[] params) {
        final BetDAO dao = DAOManager.getDAO(BetDAO.class);
        switch (cmd) {
            case 0:
                String groupname = "GLOBAL";
                if (params.length < 3) {
                    PacketSendUtility.sendMessage(admin,
                        "Syntax: //bet event add <name> [groupname]");
                    return;
                }
                String name = params[2];
                if (params.length >= 4)
                    groupname = params[3];
                int id = dao.addEvent(new Event(name, groupname, -1));
                if (id == -1) {
                    PacketSendUtility.sendMessage(admin, "Error: Cant create event!");
                    return;
                }
                PacketSendUtility.sendMessage(admin, "Created event " + name + "[" + id + "]");
                break;
            case 1:
                if (params.length < 3) {
                    PacketSendUtility.sendMessage(admin, "Syntax: //bet event delete <id>");
                    return;
                }
                int event_id;
                try {
                    event_id = Integer.parseInt(params[2]);
                }
                catch (NumberFormatException e) {
                    event_id = -1;
                }
                if (event_id == -1) {
                    PacketSendUtility.sendMessage(admin, "Syntax: //bet event delete <id>");
                    return;
                }

                if (!dao.deleteEvent(event_id)) {
                    PacketSendUtility.sendMessage(admin, "Error: Cant delete event!");
                    return;
                }
                PacketSendUtility.sendMessage(admin, "Deleted event [" + event_id + "]");
                break;
            case 2:
                if (params.length < 3) {
                    PacketSendUtility.sendMessage(admin,
                        "Syntax: //bet event list <all | groupname>");
                    return;
                }
                if (params[2].compareTo("all") == 0) {
                    PacketSendUtility.sendMessage(admin, "============================");
                    PacketSendUtility.sendMessage(admin, "ID | NAME | GROUPNAME | VALUE ");
                    PacketSendUtility.sendMessage(admin, "============================");
                    for (int i : dao.getEventIds()) {
                        Event event = dao.getEvent(i);
                        PacketSendUtility.sendMessage(
                            admin,
                            String.format("%1$d | %2$s | %3$s | %4$d ", i, event.getName(),
                                event.getGroupname(), event.getValue()));
                    }
                    PacketSendUtility.sendMessage(admin, "============================");
                }
                else {
                    PacketSendUtility.sendMessage(admin, "============================");
                    PacketSendUtility.sendMessage(admin, "ID | NAME | GROUPNAME | VALUE ");
                    PacketSendUtility.sendMessage(admin, "============================");
                    for (int i : dao.getEventIds()) {
                        Event event = dao.getEvent(i);
                        if (params[2].compareTo(event.getGroupname()) == 0)
                            PacketSendUtility.sendMessage(admin, String.format(
                                "%1$d | %2$s | %3$s | %4$d ", i, event.getName(),
                                event.getGroupname(), event.getValue()));
                    }
                    PacketSendUtility.sendMessage(admin, "============================");
                }
                break;
            case 3:
                if (params.length < 4) {
                    PacketSendUtility.sendMessage(admin,
                        "Syntax: //bet event calculate <id> <value>");
                    return;
                }
                int event_id_cal;
                int value;
                try {
                    event_id_cal = Integer.parseInt(params[2]);
                    value = Integer.parseInt(params[3]);
                }
                catch (NumberFormatException e) {
                    PacketSendUtility.sendMessage(admin,
                        "Syntax: //bet event calculate <id> <value>");
                    return;
                }
                PacketSendUtility.sendMessage(admin,
                    "The calculated rate (based on the current bets) is: "
                        + BetService.getInstance().calculateRateForEvent(event_id_cal, value));
                break;
            case 4:
                if (params.length < 4) {
                    PacketSendUtility.sendMessage(admin, "Syntax: //bet event change <id> <value>");
                    return;
                }
                int event_id_change;
                int value_change;
                try {
                    event_id_change = Integer.parseInt(params[2]);
                    value_change = Integer.parseInt(params[3]);
                }
                catch (NumberFormatException e) {
                    PacketSendUtility.sendMessage(admin, "Syntax: //bet event change <id> <value>");
                    return;
                }
                Event event_change = dao.getEvent(event_id_change);
                if (event_change == null) {
                    PacketSendUtility.sendMessage(admin, "Syntax: //bet event change <id> <value>");
                    return;
                }
                Event new_event = new Event(event_change.getName(), event_change.getGroupname(),
                    value_change);
                dao.changeEvent(new_event, event_id_change);
                PacketSendUtility.sendMessage(admin, "The value of event [" + event_id_change
                    + "] has been changed to " + value_change);
                break;
        }
    }
}
