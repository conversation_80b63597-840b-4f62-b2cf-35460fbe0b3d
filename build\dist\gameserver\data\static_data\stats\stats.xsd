<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!--
		This file is part of Aion X Emu <aionxemu.com>.

		This is free software: you can redistribute it and/or modify
		it under the terms of the GNU Lesser Public License as published by
		the Free Software Foundation, either version 3 of the License, or
		(at your option) any later version.

		This software is distributed in the hope that it will be useful,
		but WITHOUT ANY WARRANTY; without even the implied warranty of
		MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
		GNU Lesser Public License for more details.

		You should have received a copy of the GNU Lesser Public License
		along with this software.  If not, see <http://www.gnu.org/licenses/>.
	-->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" version="1.0">
	<xs:include schemaLocation="../global_types.xsd"/>
	<xs:include schemaLocation="../import.xsd"/>
	<xs:element name="player_stats_templates">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="import" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="player_stats" type="playerStatsTemplateType" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="summon_stats_templates">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="import" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="summon_stats" type="summonStatsTemplateType" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="playerStatsTemplateType">
		<xs:sequence>
			<xs:element name="stats_template" type="playerStatsTemplate" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="class" type="playerClass" use="required"/>
		<xs:attribute name="level" type="xs:byte" use="required"/>
	</xs:complexType>
	<xs:complexType name="playerStatsTemplate">
		<xs:complexContent>
			<xs:extension base="statsTemplate">
				<xs:attribute name="power" type="xs:int" use="required"/>
				<xs:attribute name="health" type="xs:int" use="required"/>
				<xs:attribute name="agility" type="xs:int" use="required"/>
				<xs:attribute name="accuracy" type="xs:int" use="required"/>
				<xs:attribute name="knowledge" type="xs:int" use="required"/>
				<xs:attribute name="will" type="xs:int" use="required"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="summonStatsTemplateType">
		<xs:sequence>
			<xs:element name="stats_template" type="summonStatsTemplate" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="npc_id_light" type="xs:int"/>
		<xs:attribute name="npc_id_dark" type="xs:int"/>
		<xs:attribute name="level" type="xs:byte" use="required"/>
	</xs:complexType>
	<xs:complexType name="summonStatsTemplate">
		<xs:complexContent>
			<xs:extension base="statsTemplate">
				<xs:attribute name="pdefense" type="xs:int" use="required"/>
				<xs:attribute name="mresist" type="xs:int" use="required"/>
				<xs:attribute name="mcrit" type="xs:int"/>
				<xs:attribute name="mboost" type="xs:int"/>
				<xs:attribute name="mdamage" type="xs:boolean"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<xs:complexType name="statsTemplate" abstract="true">
		<xs:attribute name="maxHp" type="xs:int" use="required"/>
		<xs:attribute name="maxMp" type="xs:int"/>
		<xs:attribute name="walk_speed" type="xs:float"/>
		<xs:attribute name="run_speed" type="xs:float"/>
		<xs:attribute name="fly_speed" type="xs:float"/>
		<xs:attribute name="attack_speed" type="xs:float"/>
		<xs:attribute name="evasion" type="xs:int"/>
		<xs:attribute name="block" type="xs:int"/>
		<xs:attribute name="parry" type="xs:int"/>
		<xs:attribute name="main_hand_attack" type="xs:int"/>
		<xs:attribute name="main_hand_accuracy" type="xs:int"/>
		<xs:attribute name="main_hand_crit_rate" type="xs:int"/>
		<xs:attribute name="magic_accuracy" type="xs:int"/>
		<xs:attribute name="boost_heal" type="xs:int"/>
	</xs:complexType>
</xs:schema>
