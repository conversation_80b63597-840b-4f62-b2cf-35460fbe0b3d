/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package usercommands;

import gameserver.controllers.movement.ActionObserver;
import gameserver.controllers.movement.ActionObserver.ObserverType;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.ArenaService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.utils.chathandlers.UserCommand;

import java.util.Map;
import java.util.concurrent.ScheduledFuture;

import javolution.util.FastMap;

/**
 * 
 * <AUTHOR>
 */
public class SuicideCommand extends UserCommand {
    private static Map<Integer, Long> lastExecute = new FastMap<Integer, Long>();

    public SuicideCommand() {
        super("suicide");
    }

    public void executeCommand(final Player player, String param) {
        String[] params = param.split(" ");

        if (player.getBattleground() != null || ArenaService.getInstance().isInArena(player)) {
            PacketSendUtility
                .sendMessage(player, "You cannot use this command while in BG or FFA!");
            return;
        }
        else if (player.isInCombatLong()) {
            PacketSendUtility.sendMessage(player, "You cannot use this command while in combat!");
            return;
        }
        else if (lastExecute.containsKey(player.getObjectId())) {
            if ((System.currentTimeMillis() - lastExecute.get(player.getObjectId())) < 20000) {
                PacketSendUtility.sendMessage(player,
                    "You cannot use this command more than every 20 seconds!");
                return;
            }
        }

        PacketSendUtility.sendMessage(player,
            "You will suicide in 10 seconds if you don't move or get attacked.");

        final ScheduledFuture<?> task = ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                PacketSendUtility.sendMessage(player, "You have committed suicide!");
                player.getController().die();
            }
        }, 10000);

        player.getObserveController().attach(new ActionObserver(ObserverType.MOVE) {
            @Override
            public void moved() {
                if (!task.isDone()) {
                    PacketSendUtility.sendMessage(player, "You have cancelled the suicide.");
                    task.cancel(false);
                }
            }
        });

        player.getObserveController().attach(new ActionObserver(ObserverType.ATTACKED) {
            @Override
            public void attacked(Creature creature) {
                if (!task.isDone()) {
                    PacketSendUtility.sendMessage(player, "You have cancelled the suicide.");
                    task.cancel(false);
                }
            }
        });

        lastExecute.put(player.getObjectId(), System.currentTimeMillis());
    }
}