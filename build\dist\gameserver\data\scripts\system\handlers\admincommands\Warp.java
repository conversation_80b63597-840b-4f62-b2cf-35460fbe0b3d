/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.geoEngine2.GeoEngine2;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.TeleportService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;

/**
 * <AUTHOR>
 * 
 */
public class Warp extends AdminCommand {

    public Warp() {
        super("warp");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.GM_LEVEL) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command.");
            return;
        }

        if (params.length < 1) {
            PacketSendUtility
                .sendMessage(admin,
                    "Syntax: //warp <location link> - remember to put link in \"quotation\" signs.");
            return;
        }
        else if (!params[0].startsWith("[pos:")) {
            PacketSendUtility.sendMessage(admin,
                "Please specify a proper location link in quotation signs.");
            return;
        }

        int worldId;
        float x, y, z;

        try {
            String loc = params[0].substring(3 + params[0].indexOf(';'));
            String[] l = loc.split(" ");
            worldId = Integer.parseInt(l[0]);
            x = Float.parseFloat(l[1]);
            y = Float.parseFloat(l[2]);
            z = GeoEngine2.getInstance().getHighestZ(worldId, x, y);
        }
        catch (Exception e) {
            PacketSendUtility.sendMessage(admin, "Failed parsing your specified location link.");
            return;
        }

        TeleportService.teleportTo(admin, worldId, x, y, z, 0);
        PacketSendUtility.sendMessage(admin, "Teleported to " + x + " " + y + " " + z + " ["
            + worldId + "]");
    }
}
