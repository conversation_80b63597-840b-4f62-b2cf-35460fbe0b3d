/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.configs.network.NetworkConfig;
import gameserver.dao.ShopDAO;
import gameserver.dao.ShopDAO.DonationEntry;
import gameserver.model.gameobjects.player.Player;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.World;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.List;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * <AUTHOR>
 */
public class DonateCommand extends AdminCommand {

    public DonateCommand() {
        super("donate");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_ADMIN) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command!");
            return;
        }

        if ((admin.getTarget() == null || !(admin.getTarget() instanceof Player))
            && params.length < 1) {
            PacketSendUtility.sendMessage(admin,
                "Syntax: //donate [name] -- either target a player or specify name!");
            return;
        }

        Player target = null;
        if (params.length >= 1)
            target = World.getInstance().findPlayer(Util.convertName(params[0]));
        else if (admin.getTarget() != null && admin.getTarget() instanceof Player)
            target = (Player) admin.getTarget();

        if (target == null) {
            PacketSendUtility.sendMessage(admin,
                "No player with that name online or something went wrong!");
            return;
        }

        List<DonationEntry> donations = DAOManager.getDAO(ShopDAO.class).getDonationEntries(target);

        if (donations == null || donations.size() == 0) {
            PacketSendUtility.sendMessage(admin, "No donation records of " + target.getName()
                + "'s account were found!");
        }
        else {
            float total = DAOManager.getDAO(ShopDAO.class).getDonationTotal(target);

            String msg = "--- DONATION HISTORY ---\n";

            DateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");

            for (DonationEntry donation : donations) {
                msg += "[" + formatter.format(donation.getTimeStamp()) + "] => "
                    + donation.getAmount() / (NetworkConfig.GAMESERVER_ID == 24 ? 200 : 100)
                    + " Euro\n";
            }

            msg += "\nTotal: " + total + " euros\n";
            msg += "Account name: " + target.getAccountName() + "\n";

            msg += "--- END OF HISTORY ---";

            PacketSendUtility.sendMessage(admin, msg);
        }
    }
}
