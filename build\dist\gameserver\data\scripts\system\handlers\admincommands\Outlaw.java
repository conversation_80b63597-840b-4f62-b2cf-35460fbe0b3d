/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.OutlawService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.Executor;
import gameserver.world.World;
import gnu.trove.TIntIntHashMap;
import gnu.trove.TIntIntIterator;

/**
 * <AUTHOR>
 */
public class Outlaw extends AdminCommand {
    public Outlaw() {
        super("outlaw");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.COMMAND_OUTLAW) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command");
            return;
        }

        TIntIntHashMap zergMeters = OutlawService.getInstance().getZergMeters();

        if (params.length == 1 && params[0].equalsIgnoreCase("map")) {
            admin.getWorldMapInstance().doOnAllPlayers(new Executor<Player>() {
                @Override
                public boolean run(Player pl) {
                    OutlawService.getInstance().makeOutlaw(pl);

                    return true;
                }
            }, true);
        }
        else if (params.length > 0) {
            Player player = World.getInstance().findPlayer(Util.convertName(params[0]));
            if (player == null) {
                PacketSendUtility.sendMessage(admin, "Couldn't find player " + params[0] + "!");
                return;
            }

            OutlawService.getInstance().makeOutlaw(player);
        }
        else if (zergMeters.size() == 0) {
            PacketSendUtility.sendMessage(admin, "The zerg meters are completely empty!");
            return;
        }
        else {
            StringBuilder sb = new StringBuilder();

            sb.append("==== ZERG METERS ====\n");

            for (TIntIntIterator it = zergMeters.iterator(); it.hasNext();) {
                it.advance();
                Player pl = World.getInstance().findPlayer(it.key());
                if (pl == null)
                    continue;

                sb.append(pl.getName() + ": " + it.value() + " points.\n");
            }

            sb.append("==== END OF METERS ====");

            PacketSendUtility.sendMessage(admin, sb.toString());
        }
    }
}
