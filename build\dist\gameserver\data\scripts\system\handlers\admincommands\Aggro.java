/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.controllers.attack.AggroInfo;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.chathandlers.AdminCommand;

/**
 * <AUTHOR>
 * 
 */
public class Aggro extends AdminCommand {
    public Aggro() {
        super("aggro");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.GM_LEVEL) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command!");
            return;
        }

        if (!(admin.getTarget() instanceof Creature)) {
            PacketSendUtility.sendMessage(admin, "Please select a proper target.");
            return;
        }

        Creature target = (Creature) admin.getTarget();

        StringBuilder sb = new StringBuilder();

        sb.append("--- AGGRO LIST ---\n");

        for (AggroInfo ai : target.getAggroList().getFinalDamageList(false)) {
            if (ai.getAttacker() instanceof Player) {
                sb.append(((Player) ai.getAttacker()).getName() + ": " + ai.getDamage() + " ("
                    + ai.getHate() + ") [" + (System.currentTimeMillis() - ai.getTime()) + "]\n");
            }
            else if (ai.getAttacker() instanceof Creature) {
                sb.append(((Creature) ai.getAttacker()).getName() + ": " + ai.getDamage() + " ("
                    + ai.getHate() + ") [" + (System.currentTimeMillis() - ai.getTime()) + "]\n");
            }
        }

        Creature hated = target.getAggroList().getMostHated();
        if (hated != null) {
            AggroInfo ai = target.getAggroList().getAggroInfo(hated);
            sb.append("Most hated: " + hated.getName() + ": " + ai.getDamage() + " ("
                + ai.getHate() + ") [" + (System.currentTimeMillis() - ai.getTime()) + "]\n");
        }

        sb.append("--- END AGGRO LIST ---");

        PacketSendUtility.sendMessage(admin, sb.toString());
    }
}
