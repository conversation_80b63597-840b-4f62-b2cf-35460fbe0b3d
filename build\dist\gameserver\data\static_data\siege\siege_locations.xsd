<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" version="1.0">

	<xs:include schemaLocation="../import.xsd"/>

	<xs:element name="siege_locations">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="import" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="siege_location" type="SiegeLocation" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>

	<xs:complexType name="SiegeReward">
		<xs:attribute name="grade" type="xs:int" use="required" />
		<xs:attribute name="top" type="xs:int" use="required" />
		<xs:attribute name="itemid" type="xs:int" use="required" />
		<xs:attribute name="count" type="xs:int" use="required" />
	</xs:complexType>

	<xs:complexType name="DefenseReward">
		<xs:attribute name="gold" type="xs:int" use="required" />
		<xs:attribute name="silver" type="xs:int" use="required" />
	</xs:complexType>

	<xs:complexType name="SiegeLocation">
		<xs:sequence>
			<xs:element name="siege_reward" type="SiegeReward" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="defense_reward" type="DefenseReward" minOccurs="0" maxOccurs="1" />
		</xs:sequence>
		<xs:attribute name="id" type="xs:int" use="required"/>
		<xs:attribute name="type" type="SiegeType" use="required"/>
		<xs:attribute name="world" type="xs:int" use="required"/>
		<xs:attribute name="influence_value" type="xs:int" default="1" />
		<xs:attribute name="artifact_cooldown" type="xs:int"/>
		<xs:attribute name="manor_id" type="xs:int"/>
		<xs:attribute name="manor_race" type="Race"/>
	</xs:complexType>

	<xs:simpleType name="SiegeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="FORTRESS" />
			<xs:enumeration value="ARTIFACT" />
			<xs:enumeration value="BOSSRAID_LIGHT" />
			<xs:enumeration value="BOSSRAID_DARK" />
			<xs:enumeration value="INDUN" />
			<xs:enumeration value="UNDERPASS" />
			<xs:enumeration value="OUTPOST" />
			<xs:enumeration value="MANOR" />
		</xs:restriction>
	</xs:simpleType>
</xs:schema>