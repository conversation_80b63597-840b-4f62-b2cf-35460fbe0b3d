<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:include schemaLocation="../import.xsd"/>
    <xs:element name="safezones">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="import" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element name="safezone" type="SafeZoneTemplate" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="SafeZoneTemplate">
		<xs:sequence>
			<xs:element name="point" type="PointTemplate" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="mapid" type="xs:int" use="required"/>
		<xs:attribute name="bottom" type="xs:float" use="required"/>
		<xs:attribute name="top" type="xs:float" use="required"/>
	</xs:complexType>
	<xs:complexType name="PointTemplate">
		<xs:attribute name="x" type="xs:float" use="required"/>
		<xs:attribute name="y" type="xs:float" use="required"/>
	</xs:complexType>
</xs:schema>