<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:include schemaLocation="../import.xsd"/>

    <xs:element name="cube_expander" type="cubeExpander"/>

    <xs:complexType name="cubeExpander">
        <xs:sequence>
            <xs:element ref="import" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="cube_npc" type="cubeNpcTemplate" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="cubeNpcTemplate">
        <xs:sequence>
            <xs:element name="expand" type="CubeExpand" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
        <xs:attribute name="id" type="xs:int" use="required"/>
        <xs:attribute name="name" type="xs:string" use="required"/>
    </xs:complexType>

    <xs:complexType name="CubeExpand">
        <xs:attribute name="level" type="xs:int"/>
        <xs:attribute name="price" type="xs:int"/>
    </xs:complexType>
</xs:schema>
